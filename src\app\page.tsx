
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import type { Category, SubCategory, BudgetData, FinancialGoal } from '@/lib/types';
import ModernLayout from '@/components/layout/ModernLayout';
import { QuickStatsGrid } from '@/components/dashboard/ModernMetricCard';
import CategoryManager from '@/components/budget/CategoryManager';
import BudgetVisualizer from '@/components/budget/BudgetVisualizer';
import FinancialGoalManager from '@/components/budget/FinancialGoalManager';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from '@/context/AuthContext';
import { WalletCards, Target, Trophy, Plus, TrendingUp, Pie<PERSON><PERSON>, BarChart3 } from 'lucide-react';
import { AnimatedCard } from '@/components/ui/animated-wrapper';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FloatingActionButton from '@/components/ui/floating-action-button';

const ACHIEVEMENT_IDS = {
  BUDGET_STARTER: 'BUDGET_STARTER',
  GOAL_SETTER: 'GOAL_SETTER',
  GOAL_CRUSHER: 'GOAL_CRUSHER',
};

const ACHIEVEMENT_DETAILS: Record<string, { title: string; description: string; IconComponent: React.ElementType }> = {
  [ACHIEVEMENT_IDS.BUDGET_STARTER]: { title: "Budget Starter!", description: "You've set your income and added your first category!", IconComponent: WalletCards },
  [ACHIEVEMENT_IDS.GOAL_SETTER]: { title: "Goal Setter!", description: "You've set your first financial goal!", IconComponent: Target },
  [ACHIEVEMENT_IDS.GOAL_CRUSHER]: { title: "Goal Crusher!", description: "Congratulations! You've achieved your financial goal!", IconComponent: Trophy },
};


export default function BudgetPage() {
  const { currentUser, loading: authLoading } = useAuth();
  const router = useRouter();

  const [isMounted, setIsMounted] = useState(false);
  const [totalIncome, setTotalIncome] = useState<number>(0);
  const [categories, setCategories] = useState<Category[]>([]);
  const [financialGoal, setFinancialGoal] = useState<FinancialGoal | null>(null);
  const [achievements, setAchievements] = useState<string[]>([]);
  const [balancesVisible, setBalancesVisible] = useState<boolean>(true);
  const { toast } = useToast();

  const getLocalStorageKey = useCallback(() => {
    if (currentUser) {
      return `budgetWiseData_${currentUser.uid}`;
    }
    return null; // Or a default key if you want non-authenticated users to have some data (not recommended for this app)
  }, [currentUser]);


  const awardAchievement = useCallback((achievementId: string) => {
    if (!achievements.includes(achievementId)) {
      setAchievements(prev => [...prev, achievementId]);
      const details = ACHIEVEMENT_DETAILS[achievementId];
      if (details) {
        toast({
          title: `🏆 ${details.title}`,
          description: details.description,
        });
      }
    }
  }, [achievements, toast]);

  useEffect(() => {
    setIsMounted(true); // Component is mounted on client
  }, []);

  useEffect(() => {
    if (authLoading) return; // Wait for auth state to be determined

    if (!currentUser) {
      router.push('/login');
      return;
    }

    const storageKey = getLocalStorageKey();
    if (!storageKey) return; // Should not happen if currentUser is present

    if (isMounted) { // Ensure component is client-side mounted before accessing localStorage
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        try {
          const parsedData: BudgetData = JSON.parse(storedData);
          setTotalIncome(parsedData.totalIncome || 0);
          setCategories(
            (parsedData.categories || []).map(cat => ({
              ...cat,
              isVisible: cat.isVisible !== undefined ? cat.isVisible : true,
              subCategories: cat.subCategories || [],
            }))
          );
          setFinancialGoal(parsedData.financialGoal || null);
          setAchievements(parsedData.achievements || []);
          setBalancesVisible(parsedData.balancesVisible !== undefined ? parsedData.balancesVisible : true);
        } catch (error) {
          console.error("Failed to parse budget data from localStorage", error);
          // Reset to defaults if parsing fails
          setTotalIncome(0);
          setCategories([]);
          setFinancialGoal(null);
          setAchievements([]);
          setBalancesVisible(true);
        }
      } else {
        // No data found, initialize with defaults
        setTotalIncome(0);
        setCategories([]);
        setFinancialGoal(null);
        setAchievements([]);
        setBalancesVisible(true);
      }
    }
  }, [currentUser, authLoading, router, isMounted, getLocalStorageKey]);

  useEffect(() => {
    const storageKey = getLocalStorageKey();
    if (!storageKey || !isMounted || authLoading || !currentUser) return; // Only save if user is logged in and component is mounted

    const budgetData: BudgetData = { totalIncome, categories, financialGoal, achievements, balancesVisible };
    localStorage.setItem(storageKey, JSON.stringify(budgetData));

    if (totalIncome > 0 && categories.length > 0) {
      awardAchievement(ACHIEVEMENT_IDS.BUDGET_STARTER);
    }
    if (financialGoal?.dateAchieved) {
      awardAchievement(ACHIEVEMENT_IDS.GOAL_CRUSHER);
    }
  }, [totalIncome, categories, financialGoal, achievements, balancesVisible, isMounted, awardAchievement, getLocalStorageKey, authLoading, currentUser]);

  const toggleBalancesVisibility = useCallback(() => {
    setBalancesVisible(prev => !prev);
  }, []);

  const toggleCategoryVisibility = useCallback((categoryId: string) => {
    setCategories(prevCategories =>
      prevCategories.map(cat =>
        cat.id === categoryId ? { ...cat, isVisible: !(cat.isVisible ?? true) } : cat
      )
    );
  }, []);



  const addCategory = useCallback((name: string, budget: number) => {
    const newCategory: Category = {
      id: crypto.randomUUID(),
      name,
      budget,
      subCategories: [],
      isVisible: true,
    };
    setCategories((prev) => [...prev, newCategory]);
  }, []);

  const updateCategory = useCallback((id: string, newName: string, newBudget: number) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === id ? { ...cat, name: newName, budget: newBudget } : cat
      )
    );
  }, []);

  const deleteCategory = useCallback((id: string) => {
    setCategories((prev) => prev.filter((cat) => cat.id !== id));
  }, []);

  const addSubCategory = useCallback((parentId: string, name: string, allocatedAmount: number): boolean => {
    let success = false;
    setCategories((prev) =>
      prev.map((cat) => {
        if (cat.id === parentId) {
          const currentSubTotal = cat.subCategories.reduce((sum, sc) => sum + sc.allocatedAmount, 0);
          if (currentSubTotal + allocatedAmount > cat.budget) {
            success = false;
            return cat;
          }
          const newSubCategory: SubCategory = {
            id: crypto.randomUUID(),
            name,
            allocatedAmount,
          };
          success = true;
          return { ...cat, subCategories: [...cat.subCategories, newSubCategory] };
        }
        return cat;
      })
    );
    return success;
  }, []);

  const updateSubCategory = useCallback((parentId: string, subId: string, newName: string, newAllocatedAmount: number): boolean => {
    let success = false;
    setCategories((prev) =>
      prev.map((cat) => {
        if (cat.id === parentId) {
          const otherSubCategoriesTotal = cat.subCategories
            .filter(sc => sc.id !== subId)
            .reduce((sum, sc) => sum + sc.allocatedAmount, 0);
          
          if (otherSubCategoriesTotal + newAllocatedAmount > cat.budget) {
            success = false;
            return cat;
          }
          success = true;
          return {
            ...cat,
            subCategories: cat.subCategories.map((sc) =>
              sc.id === subId ? { ...sc, name: newName, allocatedAmount: newAllocatedAmount } : sc
            ),
          };
        }
        return cat;
      })
    );
    return success;
  }, []);

  const deleteSubCategory = useCallback((parentId: string, subId: string) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === parentId
          ? { ...cat, subCategories: cat.subCategories.filter((sc) => sc.id !== subId) }
          : cat
      )
    );
    toast({ title: "Subcategory Deleted", description: "Subcategory has been removed."});
  }, [toast]);

  const handleSetFinancialGoal = useCallback((name: string, targetAmount: number, icon?: string) => {
    const newGoal: FinancialGoal = {
      id: financialGoal?.id || crypto.randomUUID(),
      name,
      targetAmount,
      savedAmount: financialGoal?.id ? financialGoal.savedAmount : 0,
      icon,
      dateSet: financialGoal?.dateSet || new Date().toISOString(),
      dateAchieved: null,
    };
    setFinancialGoal(newGoal);
    if (!achievements.includes(ACHIEVEMENT_IDS.GOAL_SETTER) || !financialGoal) {
        awardAchievement(ACHIEVEMENT_IDS.GOAL_SETTER);
    }
    toast({ title: "Financial Goal Updated!", description: `Your goal "${name}" has been set/updated.`});
  }, [financialGoal, awardAchievement, toast, achievements]);

  const handleUpdateGoalProgress = useCallback((savedAmount: number) => {
    if (financialGoal) {
      const updatedGoal = { ...financialGoal, savedAmount };
      if (savedAmount >= financialGoal.targetAmount && !financialGoal.dateAchieved) {
        updatedGoal.dateAchieved = new Date().toISOString();
        awardAchievement(ACHIEVEMENT_IDS.GOAL_CRUSHER);
        toast({ title: "Goal Achieved!", description: `Congratulations on reaching your goal: ${financialGoal.name}!`, duration: 5000 });
      }
      setFinancialGoal(updatedGoal);
    }
  }, [financialGoal, awardAchievement, toast]);
  
  const handleClearGoal = useCallback(() => {
    setFinancialGoal(null);
    toast({ title: "Financial Goal Cleared", description: "Your financial goal has been removed."});
  }, [toast]);

  const overallTotalAllocated = categories.reduce((sum, cat) => sum + cat.budget, 0);
  const overallRemaining = totalIncome - overallTotalAllocated;

  if (authLoading || !isMounted || !currentUser) {
    return (
      <ModernLayout
        balancesVisible={balancesVisible}
        onToggleBalances={toggleBalancesVisibility}
      >
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center space-y-4">
            <motion.div
              className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <p className="text-muted-foreground">Loading your budget dashboard...</p>
          </div>
        </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout
      balancesVisible={balancesVisible}
      onToggleBalances={toggleBalancesVisibility}
    >
      <div className="space-y-8">
        {/* Page Header */}
        <motion.div
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back! Here's your financial overview.
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button variant="outline" className="glass-card border-white/20">
              <BarChart3 className="w-4 h-4 mr-2" />
              View Reports
            </Button>
            <Button className="btn-modern-primary">
              <Plus className="w-4 h-4 mr-2" />
              Quick Add
            </Button>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <QuickStatsGrid
          totalIncome={totalIncome}
          totalAllocated={overallTotalAllocated}
          remaining={overallRemaining}
          savingsGoal={financialGoal?.targetAmount}
          balancesVisible={balancesVisible}
        />

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Charts and Visualizations */}
          <div className="xl:col-span-2 space-y-6">
            {/* Budget Visualizer */}
            <AnimatedCard delay={0.2} className="chart-container">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center">
                    <PieChart className="w-5 h-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Budget Overview</h3>
                    <p className="text-sm text-muted-foreground">Category allocation breakdown</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="glass-card">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Analyze
                </Button>
              </div>
              <BudgetVisualizer
                totalIncome={totalIncome}
                categories={categories}
                balancesVisible={balancesVisible}
              />
            </AnimatedCard>

            {/* Category Manager */}
            <AnimatedCard delay={0.3} className="glass-card">
              <CategoryManager
                categories={categories}
                onAddCategory={addCategory}
                onUpdateCategory={updateCategory}
                onDeleteCategory={deleteCategory}
                onAddSubCategory={addSubCategory}
                onUpdateSubCategory={updateSubCategory}
                onDeleteSubCategory={deleteSubCategory}
                onToggleCategoryVisibility={toggleCategoryVisibility}
                balancesVisible={balancesVisible}
              />
            </AnimatedCard>
          </div>

          {/* Right Column - Goals and Quick Actions */}
          <div className="space-y-6">
            {/* Financial Goals */}
            <AnimatedCard delay={0.4} className="glass-card">
              <FinancialGoalManager
                goal={financialGoal}
                onSetGoal={handleSetFinancialGoal}
                onUpdateProgress={handleUpdateGoalProgress}
                onClearGoal={handleClearGoal}
                overallRemaining={overallRemaining}
                balancesVisible={balancesVisible}
              />
            </AnimatedCard>

            {/* Quick Actions */}
            <AnimatedCard delay={0.5} className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-primary" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start glass-card border-white/20">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Income Source
                </Button>
                <Button variant="outline" className="w-full justify-start glass-card border-white/20">
                  <Target className="w-4 h-4 mr-2" />
                  Set New Goal
                </Button>
                <Button variant="outline" className="w-full justify-start glass-card border-white/20">
                  <Trophy className="w-4 h-4 mr-2" />
                  View Achievements
                </Button>
              </CardContent>
            </AnimatedCard>

            {/* Recent Activity */}
            <AnimatedCard delay={0.6} className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-white/5">
                    <div className="w-2 h-2 bg-success rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">Budget updated</p>
                      <p className="text-xs text-muted-foreground">2 minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-white/5">
                    <div className="w-2 h-2 bg-primary rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">New category added</p>
                      <p className="text-xs text-muted-foreground">1 hour ago</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-white/5">
                    <div className="w-2 h-2 bg-accent rounded-full" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">Goal achieved</p>
                      <p className="text-xs text-muted-foreground">Yesterday</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </AnimatedCard>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton />
    </ModernLayout>
  );
}
