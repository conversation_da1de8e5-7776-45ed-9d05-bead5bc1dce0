{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bo+sQ9ioaiyiLUwLjzd1udTV8v8fmaLeb82li3vq6m4=", "__NEXT_PREVIEW_MODE_ID": "efa94529c2b57640d5e8d28122a5503e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ad6a570e2a8e4a1a45550da9c5ecffb4f1caca54ce14761dc74d8983330c56e1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a802c2ff4557c498651caa751d1cb50f865626d36fc1fd0ec09673c713d79568"}}}, "sortedMiddleware": ["/"], "functions": {}}