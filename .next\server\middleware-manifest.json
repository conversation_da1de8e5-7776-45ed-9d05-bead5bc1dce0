{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "McYjuN2C2CRIVhmi0oSgm", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bo+sQ9ioaiyiLUwLjzd1udTV8v8fmaLeb82li3vq6m4=", "__NEXT_PREVIEW_MODE_ID": "3ff4c8e4540176e5d73a864ff0b111ee", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "18ba0864f61d0d0805f7475426b4e18843dc16152ee22da2e7fdbf228e6f3bfc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c3608ac96a54b150349f554aa8a193894b8c1cd002b9caefbb27aa44e867e4ec"}}}, "functions": {}, "sortedMiddleware": ["/"]}