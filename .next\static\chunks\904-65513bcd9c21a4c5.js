"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[904],{2564:(e,t,r)=>{r.d(t,{s:()=>s});var n=r(12115),o=r(63655),a=r(95155),s=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));s.displayName="VisuallyHidden"},25318:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},26621:(e,t,r)=>{r.d(t,{Kq:()=>Z,LM:()=>z,VY:()=>ee,bL:()=>J,bm:()=>er,hE:()=>Q,rc:()=>et});var n=r(12115),o=r(47650),a=r(85185),s=r(6101),i=r(82284),l=r(46081),c=r(19178),d=r(34378),u=r(28905),p=r(63655),f=r(39033),v=r(5845),w=r(52712),m=r(2564),h=r(95155),E="ToastProvider",[y,x,g]=(0,i.N)("Toast"),[b,T]=(0,l.A)("Toast",[g]),[P,R]=b(E),C=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:s=50,children:i}=e,[l,c]=n.useState(null),[d,u]=n.useState(0),p=n.useRef(!1),f=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(E,"`. Expected non-empty `string`.")),(0,h.jsx)(y.Provider,{scope:t,children:(0,h.jsx)(P,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:s,toastCount:d,viewport:l,onViewportChange:c,onToastAdd:n.useCallback(()=>u(e=>e+1),[]),onToastRemove:n.useCallback(()=>u(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:i})})};C.displayName=E;var S="ToastViewport",j=["F8"],k="toast.viewportPause",L="toast.viewportResume",A=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=j,label:a="Notifications ({hotkey})",...i}=e,l=R(S,r),d=x(r),u=n.useRef(null),f=n.useRef(null),v=n.useRef(null),w=n.useRef(null),m=(0,s.s)(t,w,l.onViewportChange),E=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=l.toastCount>0;n.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=w.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=u.current,t=w.current;if(g&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(k);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(L);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},a=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",a),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[g,l.isClosePausedRef]);let b=n.useCallback(e=>{let{tabbingDirection:t}=e,r=d().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[d]);return n.useEffect(()=>{let e=w.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,a;let r=document.activeElement,s=t.shiftKey;if(t.target===e&&s){null===(n=f.current)||void 0===n||n.focus();return}let i=b({tabbingDirection:s?"backwards":"forwards"}),l=i.findIndex(e=>e===r);$(i.slice(l+1))?t.preventDefault():s?null===(o=f.current)||void 0===o||o.focus():null===(a=v.current)||void 0===a||a.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,b]),(0,h.jsxs)(c.lg,{ref:u,role:"region","aria-label":a.replace("{hotkey}",E),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&(0,h.jsx)(I,{ref:f,onFocusFromOutsideViewport:()=>{$(b({tabbingDirection:"forwards"}))}}),(0,h.jsx)(y.Slot,{scope:r,children:(0,h.jsx)(p.sG.ol,{tabIndex:-1,...i,ref:m})}),g&&(0,h.jsx)(I,{ref:v,onFocusFromOutsideViewport:()=>{$(b({tabbingDirection:"backwards"}))}})]})});A.displayName=S;var N="ToastFocusProxy",I=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,a=R(N,r);return(0,h.jsx)(m.s,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=a.viewport)||void 0===t?void 0:t.contains(r))||n()}})});I.displayName=N;var D="Toast",F=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:s,...i}=e,[l=!0,c]=(0,v.i)({prop:n,defaultProp:o,onChange:s});return(0,h.jsx)(u.C,{present:r||l,children:(0,h.jsx)(K,{open:l,...i,ref:t,onClose:()=>c(!1),onPause:(0,f.c)(e.onPause),onResume:(0,f.c)(e.onResume),onSwipeStart:(0,a.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,a.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,a.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,a.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),c(!1)})})})});F.displayName=D;var[_,M]=b(D,{onClose(){}}),K=n.forwardRef((e,t)=>{let{__scopeToast:r,type:i="foreground",duration:l,open:d,onClose:u,onEscapeKeyDown:v,onPause:w,onResume:m,onSwipeStart:E,onSwipeMove:x,onSwipeCancel:g,onSwipeEnd:b,...T}=e,P=R(D,r),[C,S]=n.useState(null),j=(0,s.s)(t,e=>S(e)),A=n.useRef(null),N=n.useRef(null),I=l||P.duration,F=n.useRef(0),M=n.useRef(I),K=n.useRef(0),{onToastAdd:O,onToastRemove:B}=P,G=(0,f.c)(()=>{var e;(null==C?void 0:C.contains(document.activeElement))&&(null===(e=P.viewport)||void 0===e||e.focus()),u()}),q=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(K.current),F.current=new Date().getTime(),K.current=window.setTimeout(G,e))},[G]);n.useEffect(()=>{let e=P.viewport;if(e){let t=()=>{q(M.current),null==m||m()},r=()=>{let e=new Date().getTime()-F.current;M.current=M.current-e,window.clearTimeout(K.current),null==w||w()};return e.addEventListener(k,r),e.addEventListener(L,t),()=>{e.removeEventListener(k,r),e.removeEventListener(L,t)}}},[P.viewport,I,w,m,q]),n.useEffect(()=>{d&&!P.isClosePausedRef.current&&q(I)},[d,I,P.isClosePausedRef,q]),n.useEffect(()=>(O(),()=>B()),[O,B]);let H=n.useMemo(()=>C?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(C):null,[C]);return P.viewport?(0,h.jsxs)(h.Fragment,{children:[H&&(0,h.jsx)(V,{__scopeToast:r,role:"status","aria-live":"foreground"===i?"assertive":"polite","aria-atomic":!0,children:H}),(0,h.jsx)(_,{scope:r,onClose:G,children:o.createPortal((0,h.jsx)(y.ItemSlot,{scope:r,children:(0,h.jsx)(c.bL,{asChild:!0,onEscapeKeyDown:(0,a.m)(v,()=>{P.isFocusedToastEscapeKeyDownRef.current||G(),P.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,h.jsx)(p.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":P.swipeDirection,...T,ref:j,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(P.isFocusedToastEscapeKeyDownRef.current=!0,G()))}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{0===e.button&&(A.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{if(!A.current)return;let t=e.clientX-A.current.x,r=e.clientY-A.current.y,n=!!N.current,o=["left","right"].includes(P.swipeDirection),a=["left","up"].includes(P.swipeDirection)?Math.min:Math.max,s=o?a(0,t):0,i=o?0:a(0,r),l="touch"===e.pointerType?10:2,c={x:s,y:i},d={originalEvent:e,delta:c};n?(N.current=c,Y("toast.swipeMove",x,d,{discrete:!1})):W(c,P.swipeDirection,l)?(N.current=c,Y("toast.swipeStart",E,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(A.current=null)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=N.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),N.current=null,A.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};W(t,P.swipeDirection,P.swipeThreshold)?Y("toast.swipeEnd",b,n,{discrete:!0}):Y("toast.swipeCancel",g,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),P.viewport)})]}):null}),V=e=>{let{__scopeToast:t,children:r,...o}=e,a=R(D,t),[s,i]=n.useState(!1),[l,c]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.c)(e);(0,w.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>i(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,h.jsx)(d.Z,{asChild:!0,children:(0,h.jsx)(m.s,{...o,children:s&&(0,h.jsxs)(h.Fragment,{children:[a.label," ",r]})})})},O=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,h.jsx)(p.sG.div,{...n,ref:t})});O.displayName="ToastTitle";var B=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,h.jsx)(p.sG.div,{...n,ref:t})});B.displayName="ToastDescription";var G="ToastAction",q=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,h.jsx)(X,{altText:r,asChild:!0,children:(0,h.jsx)(U,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(G,"`. Expected non-empty `string`.")),null)});q.displayName=G;var H="ToastClose",U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=M(H,r);return(0,h.jsx)(X,{asChild:!0,children:(0,h.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,o.onClose)})})});U.displayName=H;var X=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,h.jsx)(p.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function Y(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,p.hO)(a,s):a.dispatchEvent(s)}var W=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),a=n>o;return"left"===t||"right"===t?a&&n>r:!a&&o>r};function $(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Z=C,z=A,J=F,Q=O,ee=B,et=q,er=U},48031:(e,t,r)=>{r.d(t,{SpeedInsights:()=>p});var n=r(12115),o=r(35695),a=r(49509),s=()=>{window.si||(window.si=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];(window.siq=window.siq||[]).push(t)})};function i(){return"development"===function(){return"production"}()}function l(e){return new RegExp("/".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"(?=[/?#]|$)"))}function c(e){(0,n.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]);let t=(0,n.useRef)(null);return(0,n.useEffect)(()=>{if(t.current)e.route&&t.current(e.route);else{var r,n;let o=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("undefined"==typeof window||null===t.route)return null;s();let r=t.scriptSrc?t.scriptSrc:i()?"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js":t.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":t.basePath?"".concat(t.basePath,"/speed-insights/script.js"):"/_vercel/speed-insights/script.js";if(document.head.querySelector('script[src*="'.concat(r,'"]')))return null;t.beforeSend&&(null==(e=window.si)||e.call(window,"beforeSend",t.beforeSend));let n=document.createElement("script");return n.src=r,n.defer=!0,n.dataset.sdkn="@vercel/speed-insights"+(t.framework?"/".concat(t.framework):""),n.dataset.sdkv="1.2.0",t.sampleRate&&(n.dataset.sampleRate=t.sampleRate.toString()),t.route&&(n.dataset.route=t.route),t.endpoint?n.dataset.endpoint=t.endpoint:t.basePath&&(n.dataset.endpoint="".concat(t.basePath,"/speed-insights/vitals")),t.dsn&&(n.dataset.dsn=t.dsn),i()&&!1===t.debug&&(n.dataset.debug="false"),n.onerror=()=>{console.log("[Vercel Speed Insights] Failed to load script from ".concat(r,". Please check if any content blockers are enabled and try again."))},document.head.appendChild(n),{setRoute:e=>{n.dataset.route=null!=e?e:void 0}}}({framework:null!==(r=e.framework)&&void 0!==r?r:"react",basePath:null!==(n=e.basePath)&&void 0!==n?n:function(){if(void 0!==a&&void 0!==a.env)return a.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...e});o&&(t.current=o.setRoute)}},[e.route]),null}var d=()=>{let e=(0,o.useParams)(),t=(0,o.useSearchParams)()||new URLSearchParams,r=(0,o.usePathname)();return e?function(e,t){if(!e||!t)return e;let r=e;try{let e=Object.entries(t);for(let[t,n]of e)if(!Array.isArray(n)){let e=l(n);e.test(r)&&(r=r.replace(e,"/[".concat(t,"]")))}for(let[t,n]of e)if(Array.isArray(n)){let e=l(n.join("/"));e.test(r)&&(r=r.replace(e,"/[...".concat(t,"]")))}return r}catch(t){return e}}(r,Object.keys(e).length?e:Object.fromEntries(t.entries())):null};function u(e){let t=d();return n.createElement(c,{route:t,...e,framework:"next",basePath:function(){if(void 0!==a&&void 0!==a.env)return a.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}()})}function p(e){return n.createElement(n.Suspense,{fallback:null},n.createElement(u,{...e}))}}}]);