'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, Wallet, Target, PieChart, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface FABAction {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  onClick: () => void;
  color?: string;
}

interface FloatingActionButtonProps {
  actions?: FABAction[];
}

const defaultActions: FABAction[] = [
  {
    icon: Wallet,
    label: 'Add Income',
    onClick: () => console.log('Add Income'),
    color: 'from-primary to-primary/80',
  },
  {
    icon: PieChart,
    label: 'New Category',
    onClick: () => console.log('New Category'),
    color: 'from-accent to-accent/80',
  },
  {
    icon: Target,
    label: 'Set Goal',
    onClick: () => console.log('Set Goal'),
    color: 'from-success to-success/80',
  },
  {
    icon: TrendingUp,
    label: 'Quick Transaction',
    onClick: () => console.log('Quick Transaction'),
    color: 'from-warning to-warning/80',
  },
];

export default function FloatingActionButton({ actions = defaultActions }: FloatingActionButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = () => setIsOpen(!isOpen);

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Action Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-16 right-0 space-y-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {actions.map((action, index) => {
              const Icon = action.icon;
              return (
                <motion.div
                  key={action.label}
                  initial={{ opacity: 0, y: 20, scale: 0.8 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 20, scale: 0.8 }}
                  transition={{
                    duration: 0.2,
                    delay: index * 0.05,
                    type: 'spring',
                    stiffness: 300,
                    damping: 20,
                  }}
                  className="flex items-center gap-3"
                >
                  {/* Label */}
                  <motion.div
                    className="bg-card/90 backdrop-blur-sm border border-white/10 px-3 py-2 rounded-lg shadow-lg"
                    whileHover={{ scale: 1.05 }}
                  >
                    <span className="text-sm font-medium text-foreground whitespace-nowrap">
                      {action.label}
                    </span>
                  </motion.div>

                  {/* Action Button */}
                  <motion.button
                    onClick={() => {
                      action.onClick();
                      setIsOpen(false);
                    }}
                    className={`w-12 h-12 bg-gradient-to-r ${action.color || 'from-primary to-primary/80'} rounded-full shadow-lg flex items-center justify-center text-white`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                  >
                    <Icon className="w-5 h-5" />
                  </motion.button>
                </motion.div>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main FAB */}
      <motion.button
        onClick={toggleOpen}
        className="fab"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      >
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{ duration: 0.2 }}
        >
          {isOpen ? <X className="w-6 h-6" /> : <Plus className="w-6 h-6" />}
        </motion.div>
      </motion.button>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm -z-10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

// Preset FAB configurations
export const IncomeFAB = () => (
  <FloatingActionButton
    actions={[
      {
        icon: Wallet,
        label: 'Add Salary',
        onClick: () => console.log('Add Salary'),
        color: 'from-primary to-primary/80',
      },
      {
        icon: TrendingUp,
        label: 'Add Bonus',
        onClick: () => console.log('Add Bonus'),
        color: 'from-success to-success/80',
      },
    ]}
  />
);

export const BudgetFAB = () => (
  <FloatingActionButton
    actions={[
      {
        icon: PieChart,
        label: 'New Category',
        onClick: () => console.log('New Category'),
        color: 'from-accent to-accent/80',
      },
      {
        icon: Target,
        label: 'Set Budget',
        onClick: () => console.log('Set Budget'),
        color: 'from-primary to-primary/80',
      },
      {
        icon: TrendingUp,
        label: 'Add Expense',
        onClick: () => console.log('Add Expense'),
        color: 'from-warning to-warning/80',
      },
    ]}
  />
);
