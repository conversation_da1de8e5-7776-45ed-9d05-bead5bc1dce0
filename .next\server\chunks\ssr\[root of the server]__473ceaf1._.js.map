{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,iKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,iKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,8OAAC,iKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,iKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,8OAAC,iIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBAAS,8OAAC,iIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,8OAAC,iIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,8OAAC,iIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/lib/firebase.ts"], "sourcesContent": ["\n// Import the functions you need from the SDKs you need\nimport { initializeApp, getApps, getApp, type FirebaseApp } from 'firebase/app';\nimport { getAuth, type Auth } from 'firebase/auth';\nimport { getStorage, type FirebaseStorage } from 'firebase/storage';\n\n// Your web app's Firebase configuration (HARCODED from user input)\nconst firebaseConfig = {\n  apiKey: \"AIzaSyBGok32mQZDRrbt9yQ0VTuZSqzLIF7xj7A\",\n  authDomain: \"budgetwise-1nj2w.firebaseapp.com\",\n  projectId: \"budgetwise-1nj2w\",\n  storageBucket: \"budgetwise-1nj2w.appspot.com\", // Corrected storageBucket\n  messagingSenderId: \"153665844551\",\n  appId: \"1:153665844551:web:dc9f0ba3384c9b23c1c862\"\n};\n\n// Initialize Firebase\nlet app: FirebaseApp;\nlet authInstance: Auth;\nlet storageInstance: FirebaseStorage;\n\nif (!getApps().length) {\n  try {\n    app = initializeApp(firebaseConfig);\n    console.log(\"Firebase app initialized successfully with hardcoded config.\");\n  } catch (e) {\n    console.error(\"Firebase initialization error with hardcoded config:\", e);\n    // @ts-ignore\n    app = undefined; \n  }\n} else {\n  app = getApp();\n  console.log(\"Firebase app already initialized, getting existing app.\");\n}\n\nif (app!) { \n  try {\n    authInstance = getAuth(app);\n    console.log(\"Firebase Auth instance obtained successfully.\");\n  } catch (e) {\n      console.error(\"Firebase getAuth error after app initialization:\", e);\n      // @ts-ignore\n      authInstance = undefined; \n  }\n  try {\n    storageInstance = getStorage(app);\n    console.log(\"Firebase Storage instance obtained successfully.\");\n  } catch (e) {\n    console.error(\"Firebase getStorage error after app initialization:\", e);\n    // @ts-ignore\n    storageInstance = undefined;\n  }\n} else {\n  console.error(\"Firebase app was not initialized successfully, auth/storage instances cannot be created.\");\n  // @ts-ignore\n  authInstance = undefined;\n  // @ts-ignore\n  storageInstance = undefined;\n}\n\nexport { app, authInstance as auth, storageInstance as storage };\n"], "names": [], "mappings": "AACA,uDAAuD;;;;;;AACvD;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEA,mEAAmE;AACnE,MAAM,iBAAiB;IACrB,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,eAAe;IACf,mBAAmB;IACnB,OAAO;AACT;AAEA,sBAAsB;AACtB,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,UAAO,AAAD,IAAI,MAAM,EAAE;IACrB,IAAI;QACF,MAAM,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE;QACpB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,wDAAwD;QACtE,aAAa;QACb,MAAM;IACR;AACF,OAAO;IACL,MAAM,CAAA,GAAA,oLAAA,CAAA,SAAM,AAAD;IACX,QAAQ,GAAG,CAAC;AACd;AAEA,IAAI,KAAM;IACR,IAAI;QACF,eAAe,CAAA,GAAA,yOAAA,CAAA,UAAO,AAAD,EAAE;QACvB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,GAAG;QACR,QAAQ,KAAK,CAAC,oDAAoD;QAClE,aAAa;QACb,eAAe;IACnB;IACA,IAAI;QACF,kBAAkB,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE;QAC7B,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,uDAAuD;QACrE,aAAa;QACb,kBAAkB;IACpB;AACF,OAAO;IACL,QAAQ,KAAK,CAAC;IACd,aAAa;IACb,eAAe;IACf,aAAa;IACb,kBAAkB;AACpB", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programming/BudgetWise/src/context/AuthContext.tsx"], "sourcesContent": ["\n'use client';\n\nimport type { User as FirebaseUser, AuthError } from 'firebase/auth';\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport {\n  onAuthStateChanged,\n  signOut as firebaseSignOut,\n  createUserWithEmailAndPassword,\n  signInWithEmailAndPassword,\n  GoogleAuthProvider,\n  signInWithPopup,\n  updateProfile as firebaseUpdateProfile,\n  updatePassword as firebaseUpdatePassword,\n  reauthenticateWithCredential,\n  EmailAuthProvider,\n} from 'firebase/auth';\nimport { auth, storage } from '@/lib/firebase'; // Assuming storage is exported from firebase.ts\nimport { ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';\nimport { useRouter } from 'next/navigation';\n\ninterface AuthContextType {\n  currentUser: FirebaseUser | null;\n  loading: boolean;\n  error: string | null;\n  signUp: (email: string, password: string) => Promise<FirebaseUser | null>;\n  signIn: (email: string, password: string) => Promise<FirebaseUser | null>;\n  signInWithGoogle: () => Promise<FirebaseUser | null>;\n  signOut: () => Promise<void>;\n  clearError: () => void;\n  updateUserProfile: (displayName: string | null, photoFile?: File | null) => Promise<void>;\n  changeUserPassword: (currentPassword: string, newPassword: string) => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [currentUser, setCurrentUser] = useState<FirebaseUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      setCurrentUser(user);\n      setLoading(false);\n    });\n    return () => unsubscribe();\n  }, []);\n  \n  const clearError = () => setError(null);\n\n  const handleAuthError = (err: unknown, defaultMessage: string = 'An unknown error occurred.'): null => {\n    const authError = err as AuthError;\n    let message = authError.message || defaultMessage;\n\n    // Customize messages for common errors\n    switch (authError.code) {\n      case 'auth/user-not-found':\n        message = 'No user found with this email address. Please check your email or sign up for a new account.';\n        break;\n      case 'auth/wrong-password':\n        message = 'Incorrect password. Please try again.';\n        break;\n      case 'auth/invalid-credential':\n        message = 'Invalid email or password. Please check your credentials and try again.';\n        break;\n      case 'auth/user-disabled':\n        message = 'This account has been disabled. Please contact support.';\n        break;\n      case 'auth/email-already-in-use':\n        message = 'This email is already registered. Please sign in instead.';\n        break;\n      case 'auth/weak-password':\n        message = 'Password must be at least 6 characters long.';\n        break;\n      case 'auth/invalid-email':\n        message = 'Please enter a valid email address.';\n        break;\n      case 'auth/popup-closed-by-user':\n        message = 'Google Sign-In was cancelled.';\n        break;\n      case 'auth/requires-recent-login':\n        message = 'This operation requires recent authentication. Please log in again.';\n        break;\n      case 'auth/network-request-failed':\n        message = 'Network error. Please check your internet connection and try again.';\n        break;\n      case 'auth/too-many-requests':\n        message = 'Too many failed attempts. Please try again later.';\n        break;\n      default:\n        message = `Authentication error: ${authError.code}. ${authError.message}`;\n    }\n\n    setError(message);\n    console.error(\"Auth Error:\", authError.code, authError.message);\n    return null;\n  }\n\n  const signUp = async (email: string, password: string): Promise<FirebaseUser | null> => {\n    setLoading(true);\n    setError(null);\n    try {\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\n      setCurrentUser(userCredential.user); // Ensure local state updates\n      setLoading(false);\n      return userCredential.user;\n    } catch (err) {\n      setLoading(false);\n      return handleAuthError(err, 'Sign up failed.');\n    }\n  };\n\n  const signIn = async (email: string, password: string): Promise<FirebaseUser | null> => {\n    setLoading(true);\n    setError(null);\n    try {\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\n      setCurrentUser(userCredential.user); // Ensure local state updates\n      setLoading(false);\n      return userCredential.user;\n    } catch (err) {\n      setLoading(false);\n      return handleAuthError(err, 'Sign in failed.');\n    }\n  };\n\n  const signInWithGoogle = async (): Promise<FirebaseUser | null> => {\n    setLoading(true);\n    setError(null);\n    const provider = new GoogleAuthProvider();\n    try {\n      const userCredential = await signInWithPopup(auth, provider);\n      setCurrentUser(userCredential.user); // Ensure local state updates\n      setLoading(false);\n      return userCredential.user;\n    } catch (err) {\n      setLoading(false);\n      return handleAuthError(err, 'Google sign-in failed.');\n    }\n  };\n\n  const signOutFunc = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      await firebaseSignOut(auth);\n      setCurrentUser(null);\n      router.push('/login'); \n    } catch (err) {\n      handleAuthError(err, 'Sign out failed.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const updateUserProfile = async (displayName: string | null, photoFile?: File | null): Promise<void> => {\n    if (!currentUser) throw new Error(\"User not authenticated.\");\n    setLoading(true);\n    setError(null);\n\n    let photoURL = currentUser.photoURL;\n\n    if (photoFile === null) { // Request to remove photo\n      if (currentUser.photoURL) {\n        // Try to derive the path from the URL if it's a Firebase Storage URL for this user\n        const currentPhotoPathRegex = new RegExp(`profilePictures%2F${currentUser.uid}%2F([^?]+)`);\n        const match = currentUser.photoURL.match(currentPhotoPathRegex);\n        if (match && match[1]) {\n          const decodedPath = decodeURIComponent(match[1]);\n          const oldPhotoStoragePath = `profilePictures/${currentUser.uid}/${decodedPath}`;\n          try {\n            await deleteObject(ref(storage, oldPhotoStoragePath));\n          } catch (e: any) {\n            if (e.code !== 'storage/object-not-found') {\n              console.warn(`Failed to delete old photo by derived path ${oldPhotoStoragePath}:`, e);\n            }\n          }\n        } else {\n           console.warn(\"Could not derive old photo path from URL for deletion.\");\n        }\n      }\n      photoURL = null;\n    } else if (photoFile) { // Request to upload/update photo\n      const fileExtension = photoFile.name.split('.').pop() || 'jpg'; // default to jpg if no extension\n      const newPhotoStoragePath = `profilePictures/${currentUser.uid}/profileImage.${fileExtension}`;\n      const storageRef = ref(storage, newPhotoStoragePath);\n      \n      // If there's an existing photoURL, attempt to delete the old object.\n      if (currentUser.photoURL) {\n        const currentPhotoPathRegex = new RegExp(`profilePictures%2F${currentUser.uid}%2F([^?]+)`);\n        const match = currentUser.photoURL.match(currentPhotoPathRegex);\n         if (match && match[1]) {\n            const decodedPath = decodeURIComponent(match[1]);\n            const oldPhotoStoragePath = `profilePictures/${currentUser.uid}/${decodedPath}`;\n            // Only delete if the path is different from the new one (e.g. different extension)\n            if (oldPhotoStoragePath !== newPhotoStoragePath) {\n                try {\n                    await deleteObject(ref(storage, oldPhotoStoragePath));\n                } catch (e:any) {\n                     if (e.code !== 'storage/object-not-found') {\n                        console.warn(`Failed to delete old photo ${oldPhotoStoragePath} before new upload:`, e);\n                    }\n                }\n            }\n        }\n      }\n\n      const uploadTask = uploadBytesResumable(storageRef, photoFile);\n      await new Promise<void>((resolve, reject) => {\n        uploadTask.on('state_changed',\n          () => { /* Progress handling (optional) */ },\n          (error) => { \n            handleAuthError(error, 'Photo upload failed.'); \n            reject(error); \n          },\n          async () => {\n            try {\n              photoURL = await getDownloadURL(uploadTask.snapshot.ref);\n              resolve();\n            } catch (getUrlError) {\n              handleAuthError(getUrlError, 'Failed to get photo download URL.');\n              reject(getUrlError);\n            }\n          }\n        );\n      });\n    }\n\n    try {\n      await firebaseUpdateProfile(currentUser, {\n        displayName: displayName === null ? currentUser.displayName : displayName, \n        photoURL: photoURL, \n      });\n      setCurrentUser(auth.currentUser); \n    } catch (err) {\n      handleAuthError(err, 'Profile update failed.');\n      throw err; \n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const changeUserPassword = async (currentPassword: string, newPassword: string): Promise<void> => {\n    if (!currentUser || !currentUser.email) throw new Error(\"User not authenticated or email missing.\");\n    setLoading(true);\n    setError(null);\n\n    const credential = EmailAuthProvider.credential(currentUser.email, currentPassword);\n    try {\n      await reauthenticateWithCredential(currentUser, credential);\n      await firebaseUpdatePassword(currentUser, newPassword);\n    } catch (err) {\n      handleAuthError(err, 'Password change failed.');\n      throw err; \n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n  const value = {\n    currentUser,\n    loading,\n    error,\n    signUp,\n    signIn,\n    signInWithGoogle,\n    signOut: signOutFunc,\n    clearError,\n    updateUserProfile,\n    changeUserPassword,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n"], "names": [], "mappings": ";;;;;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,8MAAgD,gDAAgD;AAChG;AAAA;AACA;AAlBA;;;;;;;AAiCA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,oPAAA,CAAA,qBAAkB,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,CAAC;YAC5C,eAAe;YACf,WAAW;QACb;QACA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,MAAM,aAAa,IAAM,SAAS;IAElC,MAAM,kBAAkB,CAAC,KAAc,iBAAyB,4BAA4B;QAC1F,MAAM,YAAY;QAClB,IAAI,UAAU,UAAU,OAAO,IAAI;QAEnC,uCAAuC;QACvC,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU,CAAC,sBAAsB,EAAE,UAAU,IAAI,CAAC,EAAE,EAAE,UAAU,OAAO,EAAE;QAC7E;QAEA,SAAS;QACT,QAAQ,KAAK,CAAC,eAAe,UAAU,IAAI,EAAE,UAAU,OAAO;QAC9D,OAAO;IACT;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,iQAAA,CAAA,iCAA8B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YACzE,eAAe,eAAe,IAAI,GAAG,6BAA6B;YAClE,WAAW;YACX,OAAO,eAAe,IAAI;QAC5B,EAAE,OAAO,KAAK;YACZ,WAAW;YACX,OAAO,gBAAgB,KAAK;QAC9B;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,6PAAA,CAAA,6BAA0B,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE,OAAO;YACrE,eAAe,eAAe,IAAI,GAAG,6BAA6B;YAClE,WAAW;YACX,OAAO,eAAe,IAAI;QAC5B,EAAE,OAAO,KAAK;YACZ,WAAW;YACX,OAAO,gBAAgB,KAAK;QAC9B;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT,MAAM,WAAW,IAAI,oPAAA,CAAA,qBAAkB;QACvC,IAAI;YACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD,EAAE,sHAAA,CAAA,OAAI,EAAE;YACnD,eAAe,eAAe,IAAI,GAAG,6BAA6B;YAClE,WAAW;YACX,OAAO,eAAe,IAAI;QAC5B,EAAE,OAAO,KAAK;YACZ,WAAW;YACX,OAAO,gBAAgB,KAAK;QAC9B;IACF;IAEA,MAAM,cAAc;QAClB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,CAAA,GAAA,yOAAA,CAAA,UAAe,AAAD,EAAE,sHAAA,CAAA,OAAI;YAC1B,eAAe;YACf,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,gBAAgB,KAAK;QACvB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO,aAA4B;QAC3D,IAAI,CAAC,aAAa,MAAM,IAAI,MAAM;QAClC,WAAW;QACX,SAAS;QAET,IAAI,WAAW,YAAY,QAAQ;QAEnC,IAAI,cAAc,MAAM;YACtB,IAAI,YAAY,QAAQ,EAAE;gBACxB,mFAAmF;gBACnF,MAAM,wBAAwB,IAAI,OAAO,CAAC,kBAAkB,EAAE,YAAY,GAAG,CAAC,UAAU,CAAC;gBACzF,MAAM,QAAQ,YAAY,QAAQ,CAAC,KAAK,CAAC;gBACzC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;oBACrB,MAAM,cAAc,mBAAmB,KAAK,CAAC,EAAE;oBAC/C,MAAM,sBAAsB,CAAC,gBAAgB,EAAE,YAAY,GAAG,CAAC,CAAC,EAAE,aAAa;oBAC/E,IAAI;wBACF,MAAM,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,UAAO,EAAE;oBAClC,EAAE,OAAO,GAAQ;wBACf,IAAI,EAAE,IAAI,KAAK,4BAA4B;4BACzC,QAAQ,IAAI,CAAC,CAAC,2CAA2C,EAAE,oBAAoB,CAAC,CAAC,EAAE;wBACrF;oBACF;gBACF,OAAO;oBACJ,QAAQ,IAAI,CAAC;gBAChB;YACF;YACA,WAAW;QACb,OAAO,IAAI,WAAW;YACpB,MAAM,gBAAgB,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,OAAO,iCAAiC;YACjG,MAAM,sBAAsB,CAAC,gBAAgB,EAAE,YAAY,GAAG,CAAC,cAAc,EAAE,eAAe;YAC9F,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,UAAO,EAAE;YAEhC,qEAAqE;YACrE,IAAI,YAAY,QAAQ,EAAE;gBACxB,MAAM,wBAAwB,IAAI,OAAO,CAAC,kBAAkB,EAAE,YAAY,GAAG,CAAC,UAAU,CAAC;gBACzF,MAAM,QAAQ,YAAY,QAAQ,CAAC,KAAK,CAAC;gBACxC,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;oBACpB,MAAM,cAAc,mBAAmB,KAAK,CAAC,EAAE;oBAC/C,MAAM,sBAAsB,CAAC,gBAAgB,EAAE,YAAY,GAAG,CAAC,CAAC,EAAE,aAAa;oBAC/E,mFAAmF;oBACnF,IAAI,wBAAwB,qBAAqB;wBAC7C,IAAI;4BACA,MAAM,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,UAAO,EAAE;wBACpC,EAAE,OAAO,GAAO;4BACX,IAAI,EAAE,IAAI,KAAK,4BAA4B;gCACxC,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,oBAAoB,mBAAmB,CAAC,EAAE;4BACzF;wBACJ;oBACJ;gBACJ;YACF;YAEA,MAAM,aAAa,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY;YACpD,MAAM,IAAI,QAAc,CAAC,SAAS;gBAChC,WAAW,EAAE,CAAC,iBACZ,KAA2C,GAC3C,CAAC;oBACC,gBAAgB,OAAO;oBACvB,OAAO;gBACT,GACA;oBACE,IAAI;wBACF,WAAW,MAAM,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,CAAC,GAAG;wBACvD;oBACF,EAAE,OAAO,aAAa;wBACpB,gBAAgB,aAAa;wBAC7B,OAAO;oBACT;gBACF;YAEJ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,gPAAA,CAAA,gBAAqB,AAAD,EAAE,aAAa;gBACvC,aAAa,gBAAgB,OAAO,YAAY,WAAW,GAAG;gBAC9D,UAAU;YACZ;YACA,eAAe,sHAAA,CAAA,OAAI,CAAC,WAAW;QACjC,EAAE,OAAO,KAAK;YACZ,gBAAgB,KAAK;YACrB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO,iBAAyB;QACzD,IAAI,CAAC,eAAe,CAAC,YAAY,KAAK,EAAE,MAAM,IAAI,MAAM;QACxD,WAAW;QACX,SAAS;QAET,MAAM,aAAa,mPAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,YAAY,KAAK,EAAE;QACnE,IAAI;YACF,MAAM,CAAA,GAAA,+PAAA,CAAA,+BAA4B,AAAD,EAAE,aAAa;YAChD,MAAM,CAAA,GAAA,iPAAA,CAAA,iBAAsB,AAAD,EAAE,aAAa;QAC5C,EAAE,OAAO,KAAK;YACZ,gBAAgB,KAAK;YACrB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAGA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;QACT;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C", "debugId": null}}]}