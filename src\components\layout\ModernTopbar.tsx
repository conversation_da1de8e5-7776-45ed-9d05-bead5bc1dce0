'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Bell,
  Settings,
  Eye,
  EyeOff,
  Plus,
  Filter,
  Calendar,
  Download,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

interface ModernTopbarProps {
  onMenuClick: () => void;
  balancesVisible?: boolean;
  onToggleBalances?: () => void;
}

export default function ModernTopbar({
  onMenuClick,
  balancesVisible = true,
  onToggleBalances,
}: ModernTopbarProps) {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <motion.header
      className="nav-modern sticky top-0 z-30 px-6 py-4"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search transactions, categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-modern pl-10 w-80 hidden md:block"
            />
          </div>

          {/* Quick Actions */}
          <div className="hidden lg:flex items-center gap-2">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button variant="ghost" size="sm" className="glass-card">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button variant="ghost" size="sm" className="glass-card">
                <Calendar className="w-4 h-4 mr-2" />
                This Month
              </Button>
            </motion.div>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-3">
          {/* Balance Visibility Toggle */}
          {onToggleBalances && (
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleBalances}
                className="glass-card w-10 h-10"
              >
                <motion.div
                  key={balancesVisible ? 'visible' : 'hidden'}
                  initial={{ rotate: 180, opacity: 0 }}
                  animate={{ rotate: 0, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {balancesVisible ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </motion.div>
              </Button>
            </motion.div>
          )}

          {/* Export Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button variant="ghost" size="icon" className="glass-card w-10 h-10">
              <Download className="w-4 h-4" />
            </Button>
          </motion.div>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="ghost" size="icon" className="glass-card w-10 h-10 relative">
                  <Bell className="w-4 h-4" />
                  <motion.div
                    className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  />
                </Button>
              </motion.div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 glass-card border-white/20">
              <DropdownMenuLabel className="flex items-center justify-between">
                Notifications
                <Badge variant="secondary" className="badge-modern">
                  3 new
                </Badge>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-white/10" />
              <DropdownMenuItem className="p-4 hover:bg-white/10">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Budget Goal Achieved!</p>
                  <p className="text-xs text-muted-foreground">
                    You've successfully saved R1,500 this month
                  </p>
                  <p className="text-xs text-muted-foreground">2 hours ago</p>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="p-4 hover:bg-white/10">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Overspending Alert</p>
                  <p className="text-xs text-muted-foreground">
                    You're 15% over budget in Entertainment category
                  </p>
                  <p className="text-xs text-muted-foreground">1 day ago</p>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="p-4 hover:bg-white/10">
                <div className="space-y-1">
                  <p className="text-sm font-medium">New Achievement Unlocked</p>
                  <p className="text-xs text-muted-foreground">
                    "Savings Streak" - 7 days of staying under budget
                  </p>
                  <p className="text-xs text-muted-foreground">3 days ago</p>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Add New Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button className="btn-modern-primary hidden sm:flex">
              <Plus className="w-4 h-4 mr-2" />
              Add Transaction
            </Button>
          </motion.div>

          {/* Mobile Add Button */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button size="icon" className="btn-modern-primary sm:hidden">
              <Plus className="w-4 h-4" />
            </Button>
          </motion.div>

          {/* Settings */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button variant="ghost" size="icon" className="glass-card w-10 h-10">
                  <Settings className="w-4 h-4" />
                </Button>
              </motion.div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="glass-card border-white/20">
              <DropdownMenuLabel>Quick Settings</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-white/10" />
              <DropdownMenuItem className="hover:bg-white/10">
                <span>Dark Mode</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-white/10">
                <span>Currency Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-white/10">
                <span>Export Data</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-white/10" />
              <DropdownMenuItem className="hover:bg-white/10">
                <span>Preferences</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Mobile Search */}
      <motion.div
        className="mt-4 md:hidden"
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: 'auto' }}
        transition={{ delay: 0.2 }}
      >
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input-modern pl-10 w-full"
          />
        </div>
      </motion.div>
    </motion.header>
  );
}
