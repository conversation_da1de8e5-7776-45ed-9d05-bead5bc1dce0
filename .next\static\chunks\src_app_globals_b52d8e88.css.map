{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n  :root {\n    --background: 220 40% 8%; /* Deeper Dark Slate Blue */\n    --foreground: 220 15% 90%; /* Brighter Light Grayish Blue */\n\n    --card: 220 40% 12%;\n    --card-foreground: 220 15% 85%;\n\n    --popover: 220 40% 10%; /* Slightly darker popover */\n    --popover-foreground: 220 15% 88%;\n\n    --primary: 180 75% 50%; /* Brighter Teal */\n    --primary-foreground: 180 50% 98%; /* Very light for text on primary */\n\n    --secondary: 220 35% 22%;\n    --secondary-foreground: 220 10% 75%;\n\n    --muted: 220 25% 25%;\n    --muted-foreground: 220 10% 65%;\n\n    --accent: 15 95% 65%; /* Brighter Coral */\n    --accent-foreground: 15 90% 8%; /* Dark brown/black for text on accent */\n\n    --destructive: 0 75% 55%; /* Brighter Red */\n    --destructive-foreground: 0 0% 98%;\n\n    --success: 142 76% 36%; /* Green for success states */\n    --success-foreground: 142 76% 95%;\n\n    --warning: 38 92% 50%; /* Orange for warning states */\n    --warning-foreground: 38 92% 95%;\n\n    --border: 220 35% 18%;\n    --input: 220 35% 15%;\n    --ring: 180 75% 55%; /* Primary color for focus rings */\n\n    --chart-1: 180 75% 50%; /* Brighter Teal (Primary) */\n    --chart-2: 15 95% 65%;  /* Brighter Coral (Accent) */\n    --chart-3: 200 75% 60%; /* Brighter Blue */\n    --chart-4: 300 65% 65%; /* Brighter Magenta/Purple */\n    --chart-5: 50 85% 60%;  /* Brighter Yellow/Gold */\n    --chart-6: 142 76% 45%; /* Green */\n    --chart-7: 270 75% 60%; /* Purple */\n    --chart-8: 340 75% 60%; /* Pink */\n\n    --radius: 0.5rem;\n\n    /* Gradient variables for enhanced visuals */\n    --gradient-primary: linear-gradient(135deg, hsl(180 75% 50%) 0%, hsl(200 75% 55%) 100%);\n    --gradient-accent: linear-gradient(135deg, hsl(15 95% 65%) 0%, hsl(25 95% 60%) 100%);\n    --gradient-card: linear-gradient(135deg, hsl(220 40% 12%) 0%, hsl(220 35% 15%) 100%);\n    --gradient-success: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(152 76% 40%) 100%);\n  }\n\n  .dark {\n    --background: 220 40% 4%; /* Even Darker Slate Blue */\n    --foreground: 220 15% 85%; /* Brighter text for dark mode */\n\n    --card: 220 40% 8%;\n    --card-foreground: 220 15% 80%;\n\n    --popover: 220 40% 6%;\n    --popover-foreground: 220 15% 85%;\n\n    --primary: 180 75% 55%; /* Brighter Teal for Dark Mode */\n    --primary-foreground: 180 50% 8%; /* Dark text on bright teal */\n\n    --secondary: 220 35% 18%;\n    --secondary-foreground: 220 10% 70%;\n\n    --muted: 220 25% 20%;\n    --muted-foreground: 220 10% 60%;\n\n    --accent: 15 95% 70%; /* Brighter Coral for Dark Mode */\n    --accent-foreground: 15 90% 5%; /* Dark text on bright coral */\n\n    --destructive: 0 75% 58%;\n    --destructive-foreground: 0 0% 98%;\n\n    --success: 142 76% 40%;\n    --success-foreground: 142 76% 95%;\n\n    --warning: 38 92% 55%;\n    --warning-foreground: 38 92% 95%;\n\n    --border: 220 35% 15%;\n    --input: 220 35% 12%;\n    --ring: 180 75% 60%; /* Brighter Teal for ring */\n\n    /* Enhanced gradients for dark mode */\n    --gradient-primary: linear-gradient(135deg, hsl(180 75% 55%) 0%, hsl(200 75% 60%) 100%);\n    --gradient-accent: linear-gradient(135deg, hsl(15 95% 70%) 0%, hsl(25 95% 65%) 100%);\n    --gradient-card: linear-gradient(135deg, hsl(220 40% 8%) 0%, hsl(220 35% 12%) 100%);\n    --gradient-success: linear-gradient(135deg, hsl(142 76% 40%) 0%, hsl(152 76% 45%) 100%);\n  }\n  * {\n  border-color: hsl(var(--border));\n}\n  body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n  min-height: 100vh;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n  .container {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n  @media (min-width: 640px) {\n\n  .container {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n}\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 2rem;\n  padding-left: 2rem;\n}\n@media (min-width: 1400px) {\n\n  .container {\n    max-width: 1400px;\n  }\n}\n/* Modern Glassmorphism Cards */\n.glass-card {\n  border-radius: 1rem;\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-color: hsl(var(--card) / 0.4);\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n/* Modern Sidebar Styles */\n.sidebar-modern {\n  border-right-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--card) / 0.8) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--card) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--card) / 0.4) var(--tw-gradient-to-position);\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.sidebar-item {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n  display: flex;\n  cursor: pointer;\n  align-items: center;\n  gap: 0.75rem;\n  border-radius: 0.75rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n  animation-duration: 300ms;\n}\n.sidebar-item:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  background-color: rgb(255 255 255 / 0.1);\n}\n.sidebar-item-active {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n  display: flex;\n  cursor: pointer;\n  align-items: center;\n  gap: 0.75rem;\n  border-radius: 0.75rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n  animation-duration: 300ms;\n}\n.sidebar-item-active:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  background-color: rgb(255 255 255 / 0.1);\n}\n.sidebar-item-active {\n  border-width: 1px;\n  border-color: hsl(var(--primary) / 0.3);\n  background-color: hsl(var(--primary) / 0.2);\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n/* Modern Button Styles */\n.btn-modern-primary {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  border-radius: 0.75rem;\n  border-width: 1px;\n  border-color: hsl(var(--primary) / 0.2);\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--primary) / 0.8) var(--tw-gradient-to-position);\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  font-weight: 600;\n  color: hsl(var(--primary-foreground));\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n  animation-duration: 300ms;\n}\n.btn-modern-primary:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  --tw-gradient-from: hsl(var(--primary) / 0.9) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--primary) / 0.7) var(--tw-gradient-to-position);\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.4);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.btn-modern-primary:active {\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n/* Modern Input Styles */\n.input-modern {\n  border-radius: 0.75rem;\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.2);\n  background-color: hsl(var(--input) / 0.3);\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n  animation-duration: 300ms;\n}\n.input-modern::placeholder {\n  color: hsl(var(--muted-foreground) / 0.6);\n}\n.input-modern:focus {\n  border-color: hsl(var(--primary) / 0.6);\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-color: hsl(var(--primary) / 0.3);\n}\n/* Modern Progress Styles */\n.progress-modern {\n  overflow: hidden;\n  border-radius: 9999px;\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--muted) / 0.3) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--muted) / 0.2) var(--tw-gradient-to-position);\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.progress-modern > div {\n  border-radius: 9999px;\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--primary) / 0.8) var(--tw-gradient-to-position);\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 700ms;\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  animation-duration: 700ms;\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n/* Modern Metric Cards */\n.metric-card {\n  border-radius: 1rem;\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-color: hsl(var(--card) / 0.4);\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  position: relative;\n  overflow: hidden;\n  padding: 1.5rem;\n}\n.metric-card::before {\n    content: '';\n    position: absolute;\n    top: 0px;\n    left: 0px;\n    height: 0.25rem;\n    width: 100%;\n    background-image: linear-gradient(to right, var(--tw-gradient-stops));\n    --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\n    --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n    --tw-gradient-to: hsl(var(--accent) / 0)  var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--accent)) var(--tw-gradient-via-position), var(--tw-gradient-to);\n    --tw-gradient-to: hsl(var(--primary)) var(--tw-gradient-to-position);\n  }\n/* Modern Chart Container */\n.chart-container {\n  border-radius: 1rem;\n  border-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-color: hsl(var(--card) / 0.4);\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  position: relative;\n  overflow: hidden;\n  padding: 1.5rem;\n}\n.chart-container::before {\n    content: '';\n    position: absolute;\n    top: 0px;\n    right: 0px;\n    height: 5rem;\n    width: 5rem;\n    border-bottom-left-radius: 9999px;\n    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));\n    --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);\n    --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n    --tw-gradient-to: transparent var(--tw-gradient-to-position);\n  }\n/* Modern Navigation */\n.nav-modern {\n  border-bottom-width: 1px;\n  border-color: rgb(255 255 255 / 0.1);\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--card) / 0.9) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--card) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--card) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--card) / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--card) / 0.9) var(--tw-gradient-to-position);\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n/* Floating Action Button */\n.fab {\n  position: fixed;\n  bottom: 1.5rem;\n  right: 1.5rem;\n  z-index: 50;\n  display: flex;\n  height: 3.5rem;\n  width: 3.5rem;\n  align-items: center;\n  justify-content: center;\n  border-radius: 9999px;\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--accent)) var(--tw-gradient-to-position);\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.4);\n  --tw-shadow: var(--tw-shadow-colored);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n  animation-duration: 300ms;\n}\n.fab:hover {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n/* Modern Badge */\n.badge-modern {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  border-radius: 9999px;\n  border-width: 1px;\n  border-color: hsl(var(--accent) / 0.3);\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n  --tw-gradient-from: hsl(var(--accent) / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--accent) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n  --tw-gradient-to: hsl(var(--accent) / 0.1) var(--tw-gradient-to-position);\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  color: hsl(var(--accent));\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n/* Glow Effects */\n.glow-primary-modern {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.25);\n  --tw-shadow: var(--tw-shadow-colored);\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 500ms;\n  animation-duration: 500ms;\n}\n.glow-primary-modern:hover {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  --tw-shadow-color: hsl(var(--primary) / 0.4);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.pointer-events-auto {\n  pointer-events: auto;\n}\n.visible {\n  visibility: visible;\n}\n.invisible {\n  visibility: hidden;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.sticky {\n  position: sticky;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\n.-bottom-1 {\n  bottom: -0.25rem;\n}\n.-bottom-40 {\n  bottom: -10rem;\n}\n.-left-40 {\n  left: -10rem;\n}\n.-right-1 {\n  right: -0.25rem;\n}\n.-right-40 {\n  right: -10rem;\n}\n.-top-1 {\n  top: -0.25rem;\n}\n.-top-40 {\n  top: -10rem;\n}\n.bottom-0 {\n  bottom: 0px;\n}\n.bottom-16 {\n  bottom: 4rem;\n}\n.bottom-6 {\n  bottom: 1.5rem;\n}\n.left-0 {\n  left: 0px;\n}\n.left-1 {\n  left: 0.25rem;\n}\n.left-1\\/2 {\n  left: 50%;\n}\n.left-2 {\n  left: 0.5rem;\n}\n.left-3 {\n  left: 0.75rem;\n}\n.left-4 {\n  left: 1rem;\n}\n.left-\\[50\\%\\] {\n  left: 50%;\n}\n.right-0 {\n  right: 0px;\n}\n.right-1 {\n  right: 0.25rem;\n}\n.right-2 {\n  right: 0.5rem;\n}\n.right-3 {\n  right: 0.75rem;\n}\n.right-4 {\n  right: 1rem;\n}\n.right-6 {\n  right: 1.5rem;\n}\n.top-0 {\n  top: 0px;\n}\n.top-1 {\n  top: 0.25rem;\n}\n.top-1\\.5 {\n  top: 0.375rem;\n}\n.top-1\\/2 {\n  top: 50%;\n}\n.top-2 {\n  top: 0.5rem;\n}\n.top-3\\.5 {\n  top: 0.875rem;\n}\n.top-4 {\n  top: 1rem;\n}\n.top-\\[50\\%\\] {\n  top: 50%;\n}\n.-z-10 {\n  z-index: -10;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-20 {\n  z-index: 20;\n}\n.z-30 {\n  z-index: 30;\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.z-\\[100\\] {\n  z-index: 100;\n}\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\n.mx-3\\.5 {\n  margin-left: 0.875rem;\n  margin-right: 0.875rem;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-0\\.5 {\n  margin-top: 0.125rem;\n  margin-bottom: 0.125rem;\n}\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.mb-0\\.5 {\n  margin-bottom: 0.125rem;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.ml-1\\.5 {\n  margin-left: 0.375rem;\n}\n.ml-auto {\n  margin-left: auto;\n}\n.mr-1 {\n  margin-right: 0.25rem;\n}\n.mr-2 {\n  margin-right: 0.5rem;\n}\n.mt-2 {\n  margin-top: 0.5rem;\n}\n.mt-3 {\n  margin-top: 0.75rem;\n}\n.mt-4 {\n  margin-top: 1rem;\n}\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\n.aspect-video {\n  aspect-ratio: 16 / 9;\n}\n.size-4 {\n  width: 1rem;\n  height: 1rem;\n}\n.h-1 {\n  height: 0.25rem;\n}\n.h-1\\.5 {\n  height: 0.375rem;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-11 {\n  height: 2.75rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-2 {\n  height: 0.5rem;\n}\n.h-2\\.5 {\n  height: 0.625rem;\n}\n.h-20 {\n  height: 5rem;\n}\n.h-3 {\n  height: 0.75rem;\n}\n.h-3\\.5 {\n  height: 0.875rem;\n}\n.h-32 {\n  height: 8rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-6 {\n  height: 1.5rem;\n}\n.h-60 {\n  height: 15rem;\n}\n.h-7 {\n  height: 1.75rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-80 {\n  height: 20rem;\n}\n.h-9 {\n  height: 2.25rem;\n}\n.h-96 {\n  height: 24rem;\n}\n.h-\\[1px\\] {\n  height: 1px;\n}\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\n.h-auto {\n  height: auto;\n}\n.h-full {\n  height: 100%;\n}\n.h-px {\n  height: 1px;\n}\n.h-svh {\n  height: 100svh;\n}\n.max-h-96 {\n  max-height: 24rem;\n}\n.max-h-\\[250px\\] {\n  max-height: 250px;\n}\n.max-h-screen {\n  max-height: 100vh;\n}\n.min-h-0 {\n  min-height: 0px;\n}\n.min-h-\\[60vh\\] {\n  min-height: 60vh;\n}\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.min-h-svh {\n  min-height: 100svh;\n}\n.w-0 {\n  width: 0px;\n}\n.w-1 {\n  width: 0.25rem;\n}\n.w-1\\/2 {\n  width: 50%;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-11 {\n  width: 2.75rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-2\\.5 {\n  width: 0.625rem;\n}\n.w-20 {\n  width: 5rem;\n}\n.w-3 {\n  width: 0.75rem;\n}\n.w-3\\.5 {\n  width: 0.875rem;\n}\n.w-3\\/4 {\n  width: 75%;\n}\n.w-32 {\n  width: 8rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-56 {\n  width: 14rem;\n}\n.w-6 {\n  width: 1.5rem;\n}\n.w-7 {\n  width: 1.75rem;\n}\n.w-72 {\n  width: 18rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-80 {\n  width: 20rem;\n}\n.w-9 {\n  width: 2.25rem;\n}\n.w-96 {\n  width: 24rem;\n}\n.w-\\[--sidebar-width\\] {\n  width: var(--sidebar-width);\n}\n.w-\\[1px\\] {\n  width: 1px;\n}\n.w-auto {\n  width: auto;\n}\n.w-full {\n  width: 100%;\n}\n.min-w-0 {\n  min-width: 0px;\n}\n.min-w-5 {\n  min-width: 1.25rem;\n}\n.min-w-\\[12rem\\] {\n  min-width: 12rem;\n}\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-\\[--skeleton-width\\] {\n  max-width: var(--skeleton-width);\n}\n.max-w-lg {\n  max-width: 32rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-sm {\n  max-width: 24rem;\n}\n.max-w-xs {\n  max-width: 20rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.flex-grow {\n  flex-grow: 1;\n}\n.grow {\n  flex-grow: 1;\n}\n.caption-bottom {\n  caption-side: bottom;\n}\n.border-collapse {\n  border-collapse: collapse;\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-px {\n  --tw-translate-x: -1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-px {\n  --tw-translate-x: 1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-rotate-90 {\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n.cursor-default {\n  cursor: default;\n}\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.touch-none {\n  touch-action: none;\n}\n.select-none {\n  user-select: none;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-center {\n  align-items: center;\n}\n.items-stretch {\n  align-items: stretch;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.gap-1 {\n  gap: 0.25rem;\n}\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-3 {\n  gap: 0.75rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-6 {\n  gap: 1.5rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.gap-x-2 {\n  column-gap: 0.5rem;\n}\n.gap-y-1 {\n  row-gap: 0.25rem;\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-\\[2px\\] {\n  border-radius: 2px;\n}\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n.rounded-r-full {\n  border-top-right-radius: 9999px;\n  border-bottom-right-radius: 9999px;\n}\n.rounded-bl-full {\n  border-bottom-left-radius: 9999px;\n}\n.rounded-tr-full {\n  border-top-right-radius: 9999px;\n}\n.border {\n  border-width: 1px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-4 {\n  border-width: 4px;\n}\n.border-\\[1\\.5px\\] {\n  border-width: 1.5px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-l {\n  border-left-width: 1px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-dashed {\n  border-style: dashed;\n}\n.border-\\[--color-border\\] {\n  border-color: var(--color-border);\n}\n.border-border\\/50 {\n  border-color: hsl(var(--border) / 0.5);\n}\n.border-destructive {\n  border-color: hsl(var(--destructive));\n}\n.border-destructive\\/20 {\n  border-color: hsl(var(--destructive) / 0.2);\n}\n.border-destructive\\/50 {\n  border-color: hsl(var(--destructive) / 0.5);\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-primary {\n  border-color: hsl(var(--primary));\n}\n.border-primary\\/50 {\n  border-color: hsl(var(--primary) / 0.5);\n}\n.border-sidebar-border {\n  border-color: hsl(var(--sidebar-border));\n}\n.border-success\\/20 {\n  border-color: hsl(var(--success) / 0.2);\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-white\\/10 {\n  border-color: rgb(255 255 255 / 0.1);\n}\n.border-white\\/20 {\n  border-color: rgb(255 255 255 / 0.2);\n}\n.border-l-transparent {\n  border-left-color: transparent;\n}\n.border-t-transparent {\n  border-top-color: transparent;\n}\n.bg-\\[--color-bg\\] {\n  background-color: var(--color-bg);\n}\n.bg-accent {\n  background-color: hsl(var(--accent));\n}\n.bg-accent\\/10 {\n  background-color: hsl(var(--accent) / 0.1);\n}\n.bg-background {\n  background-color: hsl(var(--background));\n}\n.bg-black\\/20 {\n  background-color: rgb(0 0 0 / 0.2);\n}\n.bg-black\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\n.bg-border {\n  background-color: hsl(var(--border));\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-card\\/90 {\n  background-color: hsl(var(--card) / 0.9);\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-destructive\\/10 {\n  background-color: hsl(var(--destructive) / 0.1);\n}\n.bg-destructive\\/5 {\n  background-color: hsl(var(--destructive) / 0.05);\n}\n.bg-destructive\\/70 {\n  background-color: hsl(var(--destructive) / 0.7);\n}\n.bg-green-500\\/10 {\n  background-color: rgb(34 197 94 / 0.1);\n}\n.bg-green-600\\/10 {\n  background-color: rgb(22 163 74 / 0.1);\n}\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-primary\\/10 {\n  background-color: hsl(var(--primary) / 0.1);\n}\n.bg-primary\\/20 {\n  background-color: hsl(var(--primary) / 0.2);\n}\n.bg-primary\\/5 {\n  background-color: hsl(var(--primary) / 0.05);\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-sidebar {\n  background-color: hsl(var(--sidebar-background));\n}\n.bg-sidebar-border {\n  background-color: hsl(var(--sidebar-border));\n}\n.bg-success {\n  background-color: hsl(var(--success));\n}\n.bg-success\\/10 {\n  background-color: hsl(var(--success) / 0.1);\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-warning {\n  background-color: hsl(var(--warning));\n}\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/5 {\n  background-color: rgb(255 255 255 / 0.05);\n}\n.bg-gradient-to-bl {\n  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-tr {\n  background-image: linear-gradient(to top right, var(--tw-gradient-stops));\n}\n.from-accent {\n  --tw-gradient-from: hsl(var(--accent)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--accent) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-accent\\/10 {\n  --tw-gradient-from: hsl(var(--accent) / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--accent) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-400 {\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-card {\n  --tw-gradient-from: hsl(var(--card)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--card) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-destructive {\n  --tw-gradient-from: hsl(var(--destructive)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--destructive) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-400 {\n  --tw-gradient-from: #9ca3af var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(156 163 175 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary {\n  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary\\/10 {\n  --tw-gradient-from: hsl(var(--primary) / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary\\/20 {\n  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-purple-400 {\n  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-success {\n  --tw-gradient-from: hsl(var(--success)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--success) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-success\\/20 {\n  --tw-gradient-from: hsl(var(--success) / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--success) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-transparent {\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-warning {\n  --tw-gradient-from: hsl(var(--warning)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--warning) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-400 {\n  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-success\\/90 {\n  --tw-gradient-to: hsl(var(--success) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--success) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white\\/20 {\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white\\/30 {\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-accent {\n  --tw-gradient-to: hsl(var(--accent)) var(--tw-gradient-to-position);\n}\n.to-accent\\/20 {\n  --tw-gradient-to: hsl(var(--accent) / 0.2) var(--tw-gradient-to-position);\n}\n.to-accent\\/80 {\n  --tw-gradient-to: hsl(var(--accent) / 0.8) var(--tw-gradient-to-position);\n}\n.to-blue-600 {\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\n}\n.to-card\\/80 {\n  --tw-gradient-to: hsl(var(--card) / 0.8) var(--tw-gradient-to-position);\n}\n.to-destructive\\/80 {\n  --tw-gradient-to: hsl(var(--destructive) / 0.8) var(--tw-gradient-to-position);\n}\n.to-gray-600 {\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\n}\n.to-primary\\/80 {\n  --tw-gradient-to: hsl(var(--primary) / 0.8) var(--tw-gradient-to-position);\n}\n.to-purple-600 {\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\n.to-success\\/80 {\n  --tw-gradient-to: hsl(var(--success) / 0.8) var(--tw-gradient-to-position);\n}\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.to-warning\\/80 {\n  --tw-gradient-to: hsl(var(--warning) / 0.8) var(--tw-gradient-to-position);\n}\n.to-yellow-600 {\n  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);\n}\n.fill-current {\n  fill: currentColor;\n}\n.stroke-destructive {\n  stroke: hsl(var(--destructive));\n}\n.stroke-primary {\n  stroke: hsl(var(--primary));\n}\n.stroke-success {\n  stroke: hsl(var(--success));\n}\n.stroke-warning {\n  stroke: hsl(var(--warning));\n}\n.p-0 {\n  padding: 0px;\n}\n.p-1 {\n  padding: 0.25rem;\n}\n.p-1\\.5 {\n  padding: 0.375rem;\n}\n.p-2 {\n  padding: 0.5rem;\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-\\[1px\\] {\n  padding: 1px;\n}\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\n.pb-0 {\n  padding-bottom: 0px;\n}\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\n.pb-4 {\n  padding-bottom: 1rem;\n}\n.pl-10 {\n  padding-left: 2.5rem;\n}\n.pl-2 {\n  padding-left: 0.5rem;\n}\n.pl-8 {\n  padding-left: 2rem;\n}\n.pr-10 {\n  padding-right: 2.5rem;\n}\n.pr-2 {\n  padding-right: 0.5rem;\n}\n.pr-8 {\n  padding-right: 2rem;\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.pt-1 {\n  padding-top: 0.25rem;\n}\n.pt-2 {\n  padding-top: 0.5rem;\n}\n.pt-3 {\n  padding-top: 0.75rem;\n}\n.pt-4 {\n  padding-top: 1rem;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.align-middle {\n  vertical-align: middle;\n}\n.font-body {\n  font-family: PT Sans, sans-serif;\n}\n.font-headline {\n  font-family: PT Sans, sans-serif;\n}\n.font-mono {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-\\[0\\.8rem\\] {\n  font-size: 0.8rem;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-normal {\n  font-weight: 400;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.tabular-nums {\n  --tw-numeric-spacing: tabular-nums;\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\n}\n.leading-none {\n  line-height: 1;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\n.text-accent {\n  color: hsl(var(--accent));\n}\n.text-accent-foreground {\n  color: hsl(var(--accent-foreground));\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-current {\n  color: currentColor;\n}\n.text-destructive {\n  color: hsl(var(--destructive));\n}\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-foreground\\/50 {\n  color: hsl(var(--foreground) / 0.5);\n}\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-muted {\n  color: hsl(var(--muted));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-muted-foreground\\/70 {\n  color: hsl(var(--muted-foreground) / 0.7);\n}\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-sidebar-foreground {\n  color: hsl(var(--sidebar-foreground));\n}\n.text-sidebar-foreground\\/70 {\n  color: hsl(var(--sidebar-foreground) / 0.7);\n}\n.text-success {\n  color: hsl(var(--success));\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-20 {\n  opacity: 0.2;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-60 {\n  opacity: 0.6;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.opacity-90 {\n  opacity: 0.9;\n}\n.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-none {\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-blue-400\\/30 {\n  --tw-shadow-color: rgb(96 165 250 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-destructive\\/30 {\n  --tw-shadow-color: hsl(var(--destructive) / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-gray-400\\/20 {\n  --tw-shadow-color: rgb(156 163 175 / 0.2);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-primary\\/30 {\n  --tw-shadow-color: hsl(var(--primary) / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-purple-400\\/30 {\n  --tw-shadow-color: rgb(192 132 252 / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-success\\/30 {\n  --tw-shadow-color: hsl(var(--success) / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-warning\\/30 {\n  --tw-shadow-color: hsl(var(--warning) / 0.3);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.shadow-yellow-400\\/40 {\n  --tw-shadow-color: rgb(250 204 21 / 0.4);\n  --tw-shadow: var(--tw-shadow-colored);\n}\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.outline {\n  outline-style: solid;\n}\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-primary\\/50 {\n  --tw-ring-color: hsl(var(--primary) / 0.5);\n}\n.ring-sidebar-ring {\n  --tw-ring-color: hsl(var(--sidebar-ring));\n}\n.ring-offset-2 {\n  --tw-ring-offset-width: 2px;\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur-3xl {\n  --tw-blur: blur(64px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-sm {\n  --tw-blur: blur(4px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.grayscale {\n  --tw-grayscale: grayscale(100%);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[left\\2c right\\2c width\\] {\n  transition-property: left,right,width;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[margin\\2c opa\\] {\n  transition-property: margin,opa;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[width\\2c height\\2c padding\\] {\n  transition-property: width,height,padding;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[width\\] {\n  transition-property: width;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-linear {\n  transition-timing-function: linear;\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\n.duration-200 {\n  animation-duration: 200ms;\n}\n.duration-300 {\n  animation-duration: 300ms;\n}\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-linear {\n  animation-timing-function: linear;\n}\n/* Custom Animations */\n@keyframes float {\n    0%, 100% {\n      transform: translateY(0px);\n    }\n    50% {\n      transform: translateY(-10px);\n    }\n  }\n@keyframes slideInUp {\n    from {\n      opacity: 0;\n      transform: translateY(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n@keyframes slideInLeft {\n    from {\n      opacity: 0;\n      transform: translateX(-30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n@keyframes slideInRight {\n    from {\n      opacity: 0;\n      transform: translateX(30px);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n@keyframes scaleIn {\n    from {\n      opacity: 0;\n      transform: scale(0.9);\n    }\n    to {\n      opacity: 1;\n      transform: scale(1);\n    }\n  }\n@keyframes shimmer {\n    0% {\n      background-position: -200px 0;\n    }\n    100% {\n      background-position: calc(200px + 100%) 0;\n    }\n  }\n@keyframes confetti {\n    0% {\n      transform: translateY(0) rotate(0deg);\n      opacity: 1;\n    }\n    100% {\n      transform: translateY(-100vh) rotate(720deg);\n      opacity: 0;\n    }\n  }\n/* Animation Classes */\n/* Custom Scrollbar */\n.scrollbar-thin {\n    scrollbar-width: thin;\n    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;\n  }\n.scrollbar-thin::-webkit-scrollbar {\n    width: 6px;\n  }\n.scrollbar-thin::-webkit-scrollbar-track {\n    background: transparent;\n  }\n.scrollbar-thin::-webkit-scrollbar-thumb {\n    background-color: rgba(255, 255, 255, 0.2);\n    border-radius: 3px;\n  }\n.scrollbar-thin::-webkit-scrollbar-thumb:hover {\n    background-color: rgba(255, 255, 255, 0.3);\n  }\n\nbody {\n  font-family: 'PT Sans', sans-serif;\n}\n\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\n\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\n\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\n\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\n\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\n\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\n\n.after\\:-inset-2::after {\n  content: var(--tw-content);\n  inset: -0.5rem;\n}\n\n.after\\:inset-y-0::after {\n  content: var(--tw-content);\n  top: 0px;\n  bottom: 0px;\n}\n\n.after\\:left-1\\/2::after {\n  content: var(--tw-content);\n  left: 50%;\n}\n\n.after\\:w-\\[2px\\]::after {\n  content: var(--tw-content);\n  width: 2px;\n}\n\n.last\\:border-b-0:last-child {\n  border-bottom-width: 0px;\n}\n\n.focus-within\\:relative:focus-within {\n  position: relative;\n}\n\n.focus-within\\:z-20:focus-within {\n  z-index: 20;\n}\n\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n\n.hover\\:bg-destructive\\/10:hover {\n  background-color: hsl(var(--destructive) / 0.1);\n}\n\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\n\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n\n.hover\\:bg-muted:hover {\n  background-color: hsl(var(--muted));\n}\n\n.hover\\:bg-muted\\/50:hover {\n  background-color: hsl(var(--muted) / 0.5);\n}\n\n.hover\\:bg-primary:hover {\n  background-color: hsl(var(--primary));\n}\n\n.hover\\:bg-primary\\/10:hover {\n  background-color: hsl(var(--primary) / 0.1);\n}\n\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\n\n.hover\\:bg-secondary:hover {\n  background-color: hsl(var(--secondary));\n}\n\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n\n.hover\\:bg-sidebar-accent:hover {\n  background-color: hsl(var(--sidebar-accent));\n}\n\n.hover\\:bg-white\\/10:hover {\n  background-color: rgb(255 255 255 / 0.1);\n}\n\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n\n.hover\\:text-destructive:hover {\n  color: hsl(var(--destructive));\n}\n\n.hover\\:text-foreground:hover {\n  color: hsl(var(--foreground));\n}\n\n.hover\\:text-primary:hover {\n  color: hsl(var(--primary));\n}\n\n.hover\\:text-primary-foreground:hover {\n  color: hsl(var(--primary-foreground));\n}\n\n.hover\\:text-primary\\/80:hover {\n  color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:text-sidebar-accent-foreground:hover {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n\n.hover\\:no-underline:hover {\n  text-decoration-line: none;\n}\n\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n\n.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\]:hover {\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:after\\:bg-sidebar-border:hover::after {\n  content: var(--tw-content);\n  background-color: hsl(var(--sidebar-border));\n}\n\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\n\n.focus\\:bg-primary:focus {\n  background-color: hsl(var(--primary));\n}\n\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\n\n.focus\\:text-primary-foreground:focus {\n  color: hsl(var(--primary-foreground));\n}\n\n.focus\\:opacity-100:focus {\n  opacity: 1;\n}\n\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-primary\\/20:focus {\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\n\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus-visible\\:ring-sidebar-ring:focus-visible {\n  --tw-ring-color: hsl(var(--sidebar-ring));\n}\n\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:ring-offset-background:focus-visible {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n\n.active\\:bg-sidebar-accent:active {\n  background-color: hsl(var(--sidebar-accent));\n}\n\n.active\\:text-sidebar-accent-foreground:active {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n\n.group\\/menu-item:focus-within .group-focus-within\\/menu-item\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:text-primary {\n  color: hsl(var(--primary));\n}\n\n.group:hover .group-hover\\:text-primary\\/80 {\n  color: hsl(var(--primary) / 0.8);\n}\n\n.group\\/menu-item:hover .group-hover\\/menu-item\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:border-muted\\/40 {\n  border-color: hsl(var(--muted) / 0.4);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:text-red-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:hover {\n  border-color: hsl(var(--destructive) / 0.3);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:bg-destructive:hover {\n  background-color: hsl(var(--destructive));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:hover {\n  color: hsl(var(--destructive-foreground));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-red-50:hover {\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-destructive:focus {\n  --tw-ring-color: hsl(var(--destructive));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-red-400:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:focus {\n  --tw-ring-offset-color: #dc2626;\n}\n\n.peer\\/menu-button:hover ~ .peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\n\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\n\n.has-\\[\\[data-variant\\=inset\\]\\]\\:bg-sidebar:has([data-variant=inset]) {\n  background-color: hsl(var(--sidebar-background));\n}\n\n.group\\/menu-item:has([data-sidebar=menu-action]) .group-has-\\[\\[data-sidebar\\=menu-action\\]\\]\\/menu-item\\:pr-8 {\n  padding-right: 2rem;\n}\n\n.aria-disabled\\:pointer-events-none[aria-disabled=\"true\"] {\n  pointer-events: none;\n}\n\n.aria-disabled\\:opacity-50[aria-disabled=\"true\"] {\n  opacity: 0.5;\n}\n\n.aria-selected\\:bg-accent[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent));\n}\n\n.aria-selected\\:bg-accent\\/50[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\n\n.aria-selected\\:text-accent-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--accent-foreground));\n}\n\n.aria-selected\\:text-muted-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--muted-foreground));\n}\n\n.aria-selected\\:opacity-100[aria-selected=\"true\"] {\n  opacity: 1;\n}\n\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\n\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=checked\\]\\:translate-x-5[data-state=\"checked\"] {\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=\"cancel\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=\"end\"] {\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=\"move\"] {\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n@keyframes accordion-up {\n\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n\n  to {\n    height: 0;\n  }\n}\n\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"] {\n  animation: accordion-up 0.2s ease-out;\n}\n\n@keyframes accordion-down {\n\n  from {\n    height: 0;\n  }\n\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\n\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"] {\n  animation: accordion-down 0.2s ease-out;\n}\n\n.data-\\[active\\=true\\]\\:bg-sidebar-accent[data-active=\"true\"] {\n  background-color: hsl(var(--sidebar-accent));\n}\n\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"] {\n  background-color: hsl(var(--background));\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\n\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\n\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\n\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"] {\n  background-color: hsl(var(--muted));\n}\n\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\n\n.data-\\[active\\=true\\]\\:font-medium[data-active=\"true\"] {\n  font-weight: 500;\n}\n\n.data-\\[active\\=true\\]\\:text-sidebar-accent-foreground[data-active=\"true\"] {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.data-\\[state\\=active\\]\\:text-foreground[data-state=\"active\"] {\n  color: hsl(var(--foreground));\n}\n\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\n\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"] {\n  color: hsl(var(--accent-foreground));\n}\n\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\n\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\n\n.data-\\[state\\=open\\]\\:opacity-100[data-state=\"open\"] {\n  opacity: 1;\n}\n\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"] {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=\"move\"] {\n  transition-property: none;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\n\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[swipe\\=end\\]\\:animate-out[data-swipe=\"end\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-80[data-state=\"closed\"] {\n  --tw-exit-opacity: 0.8;\n}\n\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\n\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\n\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\n\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\n\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\n\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right-full[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top-full[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\n\n.data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent:hover[data-state=\"open\"] {\n  background-color: hsl(var(--sidebar-accent));\n}\n\n.data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground:hover[data-state=\"open\"] {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n  left: calc(var(--sidebar-width) * -1);\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n  right: calc(var(--sidebar-width) * -1);\n}\n\n.group[data-side=\"left\"] .group-data-\\[side\\=left\\]\\:-right-4 {\n  right: -1rem;\n}\n\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:left-0 {\n  left: 0px;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n  margin-top: -2rem;\n}\n\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:block {\n  display: block;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:hidden {\n  display: none;\n}\n\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:hidden {\n  display: none;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!size-8 {\n  width: 2rem !important;\n  height: 2rem !important;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[--sidebar-width-icon\\] {\n  width: var(--sidebar-width-icon);\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)\\)\\] {\n  width: calc(var(--sidebar-width-icon) + 1rem);\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)_\\+2px\\)\\] {\n  width: calc(var(--sidebar-width-icon) + 1rem + 2px);\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n  width: 0px;\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n  overflow: hidden;\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n  border-radius: var(--radius);\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:border {\n  border-width: 1px;\n}\n\n.group[data-side=\"left\"] .group-data-\\[side\\=left\\]\\:border-r {\n  border-right-width: 1px;\n}\n\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:border-l {\n  border-left-width: 1px;\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n  border-color: hsl(var(--sidebar-border));\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!p-0 {\n  padding: 0px !important;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!p-2 {\n  padding: 0.5rem !important;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n  opacity: 0;\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full::after {\n  content: var(--tw-content);\n  left: 100%;\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:hover\\:bg-sidebar:hover {\n  background-color: hsl(var(--sidebar-background));\n}\n\n.peer\\/menu-button[data-size=\"default\"] ~ .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n  top: 0.375rem;\n}\n\n.peer\\/menu-button[data-size=\"lg\"] ~ .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n  top: 0.625rem;\n}\n\n.peer\\/menu-button[data-size=\"sm\"] ~ .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n  top: 0.25rem;\n}\n\n.peer[data-variant=\"inset\"] ~ .peer-data-\\[variant\\=inset\\]\\:min-h-\\[calc\\(100svh-theme\\(spacing\\.4\\)\\)\\] {\n  min-height: calc(100svh - 1rem);\n}\n\n.peer\\/menu-button[data-active=\"true\"] ~ .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.dark\\:border-destructive:is(.dark *) {\n  border-color: hsl(var(--destructive));\n}\n\n@media (min-width: 640px) {\n\n  .sm\\:bottom-0 {\n    bottom: 0px;\n  }\n\n  .sm\\:right-0 {\n    right: 0px;\n  }\n\n  .sm\\:top-auto {\n    top: auto;\n  }\n\n  .sm\\:mr-2 {\n    margin-right: 0.5rem;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:flex {\n    display: flex;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n\n  .sm\\:h-5 {\n    height: 1.25rem;\n  }\n\n  .sm\\:h-9 {\n    height: 2.25rem;\n  }\n\n  .sm\\:w-5 {\n    width: 1.25rem;\n  }\n\n  .sm\\:w-9 {\n    width: 2.25rem;\n  }\n\n  .sm\\:max-w-\\[425px\\] {\n    max-width: 425px;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col {\n    flex-direction: column;\n  }\n\n  .sm\\:items-center {\n    align-items: center;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .sm\\:gap-3 {\n    gap: 0.75rem;\n  }\n\n  .sm\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:space-y-8 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: var(--radius);\n  }\n\n  .sm\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:py-6 {\n    padding-top: 1.5rem;\n    padding-bottom: 1.5rem;\n  }\n\n  .sm\\:py-8 {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .sm\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .sm\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .data-\\[state\\=open\\]\\:sm\\:slide-in-from-bottom-full[data-state=\"open\"] {\n    --tw-enter-translate-y: 100%;\n  }\n}\n\n@media (min-width: 768px) {\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:max-w-\\[420px\\] {\n    max-width: 420px;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:opacity-0 {\n    opacity: 0;\n  }\n\n  .after\\:md\\:hidden::after {\n    content: var(--tw-content);\n    display: none;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    margin: 0.5rem;\n  }\n\n  .peer[data-state=\"collapsed\"][data-variant=\"inset\"] ~ .md\\:peer-data-\\[state\\=collapsed\\]\\:peer-data-\\[variant\\=inset\\]\\:ml-2 {\n    margin-left: 0.5rem;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    margin-left: 0px;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    border-radius: 0.75rem;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:shadow {\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  }\n}\n\n@media (min-width: 1024px) {\n\n  .lg\\:ml-80 {\n    margin-left: 20rem;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n\n@media (min-width: 1280px) {\n\n  .xl\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .xl\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n}\n\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]) {\n  background-color: hsl(var(--accent));\n}\n\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child {\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\n\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\n\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside) {\n  background-color: hsl(var(--accent) / 0.5);\n}\n\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end) {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\n\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\n\n.\\[\\&\\>\\*\\]\\:bg-destructive>* {\n  background-color: hsl(var(--destructive));\n}\n\n.\\[\\&\\>\\*\\]\\:bg-primary>* {\n  background-color: hsl(var(--primary));\n}\n\n.\\[\\&\\>button\\]\\:hidden>button {\n  display: none;\n}\n\n.\\[\\&\\>span\\:last-child\\]\\:truncate>span:last-child {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.\\[\\&\\>span\\]\\:line-clamp-1>span {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div {\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&\\>svg\\]\\:absolute>svg {\n  position: absolute;\n}\n\n.\\[\\&\\>svg\\]\\:left-4>svg {\n  left: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:top-4>svg {\n  top: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:h-2\\.5>svg {\n  height: 0.625rem;\n}\n\n.\\[\\&\\>svg\\]\\:h-3>svg {\n  height: 0.75rem;\n}\n\n.\\[\\&\\>svg\\]\\:w-2\\.5>svg {\n  width: 0.625rem;\n}\n\n.\\[\\&\\>svg\\]\\:w-3>svg {\n  width: 0.75rem;\n}\n\n.\\[\\&\\>svg\\]\\:shrink-0>svg {\n  flex-shrink: 0;\n}\n\n.\\[\\&\\>svg\\]\\:text-destructive>svg {\n  color: hsl(var(--destructive));\n}\n\n.\\[\\&\\>svg\\]\\:text-foreground>svg {\n  color: hsl(var(--foreground));\n}\n\n.\\[\\&\\>svg\\]\\:text-muted-foreground>svg {\n  color: hsl(var(--muted-foreground));\n}\n\n.\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground>svg {\n  color: hsl(var(--sidebar-accent-foreground));\n}\n\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~* {\n  padding-left: 1.75rem;\n}\n\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\n\n.\\[\\&\\[data-state\\=open\\]\\>\\.accordion-trigger-chevron\\]\\:rotate-180[data-state=open]>.accordion-trigger-chevron {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text {\n  fill: hsl(var(--muted-foreground));\n}\n\n.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke='#ccc'] {\n  stroke: hsl(var(--border) / 0.5);\n}\n\n.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor {\n  stroke: hsl(var(--border));\n}\n\n.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke='#fff'] {\n  stroke: transparent;\n}\n\n.\\[\\&_\\.recharts-layer\\]\\:outline-none .recharts-layer {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke='#ccc'] {\n  stroke: hsl(var(--border));\n}\n\n.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector {\n  fill: hsl(var(--muted));\n}\n\n.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {\n  fill: hsl(var(--muted));\n}\n\n.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke='#ccc'] {\n  stroke: hsl(var(--border));\n}\n\n.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke='#fff'] {\n  stroke: transparent;\n}\n\n.\\[\\&_\\.recharts-sector\\]\\:outline-none .recharts-sector {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.\\[\\&_\\.recharts-surface\\]\\:outline-none .recharts-surface {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.\\[\\&_p\\]\\:leading-relaxed p {\n  line-height: 1.625;\n}\n\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\n\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\n\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\n\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}\n\n[data-side=left][data-collapsible=offcanvas] .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n  right: -0.5rem;\n}\n\n[data-side=left][data-state=collapsed] .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n  cursor: e-resize;\n}\n\n[data-side=left] .\\[\\[data-side\\=left\\]_\\&\\]\\:cursor-w-resize {\n  cursor: w-resize;\n}\n\n[data-side=right][data-collapsible=offcanvas] .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n  left: -0.5rem;\n}\n\n[data-side=right][data-state=collapsed] .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n  cursor: w-resize;\n}\n\n[data-side=right] .\\[\\[data-side\\=right\\]_\\&\\]\\:cursor-e-resize {\n  cursor: e-resize;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAGE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;;;;AAGA;;;;;;;;AAOA;;;;;AAIA;EAEA;;;;;;AAKF;;;;;;;;AAOA;EAEE;;;;;AAKF;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AAMA;;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;;;;;;;;;;;;;;AAiBA;;;;AAGA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;;;;;;;;;;;AAWA;;;;;;;;AAOA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;;;;;AAQA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAWA;;;;AAIA;;;;;;;;;;AAWA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;AAOF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;;AAKF;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA"}}]}