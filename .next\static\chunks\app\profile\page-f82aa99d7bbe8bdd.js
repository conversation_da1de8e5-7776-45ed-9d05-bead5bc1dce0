(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{29100:(e,s,a)=>{"use strict";a.d(s,{Separator:()=>o});var r=a(95155),l=a(12115),t=a(63655),i="horizontal",n=["horizontal","vertical"],d=l.forwardRef((e,s)=>{var a;let{decorative:l,orientation:d=i,...c}=e,o=(a=d,n.includes(a))?d:i;return(0,r.jsx)(t.sG.div,{"data-orientation":o,...l?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:s})});d.displayName="Separator";var c=a(59434);let o=l.forwardRef((e,s)=>{let{className:a,orientation:l="horizontal",decorative:t=!0,...i}=e;return(0,r.jsx)(d,{ref:s,decorative:t,orientation:l,className:(0,c.cn)("shrink-0 bg-border","horizontal"===l?"h-[1px] w-full":"h-full w-[1px]",a),...i})});o.displayName=d.displayName},36682:(e,s,a)=>{Promise.resolve().then(a.bind(a,85752))},68856:(e,s,a)=>{"use strict";a.d(s,{EA:()=>t,eX:()=>d});var r=a(95155),l=a(59434);function t(e){let{className:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",s),...a})}function i(){return(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(t,{className:"h-5 w-5 rounded"}),(0,r.jsx)(t,{className:"h-5 w-32"})]}),(0,r.jsx)(t,{className:"h-8 w-20 rounded-md"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(t,{className:"h-4 w-24"}),(0,r.jsx)(t,{className:"h-4 w-16"})]}),(0,r.jsx)(t,{className:"h-2 w-full rounded-full"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(t,{className:"h-4 w-20"}),(0,r.jsx)(t,{className:"h-4 w-16"})]})]})]})}function n(){return(0,r.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)(t,{className:"h-5 w-5 rounded"}),(0,r.jsx)(t,{className:"h-5 w-32"})]}),(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)(t,{className:"h-48 w-48 rounded-full"})}),(0,r.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2 justify-center",children:[(0,r.jsx)(t,{className:"h-4 w-16 rounded-full"}),(0,r.jsx)(t,{className:"h-4 w-20 rounded-full"}),(0,r.jsx)(t,{className:"h-4 w-14 rounded-full"}),(0,r.jsx)(t,{className:"h-4 w-18 rounded-full"})]})]})}function d(){return(0,r.jsx)("div",{className:"container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,r.jsxs)("div",{className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,r.jsx)(i,{}),(0,r.jsx)(i,{}),(0,r.jsx)(i,{})]}),(0,r.jsxs)("div",{className:"md:col-span-2 space-y-4 sm:space-y-6",children:[(0,r.jsx)(i,{}),(0,r.jsx)(n,{})]})]})})}},77223:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},85752:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>R});var r=a(95155),l=a(12115),t=a(35695),i=a(90925),n=a(44321),d=a(62177),c=a(90221),o=a(55594),m=a(30285),u=a(62523),x=a(85057),p=a(17759),h=a(87481),j=a(66695),f=a(40157);let w=(0,f.A)("CircleUser",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]),y=o.Ik({displayName:o.Yj().min(1,{message:"Display name cannot be empty."}).max(50,{message:"Display name must be 50 characters or less."}),email:o.Yj().email().optional()});function N(){let{currentUser:e,updateUserProfile:s,loading:a,error:t,clearError:n}=(0,i.A)(),{toast:o}=(0,h.dj)(),f=(0,d.mN)({resolver:(0,c.u)(y),defaultValues:{displayName:"",email:""}});(0,l.useEffect)(()=>{e&&f.reset({displayName:e.displayName||"",email:e.email||""})},[e,f]);let N=async a=>{if(e){n();try{await s(a.displayName,void 0),o({title:"Profile Updated",description:"Your display name has been updated."}),f.reset({},{keepValues:!0,keepDirty:!1,keepDefaultValues:!1})}catch(e){o({variant:"destructive",title:"Update Failed",description:t||e.message||"Could not update profile."})}}};return(0,r.jsxs)(j.Zp,{children:[(0,r.jsxs)(j.aR,{children:[(0,r.jsxs)(j.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,r.jsx)(w,{className:"h-5 w-5 text-primary"}),"Profile Information"]}),(0,r.jsx)(j.BT,{children:"Update your display name and view your email address."})]}),(0,r.jsx)(j.Wu,{children:(0,r.jsx)(p.lV,{...f,children:(0,r.jsxs)("form",{onSubmit:f.handleSubmit(N),className:"space-y-6",children:[(0,r.jsx)(p.zB,{control:f.control,name:"displayName",render:e=>{let{field:s}=e;return(0,r.jsxs)(p.eI,{children:[(0,r.jsx)(x.J,{htmlFor:"displayName",children:"Display Name"}),(0,r.jsx)(p.MJ,{children:(0,r.jsx)(u.p,{id:"displayName",placeholder:"Your display name",...s})}),(0,r.jsx)(p.C5,{})]})}}),(0,r.jsx)(p.zB,{control:f.control,name:"email",render:e=>{let{field:s}=e;return(0,r.jsxs)(p.eI,{children:[(0,r.jsx)(x.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(p.MJ,{children:(0,r.jsx)(u.p,{id:"email",type:"email",...s,readOnly:!0,disabled:!0,className:"bg-muted/50 cursor-not-allowed"})}),(0,r.jsx)(p.Rr,{children:"Your email address cannot be changed here."}),(0,r.jsx)(p.C5,{})]})}}),(0,r.jsx)(m.$,{type:"submit",disabled:a||!f.formState.isDirty,children:a?"Saving...":"Save Name Changes"})]})})})]})}var v=a(91394);let g=(0,f.A)("ImageUp",[["path",{d:"M10.3 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10l-3.1-3.1a2 2 0 0 0-2.814.014L6 21",key:"9csbqa"}],["path",{d:"m14 19.5 3-3 3 3",key:"9vmjn0"}],["path",{d:"M17 22v-5.5",key:"1aa6fl"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]]);var b=a(77223);function P(){let{currentUser:e,updateUserProfile:s,loading:a,error:t,clearError:n}=(0,i.A)(),[d,c]=(0,l.useState)(null),[o,p]=(0,l.useState)(null),f=(0,l.useRef)(null),{toast:w}=(0,h.dj)();(0,l.useEffect)(()=>()=>{o&&o.startsWith("blob:")&&URL.revokeObjectURL(o)},[o]);let y=async()=>{if(!d){w({variant:"destructive",title:"No file selected",description:"Please select an image to upload."});return}if(e){n();try{await s(e.displayName,d),w({title:"Profile Picture Updated",description:"Your new profile picture has been saved."}),c(null),p(null),f.current&&(f.current.value="")}catch(e){w({variant:"destructive",title:"Upload Failed",description:t||e.message||"Could not upload profile picture."})}}},N=async()=>{if(e){if(!e.photoURL){w({title:"No Picture to Remove",description:"You do not have a profile picture set."});return}n();try{await s(e.displayName,null),w({title:"Profile Picture Removed",description:"Your profile picture has been removed."}),c(null),p(null),f.current&&(f.current.value="")}catch(e){w({variant:"destructive",title:"Removal Failed",description:t||e.message||"Could not remove profile picture."})}}};return(0,r.jsxs)(j.Zp,{children:[(0,r.jsxs)(j.aR,{children:[(0,r.jsxs)(j.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,r.jsx)(g,{className:"h-5 w-5 text-primary"}),"Profile Picture"]}),(0,r.jsx)(j.BT,{children:"Upload, change, or remove your profile picture. Max 5MB."})]}),(0,r.jsxs)(j.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsxs)(v.eu,{className:"h-32 w-32 text-4xl",children:[(0,r.jsx)(v.BK,{src:o||(null==e?void 0:e.photoURL)||void 0,alt:(null==e?void 0:e.displayName)||"User"}),(0,r.jsx)(v.q5,{children:((e,s)=>{if(s){let e=s.split(" ").filter(Boolean);return e.length>1?(e[0][0]+e[1][0]).toUpperCase():s.substring(0,2).toUpperCase()}return e?e.substring(0,2).toUpperCase():"U"})(null==e?void 0:e.email,null==e?void 0:e.displayName)})]}),(0,r.jsxs)("div",{className:"w-full max-w-xs space-y-2",children:[(0,r.jsx)(x.J,{htmlFor:"profilePictureFile",className:"sr-only",children:"Choose profile picture"}),(0,r.jsx)(u.p,{id:"profilePictureFile",type:"file",accept:"image/png, image/jpeg, image/gif",onChange:e=>{var s;let a=null===(s=e.target.files)||void 0===s?void 0:s[0];if(a){if(a.size>5242880){w({variant:"destructive",title:"File too large",description:"Please select an image smaller than 5MB."}),f.current&&(f.current.value=""),c(null),p(null);return}c(a),o&&o.startsWith("blob:")&&URL.revokeObjectURL(o),p(URL.createObjectURL(a))}else c(null),p(null)},ref:f,disabled:a})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 justify-center",children:[d&&(0,r.jsxs)(m.$,{onClick:y,disabled:a||!d,className:"flex-grow",children:[(0,r.jsx)(g,{className:"mr-2 h-4 w-4"}),a?"Uploading...":"Upload Picture"]}),(null==e?void 0:e.photoURL)&&(0,r.jsxs)(m.$,{onClick:N,variant:"outline",className:"flex-grow",disabled:a,children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"})," Remove Picture"]})]})]})]})}let S=(0,f.A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),k=o.Ik({currentPassword:o.Yj().min(1,{message:"Current password is required."}),newPassword:o.Yj().min(6,{message:"New password must be at least 6 characters."}),confirmNewPassword:o.Yj()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"New passwords don't match.",path:["confirmNewPassword"]});function A(){let{changeUserPassword:e,loading:s,error:a,clearError:l,currentUser:t}=(0,i.A)(),{toast:n}=(0,h.dj)(),o=(0,d.mN)({resolver:(0,c.u)(k),defaultValues:{currentPassword:"",newPassword:"",confirmNewPassword:""}}),f=null==t?void 0:t.providerData.some(e=>"password"===e.providerId),w=async s=>{l();try{await e(s.currentPassword,s.newPassword),n({title:"Password Changed",description:"Your password has been updated successfully."}),o.reset()}catch(e){n({variant:"destructive",title:"Password Change Failed",description:a||e.message||"Could not change password."})}};return f?(0,r.jsxs)(j.Zp,{children:[(0,r.jsxs)(j.aR,{children:[(0,r.jsxs)(j.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,r.jsx)(S,{className:"h-5 w-5 text-primary"}),"Change Password"]}),(0,r.jsx)(j.BT,{children:"Update your account password."})]}),(0,r.jsx)(j.Wu,{children:(0,r.jsx)(p.lV,{...o,children:(0,r.jsxs)("form",{onSubmit:o.handleSubmit(w),className:"space-y-6",children:[(0,r.jsx)(p.zB,{control:o.control,name:"currentPassword",render:e=>{let{field:s}=e;return(0,r.jsxs)(p.eI,{children:[(0,r.jsx)(x.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,r.jsx)(p.MJ,{children:(0,r.jsx)(u.p,{id:"currentPassword",type:"password",placeholder:"••••••••",...s})}),(0,r.jsx)(p.C5,{})]})}}),(0,r.jsx)(p.zB,{control:o.control,name:"newPassword",render:e=>{let{field:s}=e;return(0,r.jsxs)(p.eI,{children:[(0,r.jsx)(x.J,{htmlFor:"newPassword",children:"New Password"}),(0,r.jsx)(p.MJ,{children:(0,r.jsx)(u.p,{id:"newPassword",type:"password",placeholder:"••••••••",...s})}),(0,r.jsx)(p.C5,{})]})}}),(0,r.jsx)(p.zB,{control:o.control,name:"confirmNewPassword",render:e=>{let{field:s}=e;return(0,r.jsxs)(p.eI,{children:[(0,r.jsx)(x.J,{htmlFor:"confirmNewPassword",children:"Confirm New Password"}),(0,r.jsx)(p.MJ,{children:(0,r.jsx)(u.p,{id:"confirmNewPassword",type:"password",placeholder:"••••••••",...s})}),(0,r.jsx)(p.C5,{})]})}}),(0,r.jsx)(m.$,{type:"submit",disabled:s,children:s?"Updating Password...":"Update Password"})]})})})]}):(0,r.jsxs)(j.Zp,{children:[(0,r.jsx)(j.aR,{children:(0,r.jsxs)(j.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,r.jsx)(S,{className:"h-5 w-5 text-primary"}),"Change Password"]})}),(0,r.jsx)(j.Wu,{children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"You signed in using a social provider (e.g., Google). Password changes are managed through your social provider account."})})]})}var C=a(29100),U=a(68856);function R(){let{currentUser:e,loading:s}=(0,i.A)(),a=(0,t.useRouter)();return((0,l.useEffect)(()=>{s||e||a.push("/login")},[e,s,a]),s||!e)?(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,r.jsx)(n.default,{title:"BudgetWise"}),(0,r.jsx)("main",{className:"flex-grow container mx-auto py-6 sm:py-8 px-2 sm:px-4",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6 sm:space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(U.EA,{className:"h-8 w-1/2 mb-2"}),(0,r.jsx)(U.EA,{className:"h-4 w-3/4"})]}),(0,r.jsx)(C.Separator,{}),(0,r.jsx)(U.EA,{className:"h-60 w-full"}),(0,r.jsx)(U.EA,{className:"h-60 w-full"}),(0,r.jsx)(U.EA,{className:"h-60 w-full"})]})})]}):(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,r.jsx)(n.default,{title:"BudgetWise"}),(0,r.jsx)("main",{className:"flex-grow container mx-auto py-6 sm:py-8 px-2 sm:px-4",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6 sm:space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-headline font-bold text-foreground",children:"Profile Settings"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your account details and preferences."})]}),(0,r.jsx)(C.Separator,{}),(0,r.jsx)(N,{}),(0,r.jsx)(C.Separator,{}),(0,r.jsx)(P,{}),(0,r.jsx)(C.Separator,{}),(0,r.jsx)(A,{})]})})]})}},87481:(e,s,a)=>{"use strict";a.d(s,{dj:()=>u});var r=a(12115);let l=0,t=new Map,i=e=>{if(t.has(e))return;let s=setTimeout(()=>{t.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);t.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=s;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},d=[],c={toasts:[]};function o(e){c=n(c,e),d.forEach(e=>{e(c)})}function m(e){let{...s}=e,a=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>o({type:"DISMISS_TOAST",toastId:a});return o({type:"ADD_TOAST",toast:{...s,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){let[e,s]=r.useState(c);return r.useEffect(()=>(d.push(s),()=>{let e=d.indexOf(s);e>-1&&d.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var s=s=>e(e.s=s);e.O(0,[73,671,440,521,422,59,375,321,441,684,358],()=>s(36682)),_N_E=e.O()}]);