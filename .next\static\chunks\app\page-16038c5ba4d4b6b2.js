(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{17153:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>aj});var s=t(95155),l=t(12115),n=t(35695),r=t(60760),i=t(1978),o=t(44321),d=t(62523),c=t(85057),m=t(66695),u=t(19591),x=t(80659),h=t(88390);function p(e){let{totalIncome:a,onIncomeChange:t,balancesVisible:n}=e,[r,o]=(0,l.useState)("");return(0,l.useEffect)(()=>{n?o(a.toString()):o("••••")},[a,n]),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,s.jsxs)(m.Zp,{className:"card-modern hover-glow group",children:[(0,s.jsx)(m.aR,{className:"pb-4",children:(0,s.jsxs)(i.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,type:"spring"},className:"flex items-center justify-between",children:[(0,s.jsxs)(m.ZB,{className:"text-xl flex items-center gap-3 font-headline text-gradient-primary",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:"p-2 rounded-xl bg-gradient-to-r from-primary/20 to-accent/20 group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300",children:(0,s.jsx)(u.A,{className:"h-6 w-6 text-primary"})}),"Total Monthly Income"]}),(0,s.jsx)(i.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,repeatDelay:4},className:"p-2 rounded-full bg-success/20",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-success"})})]})}),(0,s.jsxs)(m.Wu,{className:"space-y-4",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},className:"relative",children:[(0,s.jsx)(c.J,{htmlFor:"totalIncome",className:"sr-only",children:"Total Income"}),(0,s.jsxs)("div",{className:"relative flex items-center",children:[(0,s.jsx)(i.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.6,type:"spring"},className:"absolute left-4 text-2xl font-bold text-gradient-primary z-10",children:"R"}),(0,s.jsx)(d.p,{id:"totalIncome",type:n?"number":"text",placeholder:"Enter your monthly income...",value:r,onChange:e=>{let a=e.target.value;n&&o(a);let s=""===a?0:parseFloat(a);!isNaN(s)&&s>=0?t(s):""===a&&t(0)},className:"input-modern text-2xl h-16 pl-12 pr-6 font-semibold text-center bg-gradient-to-r from-card/50 to-card/80 border-2 focus:border-primary/50 focus:shadow-lg focus:shadow-primary/20",min:"0",step:"any",readOnly:!n&&"••••"===r}),a>0&&n&&(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.8},className:"absolute right-4 top-1/2 -translate-y-1/2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1 rounded-full bg-success/20 border border-success/30",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-success animate-pulse"}),(0,s.jsx)("span",{className:"text-xs font-medium text-success",children:"Active"})]})})]})]}),a>0&&n&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1},className:"glass-premium p-4 rounded-xl",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-success/20 to-primary/20",children:(0,s.jsx)(h.A,{className:"h-4 w-4 text-success"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground",children:"Monthly Budget"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for allocation"})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-gradient-primary",children:["R ",a.toLocaleString("en-ZA",{minimumFractionDigits:2})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["~R ",(a/30).toFixed(0)," per day"]})]})]})}),!a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"text-center py-6",children:[(0,s.jsx)(i.P.div,{animate:{y:[0,-5,0]},transition:{duration:2,repeat:1/0},className:"inline-block p-4 rounded-full bg-gradient-to-r from-muted/20 to-muted/10 mb-3",children:(0,s.jsx)(u.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your monthly income to start budgeting"})]})]})]})})}var g=t(59434);let f={default:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500",destructive:"bg-red-500",primary:"bg-primary"},j={sm:"h-1",md:"h-2",lg:"h-3"};function v(e){let{value:a=0,max:t=100,className:l,indicatorClassName:n,showValue:r=!1,showPercentage:o=!1,animated:d=!0,variant:c="default",size:m="md",label:u,formatValue:x,...h}=e,p=Math.min(Math.max(a/t*100,0),100),v=x?x(a,t):o?"".concat(Math.round(p),"%"):"".concat(a,"/").concat(t);return(0,s.jsxs)("div",{className:(0,g.cn)("w-full",l),...h,children:[(u||r||o)&&(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[u&&(0,s.jsx)("span",{className:"text-sm font-medium text-foreground",children:u}),(r||o)&&(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:v})]}),(0,s.jsxs)("div",{className:(0,g.cn)("relative w-full overflow-hidden rounded-full bg-muted",j[m]),children:[d?(0,s.jsx)(i.P.div,{className:(0,g.cn)("h-full rounded-full transition-colors",f[c],n),initial:{width:0},animate:{width:"".concat(p,"%")},transition:{duration:1,ease:"easeOut"}}):(0,s.jsx)("div",{className:(0,g.cn)("h-full rounded-full transition-all duration-500 ease-out",f[c],n),style:{width:"".concat(p,"%")}}),d&&p>0&&p<100&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer",style:{backgroundSize:"200% 100%"}})]})]})}function y(e){let{value:a=0,max:t=100,size:l=120,strokeWidth:n=8,className:r,variant:o="default",showValue:d=!1,showPercentage:c=!0,animated:m=!0,children:u}=e,x=Math.min(Math.max(a/t*100,0),100),h=(l-n)/2,p=2*h*Math.PI,f=p-x/100*p,j={default:"stroke-primary",success:"stroke-green-500",warning:"stroke-yellow-500",destructive:"stroke-red-500",primary:"stroke-primary"};return(0,s.jsxs)("div",{className:(0,g.cn)("relative inline-flex items-center justify-center",r),children:[(0,s.jsxs)("svg",{width:l,height:l,className:"transform -rotate-90",children:[(0,s.jsx)("circle",{cx:l/2,cy:l/2,r:h,stroke:"currentColor",strokeWidth:n,fill:"none",className:"text-muted opacity-20"}),m?(0,s.jsx)(i.P.circle,{cx:l/2,cy:l/2,r:h,strokeWidth:n,fill:"none",strokeDasharray:p,strokeLinecap:"round",className:(0,g.cn)("transition-colors",j[o]),initial:{strokeDashoffset:p},animate:{strokeDashoffset:f},transition:{duration:1.5,ease:"easeOut"}}):(0,s.jsx)("circle",{cx:l/2,cy:l/2,r:h,strokeWidth:n,fill:"none",strokeDasharray:p,strokeDashoffset:f,strokeLinecap:"round",className:(0,g.cn)("transition-all duration-500 ease-out",j[o])})]}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:u||(0,s.jsxs)("div",{className:"text-center",children:[c&&(0,s.jsxs)("div",{className:"text-2xl font-bold text-foreground",children:[Math.round(x),"%"]}),d&&(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:[a,"/",t]})]})})]})}var b=t(81203),N=t(31949),w=t(49940),A=t(89829);function C(e){let{totalIncome:a,overallTotalAllocated:t,balancesVisible:l}=e,n=a-t,r=a>0?t/a*100:0,o=n<0,d=e=>l?"R ".concat(e.toFixed(2)):"R ••••";return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:(0,s.jsxs)(m.Zp,{className:"card-modern hover-glow group",children:[(0,s.jsx)(m.aR,{className:"pb-4",children:(0,s.jsxs)(i.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.3,type:"spring"},className:"flex items-center justify-between",children:[(0,s.jsxs)(m.ZB,{className:"text-xl flex items-center gap-3 font-headline text-gradient-accent",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,360]},transition:{duration:3,repeat:1/0,ease:"linear"},className:"p-2 rounded-xl bg-gradient-to-r from-accent/20 to-primary/20 group-hover:from-accent/30 group-hover:to-primary/30 transition-all duration-300",children:(0,s.jsx)(b.A,{className:"h-6 w-6 text-accent"})}),"Budget Overview"]}),(0,s.jsx)(i.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:"p-2 rounded-full ".concat(o?"bg-destructive/20":"bg-success/20"),children:o?(0,s.jsx)(N.A,{className:"h-5 w-5 text-destructive"}):(0,s.jsx)(w.A,{className:"h-5 w-5 text-success"})})]})}),(0,s.jsxs)(m.Wu,{className:"space-y-6",children:[(0,s.jsx)(i.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.5,type:"spring"},className:"flex justify-center",children:(0,s.jsx)(y,{value:t,max:a,size:120,strokeWidth:8,variant:o?"destructive":"success",showPercentage:!0,animated:!0,children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"text-lg font-bold text-foreground",children:[Math.round(r),"%"]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Allocated"})]})})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"grid grid-cols-1 gap-4",children:[(0,s.jsx)("div",{className:"glass-premium p-4 rounded-xl",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-primary/20 to-accent/20",children:(0,s.jsx)(h.A,{className:"h-4 w-4 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Income"}),(0,s.jsx)("p",{className:"text-lg font-bold text-gradient-primary",children:d(a)})]})]})})}),(0,s.jsx)("div",{className:"glass-premium p-4 rounded-xl",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-accent/20 to-primary/20",children:(0,s.jsx)(x.A,{className:"h-4 w-4 text-accent"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Allocated"}),(0,s.jsx)("p",{className:"text-lg font-bold text-gradient-accent",children:d(t)})]})]})})}),(0,s.jsx)("div",{className:"glass-premium p-4 rounded-xl border-2 ".concat(o?"border-destructive/30":"border-success/30"),children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(o?"bg-destructive/20":"bg-success/20"),children:(0,s.jsx)(A.A,{className:"h-4 w-4 ".concat(o?"text-destructive":"text-success")})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:o?"Over Budget":"Remaining"}),(0,s.jsx)("p",{className:"text-lg font-bold ".concat(o?"text-destructive":"text-success"),children:d(Math.abs(n))})]})]})})})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,scaleX:0},animate:{opacity:1,scaleX:1},transition:{delay:.9,duration:.8},children:(0,s.jsx)(v,{value:t,max:a,variant:o?"destructive":"success",size:"lg",animated:!0,showPercentage:!0,label:"Budget Allocation Progress"})}),o&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:1.1},className:"status-error flex items-center gap-3 p-4 rounded-xl",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.5,repeat:1/0,repeatDelay:2},children:(0,s.jsx)(N.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold",children:"Budget Exceeded!"}),(0,s.jsxs)("p",{className:"text-sm opacity-90",children:["You've allocated R ",Math.abs(n).toFixed(2)," more than your income."]})]})]}),!o&&a>0&&t<=a&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:1.1},className:"status-success flex items-center gap-3 p-4 rounded-xl",children:[(0,s.jsx)(i.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:1,repeat:1/0,repeatDelay:2},children:(0,s.jsx)(w.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-semibold",children:"Budget Balanced!"}),(0,s.jsx)("p",{className:"text-sm opacity-90",children:"Your budget is well-managed with funds remaining."})]})]})]})]})})}var S=t(30285),k=t(23478),R=t(79556);let T=k.bL,P=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(k.q7,{ref:a,className:(0,g.cn)("border-b",t),...l})});P.displayName="AccordionItem";let E=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsx)(k.Y9,{className:"flex",children:(0,s.jsxs)(k.l9,{ref:a,className:(0,g.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>.accordion-trigger-chevron]:rotate-180",t),...n,children:[l,(0,s.jsx)(R.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200 accordion-trigger-chevron"})]})})});E.displayName=k.l9.displayName;let I=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsx)(k.UC,{ref:a,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...n,children:(0,s.jsx)("div",{className:(0,g.cn)("pb-4 pt-0",t),children:l})})});I.displayName=k.UC.displayName;var D=t(15452),V=t(25318);let _=D.bL,O=D.l9,B=D.ZL;D.bm;let M=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(D.hJ,{ref:a,className:(0,g.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l})});M.displayName=D.hJ.displayName;let G=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(B,{children:[(0,s.jsx)(M,{}),(0,s.jsxs)(D.UC,{ref:a,className:(0,g.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n,children:[l,(0,s.jsxs)(D.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(V.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});G.displayName=D.UC.displayName;let L=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,g.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};L.displayName="DialogHeader";let F=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(D.hE,{ref:a,className:(0,g.cn)("text-lg font-semibold leading-none tracking-tight",t),...l})});F.displayName=D.hE.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(D.VY,{ref:a,className:(0,g.cn)("text-sm text-muted-foreground",t),...l})}).displayName=D.VY.displayName;var U=t(15222),z=t(77223);function Y(e){let{subCategory:a,onEdit:t,onDelete:l,balancesVisible:n}=e;return(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 border-b border-border/50 last:border-b-0",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:a.name}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Allocated: ",n?"R ".concat(a.allocatedAmount.toFixed(2)):"R ••••"]})]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsxs)(S.$,{variant:"ghost",size:"icon",onClick:t,className:"h-7 w-7",children:[(0,s.jsx)(U.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Edit Subcategory"})]}),(0,s.jsxs)(S.$,{variant:"ghost",size:"icon",onClick:l,className:"h-7 w-7 text-destructive hover:text-destructive",children:[(0,s.jsx)(z.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Delete Subcategory"})]})]})]})}var Z=t(90221),H=t(62177),W=t(55594),$=t(17759);let J=W.Ik({name:W.Yj().min(1,{message:"Subcategory name is required."}).max(50,{message:"Name must be 50 characters or less."}),allocatedAmount:W.vk(e=>"string"==typeof e?parseFloat(e):e,W.ai().min(0,{message:"Allocated amount must be a positive number."}))});function K(e){let{onSubmit:a,initialData:t,onClose:l,parentCategoryName:n,balancesVisible:r}=e,i=(0,H.mN)({resolver:(0,Z.u)(J),defaultValues:{name:(null==t?void 0:t.name)||"",allocatedAmount:(null==t?void 0:t.allocatedAmount)||0}});return(0,s.jsx)($.lV,{...i,children:(0,s.jsxs)("form",{onSubmit:i.handleSubmit(e=>{a(e)&&(i.reset(),l())}),className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Adding to: ",(0,s.jsx)("span",{className:"font-semibold",children:n})]}),(0,s.jsx)($.zB,{control:i.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Subcategory Name"}),(0,s.jsx)($.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Fruits & Vegetables",...a})}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsx)($.zB,{control:i.control,name:"allocatedAmount",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Allocated Amount (R)"}),(0,s.jsx)($.MJ,{children:(0,s.jsx)(d.p,{type:r?"number":"password",placeholder:r?"e.g., 100":"••••",...a,value:r?a.value:a.value>0?"••••":"0",onChange:e=>{if(r)a.onChange(""===e.target.value?0:parseFloat(e.target.value));else{let t=parseFloat(e.target.value);isNaN(t)?""===e.target.value&&a.onChange(0):a.onChange(t)}},step:"any"})}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,s.jsx)(S.$,{type:"button",variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(S.$,{type:"submit",children:(null==t?void 0:t.id)?"Save Changes":"Add Subcategory"})]})]})})}var q=t(2682),X=t(43369),Q=t(34301),ee=t(55863);let ea=l.forwardRef((e,a)=>{let{className:t,value:l,...n}=e;return(0,s.jsx)(ee.bL,{ref:a,className:(0,g.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...n,children:(0,s.jsx)(ee.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});ea.displayName=ee.bL.displayName;var et=t(87481);function es(e){let{category:a,onAddSubCategory:t,onUpdateSubCategory:n,onDeleteSubCategory:r,balancesVisible:i,categoryIsVisible:o}=e,[d,c]=(0,l.useState)(!1),[u,x]=(0,l.useState)(null),{toast:h}=(0,et.dj)(),p=i&&o,g=a.subCategories.reduce((e,a)=>e+a.allocatedAmount,0),f=a.budget-g,j=f<0,v=a.budget>0?g/a.budget*100:0,y=e=>p?"R ".concat(e.toFixed(2)):"R ••••";return(0,s.jsxs)(m.Zp,{className:"mb-3",children:[(0,s.jsx)(m.aR,{className:"flex flex-row items-start justify-between pb-2 pt-3 px-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)(m.ZB,{className:"text-base font-headline",children:a.name}),(0,s.jsxs)(m.BT,{className:"text-xs",children:["Budget: ",y(a.budget)]})]})}),(0,s.jsxs)(m.Wu,{className:"px-4 pb-3 space-y-2",children:[(0,s.jsxs)("div",{className:"text-xs space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Subcategories Total:"}),(0,s.jsx)("span",{children:y(g)})]}),(0,s.jsx)(ea,{value:Math.min(v,100),className:j?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary"}),(0,s.jsxs)("div",{className:"flex justify-between font-medium ".concat(j?"text-destructive":"text-green-600"),children:[(0,s.jsx)("span",{children:"Remaining in Budget:"}),(0,s.jsx)("span",{children:y(f)})]})]}),j&&p&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs p-1.5 bg-destructive/10 rounded-md",children:[(0,s.jsx)(N.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:"Subcategory allocations exceed this category's budget!"})]}),a.subCategories.length>0&&(0,s.jsx)(T,{type:"single",collapsible:!0,className:"w-full text-sm",children:(0,s.jsxs)(P,{value:"cat-".concat(a.id,"-subcategories"),className:"border-t pt-2",children:[(0,s.jsxs)(E,{className:"py-1 text-xs hover:no-underline justify-start gap-1 group",children:[(0,s.jsx)(q.A,{className:"h-3 w-3 hidden group-data-[state=open]:block"}),(0,s.jsx)(X.A,{className:"h-3 w-3 block group-data-[state=open]:hidden"}),(0,s.jsxs)("span",{children:["Subcategories (",a.subCategories.length,")"]})]}),(0,s.jsx)(I,{className:"pt-1 pb-0 pl-2 border-l ml-1.5",children:a.subCategories.map(e=>(0,s.jsx)(Y,{subCategory:e,onEdit:()=>{x(e),c(!0)},onDelete:()=>r(a.id,e.id),balancesVisible:p},e.id))})]})})]}),(0,s.jsx)(m.wL,{className:"px-4 pb-3 pt-0",children:(0,s.jsxs)(_,{open:d,onOpenChange:e=>{c(e),e||x(null)},children:[(0,s.jsx)(O,{asChild:!0,children:(0,s.jsxs)(S.$,{variant:"outline",size:"sm",className:"w-full text-xs",onClick:()=>{x(null),c(!0)},children:[(0,s.jsx)(Q.A,{className:"mr-1 h-3 w-3"})," Add Subcategory"]})}),(0,s.jsxs)(G,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(L,{children:(0,s.jsxs)(F,{className:"font-headline",children:[u?"Edit":"Add"," Subcategory"]})}),(0,s.jsx)(K,{onSubmit:u?e=>{if(!u)return!1;let t=n(a.id,u.id,e.name,e.allocatedAmount);return t?(h({title:"Subcategory Updated",description:"".concat(e.name," updated.")}),x(null),c(!1)):h({title:"Error",description:"Cannot update allocation. Exceeds remaining budget in ".concat(a.name,"."),variant:"destructive"}),t}:e=>{let s=t(a.id,e.name,e.allocatedAmount);return s?(h({title:"Subcategory Added",description:"".concat(e.name," added to ").concat(a.name,".")}),c(!1)):h({title:"Error",description:"Cannot allocate ".concat(p?"R"+e.allocatedAmount.toFixed(2):"amount",". Exceeds remaining budget in ").concat(a.name,"."),variant:"destructive"}),s},initialData:u||{},onClose:()=>{c(!1),x(null)},parentCategoryName:a.name,balancesVisible:p})]})]})})]})}let el=W.Ik({name:W.Yj().min(1,{message:"Category name is required."}).max(50,{message:"Name must be 50 characters or less."}),budget:W.vk(e=>"string"==typeof e?parseFloat(e):e,W.ai().min(0,{message:"Budget must be a positive number."}))});function en(e){let{onSubmit:a,initialData:t,onClose:l}=e,n=(0,H.mN)({resolver:(0,Z.u)(el),defaultValues:{name:(null==t?void 0:t.name)||"",budget:(null==t?void 0:t.budget)||0}});return(0,s.jsx)($.lV,{...n,children:(0,s.jsxs)("form",{onSubmit:n.handleSubmit(e=>{a(e),n.reset(),l()}),className:"space-y-4",children:[(0,s.jsx)($.zB,{control:n.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Category Name"}),(0,s.jsx)($.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., Groceries",...a})}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsx)($.zB,{control:n.control,name:"budget",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Budget Amount (R)"}),(0,s.jsx)($.MJ,{children:(0,s.jsx)(d.p,{type:"number",placeholder:"e.g., 500",...a,step:"any"})}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,s.jsx)(S.$,{type:"button",variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(S.$,{type:"submit",children:(null==t?void 0:t.id)?"Save Changes":"Add Category"})]})]})})}var er=t(17649);let ei=er.bL;er.l9;let eo=er.ZL,ed=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(er.hJ,{className:(0,g.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...l,ref:a})});ed.displayName=er.hJ.displayName;let ec=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsxs)(eo,{children:[(0,s.jsx)(ed,{}),(0,s.jsx)(er.UC,{ref:a,className:(0,g.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...l})]})});ec.displayName=er.UC.displayName;let em=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,g.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...t})};em.displayName="AlertDialogHeader";let eu=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,g.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};eu.displayName="AlertDialogFooter";let ex=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(er.hE,{ref:a,className:(0,g.cn)("text-lg font-semibold",t),...l})});ex.displayName=er.hE.displayName;let eh=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(er.VY,{ref:a,className:(0,g.cn)("text-sm text-muted-foreground",t),...l})});eh.displayName=er.VY.displayName;let ep=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(er.rc,{ref:a,className:(0,g.cn)((0,S.r)(),t),...l})});ep.displayName=er.rc.displayName;let eg=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(er.ZD,{ref:a,className:(0,g.cn)((0,S.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...l})});eg.displayName=er.ZD.displayName;var ef=t(46287),ej=t(61021),ev=t(74601),ey=t(17607),eb=t(4607);function eN(e){var a,t;let{categories:n,onAddCategory:o,onUpdateCategory:d,onDeleteCategory:c,onAddSubCategory:u,onUpdateSubCategory:x,onDeleteSubCategory:h,onToggleCategoryVisibility:p,balancesVisible:g}=e,[f,j]=(0,l.useState)(!1),[v,y]=(0,l.useState)(null),[b,N]=(0,l.useState)(null),{toast:w}=(0,et.dj)();return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:(0,s.jsxs)(m.Zp,{className:"card-modern hover-glow group",children:[(0,s.jsxs)(m.aR,{className:"flex flex-row items-center justify-between pb-4",children:[(0,s.jsxs)(i.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.4,type:"spring"},className:"flex items-center gap-3",children:[(0,s.jsxs)(m.ZB,{className:"text-xl flex items-center gap-3 font-headline text-gradient-primary",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatDelay:4},className:"p-2 rounded-xl bg-gradient-to-r from-primary/20 to-accent/20 group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300",children:(0,s.jsx)(ef.A,{className:"h-6 w-6 text-primary"})}),"Budget Categories"]}),n.length>0&&(0,s.jsx)(i.P.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.6},className:"px-3 py-1 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 border border-primary/30",children:(0,s.jsx)("span",{className:"text-xs font-semibold text-primary",children:n.length})})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.5},children:(0,s.jsxs)(_,{open:f,onOpenChange:e=>{j(e),e||y(null)},children:[(0,s.jsx)(O,{asChild:!0,children:(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsxs)(S.$,{size:"sm",className:"btn-gradient-primary",onClick:()=>{y(null),j(!0)},children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,180,360]},transition:{duration:2,repeat:1/0,ease:"linear"},className:"mr-2",children:(0,s.jsx)(Q.A,{className:"h-4 w-4"})}),"Add Category"]})})}),(0,s.jsxs)(G,{className:"sm:max-w-[425px] glass-premium",children:[(0,s.jsx)(L,{children:(0,s.jsxs)(F,{className:"font-headline text-gradient-primary flex items-center gap-2",children:[(0,s.jsx)(ej.A,{className:"h-5 w-5"}),v?"Edit":"Add"," Category"]})}),(0,s.jsx)(en,{onSubmit:v?e=>{v&&(d(v.id,e.name,e.budget),w({title:"Category Updated",description:"".concat(e.name," has been updated.")}),y(null),j(!1))}:e=>{o(e.name,e.budget),w({title:"Category Added",description:"".concat(e.name," has been added.")}),j(!1)},initialData:v||{},onClose:()=>{j(!1),y(null)}})]})]})})]}),(0,s.jsx)(m.Wu,{className:"pt-4",children:0===n.length?(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.7},className:"text-center py-12",children:[(0,s.jsx)(i.P.div,{animate:{y:[0,-10,0]},transition:{duration:2,repeat:1/0},className:"inline-block p-6 rounded-full bg-gradient-to-r from-muted/20 to-muted/10 mb-4",children:(0,s.jsx)(ej.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"No Categories Yet"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Start organizing your budget by creating your first category"}),(0,s.jsx)(i.P.div,{animate:{scale:[1,1.05,1]},transition:{duration:2,repeat:1/0,repeatDelay:3},children:(0,s.jsxs)(S.$,{className:"btn-gradient-accent",onClick:()=>{y(null),j(!0)},children:[(0,s.jsx)(ev.A,{className:"mr-2 h-4 w-4"}),"Create Your First Category"]})})]}):(0,s.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"space-y-4",children:(0,s.jsx)(r.N,{mode:"popLayout",children:n.map((e,a)=>{var t,l,n,r;return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:.4,delay:.1*a,type:"spring",stiffness:300,damping:30},layout:!0,className:"relative group",children:(0,s.jsxs)("div",{className:"card-modern p-1 hover:shadow-lg transition-all duration-300",children:[(0,s.jsx)(es,{category:e,onUpdateCategory:d,onDeleteCategory:c,onAddSubCategory:u,onUpdateSubCategory:x,onDeleteSubCategory:h,balancesVisible:g,categoryIsVisible:null===(t=e.isVisible)||void 0===t||t,onToggleVisibility:()=>p(e.id)}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:0,scale:.8},whileHover:{opacity:1,scale:1},transition:{duration:.2},className:"absolute top-3 right-3 flex gap-1 group-hover:opacity-100 group-hover:scale-100",children:[(0,s.jsx)(i.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsxs)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8 glass-premium hover:bg-primary/20 border border-border/50",onClick:()=>p(e.id),children:[(0,s.jsx)(i.P.div,{animate:{rotate:null===(l=e.isVisible)||void 0===l||l?0:180},transition:{duration:.3},children:null===(n=e.isVisible)||void 0===n||n?(0,s.jsx)(ey.A,{className:"h-4 w-4"}):(0,s.jsx)(eb.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"sr-only",children:null===(r=e.isVisible)||void 0===r||r?"Hide Category Balances":"Show Category Balances"})]})}),(0,s.jsx)(i.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsxs)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8 glass-premium hover:bg-accent/20 border border-border/50",onClick:()=>{y(e),j(!0)},children:[(0,s.jsx)(U.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Edit Category"})]})}),(0,s.jsx)(i.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,s.jsxs)(S.$,{variant:"ghost",size:"icon",className:"h-8 w-8 glass-premium hover:bg-destructive/20 border border-border/50 text-destructive hover:text-destructive",onClick:()=>N(e.id),children:[(0,s.jsx)(i.P.div,{whileHover:{rotate:[0,10,-10,0]},transition:{duration:.3},children:(0,s.jsx)(z.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"sr-only",children:"Delete Category"})]})})]})]})},e.id)})})})}),(0,s.jsx)(ei,{open:!!b,onOpenChange:()=>N(null),children:(0,s.jsxs)(ec,{className:"glass-premium",children:[(0,s.jsxs)(em,{children:[(0,s.jsxs)(ex,{className:"flex items-center gap-2 text-destructive",children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.5,repeat:1/0},children:(0,s.jsx)(z.A,{className:"h-5 w-5"})}),"Delete Category?"]}),(0,s.jsxs)(eh,{className:"text-muted-foreground",children:["This action will permanently delete the category",null!==(t=null===(a=n.find(e=>e.id===b))||void 0===a?void 0:a.subCategories.length)&&void 0!==t&&t?" and all its subcategories":"",". This cannot be undone."]})]}),(0,s.jsxs)(eu,{className:"gap-2",children:[(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)(eg,{className:"btn-glass",children:"Cancel"})}),(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsxs)(ep,{onClick:()=>{if(b){let e=n.find(e=>e.id===b);c(b),w({title:"Category Deleted",description:"".concat((null==e?void 0:e.name)||"Category"," has been deleted.")}),N(null)}},className:"bg-gradient-to-r from-destructive to-destructive/80 hover:from-destructive/90 hover:to-destructive/70 text-white",children:[(0,s.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Delete Forever"]})})]})]})})]})})}var ew=t(9041),eA=t(83540),eC=t(94517),eS=t(24026);let ek={light:"",dark:".dark"},eR=l.createContext(null);function eT(){let e=l.useContext(eR);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let eP=l.forwardRef((e,a)=>{let{id:t,className:n,children:r,config:i,...o}=e,d=l.useId(),c="chart-".concat(t||d.replace(/:/g,""));return(0,s.jsx)(eR.Provider,{value:{config:i},children:(0,s.jsxs)("div",{"data-chart":c,ref:a,className:(0,g.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",n),...o,children:[(0,s.jsx)(eE,{id:c,config:i}),(0,s.jsx)(eA.u,{children:r})]})})});eP.displayName="Chart";let eE=e=>{let{id:a,config:t}=e,l=Object.entries(t).filter(e=>{let[,a]=e;return a.theme||a.color});return l.length?(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(ek).map(e=>{let[t,s]=e;return"\n".concat(s," [data-chart=").concat(a,"] {\n").concat(l.map(e=>{var a;let[s,l]=e,n=(null===(a=l.theme)||void 0===a?void 0:a[t])||l.color;return n?"  --color-".concat(s,": ").concat(n,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},eI=eC.m,eD=l.forwardRef((e,a)=>{let{active:t,payload:n,className:r,indicator:i="dot",hideLabel:o=!1,hideIndicator:d=!1,label:c,labelFormatter:m,labelClassName:u,formatter:x,color:h,nameKey:p,labelKey:f}=e,{config:j}=eT(),v=l.useMemo(()=>{var e;if(o||!(null==n?void 0:n.length))return null;let[a]=n,t="".concat(f||a.dataKey||a.name||"value"),l=eO(j,a,t),r=f||"string"!=typeof c?null==l?void 0:l.label:(null===(e=j[c])||void 0===e?void 0:e.label)||c;return m?(0,s.jsx)("div",{className:(0,g.cn)("font-medium",u),children:m(r,n)}):r?(0,s.jsx)("div",{className:(0,g.cn)("font-medium",u),children:r}):null},[c,m,n,o,u,j,f]);if(!t||!(null==n?void 0:n.length))return null;let y=1===n.length&&"dot"!==i;return(0,s.jsxs)("div",{ref:a,className:(0,g.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",r),children:[y?null:v,(0,s.jsx)("div",{className:"grid gap-1.5",children:n.map((e,a)=>{let t="".concat(p||e.name||e.dataKey||"value"),l=eO(j,e,t),n=h||e.payload.fill||e.color;return(0,s.jsx)("div",{className:(0,g.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===i&&"items-center"),children:x&&(null==e?void 0:e.value)!==void 0&&e.name?x(e.value,e.name,e,a,e.payload):(0,s.jsxs)(s.Fragment,{children:[(null==l?void 0:l.icon)?(0,s.jsx)(l.icon,{}):!d&&(0,s.jsx)("div",{className:(0,g.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===i,"w-1":"line"===i,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===i,"my-0.5":y&&"dashed"===i}),style:{"--color-bg":n,"--color-border":n}}),(0,s.jsxs)("div",{className:(0,g.cn)("flex flex-1 justify-between leading-none",y?"items-end":"items-center"),children:[(0,s.jsxs)("div",{className:"grid gap-1.5",children:[y?v:null,(0,s.jsx)("span",{className:"text-muted-foreground",children:(null==l?void 0:l.label)||e.name})]}),e.value&&(0,s.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});eD.displayName="ChartTooltip";let eV=eS.s,e_=l.forwardRef((e,a)=>{let{className:t,hideIcon:l=!1,payload:n,verticalAlign:r="bottom",nameKey:i}=e,{config:o}=eT();return(null==n?void 0:n.length)?(0,s.jsx)("div",{ref:a,className:(0,g.cn)("flex items-center justify-center gap-4","top"===r?"pb-3":"pt-3",t),children:n.map(e=>{let a="".concat(i||e.dataKey||"value"),t=eO(o,e,a);return(0,s.jsxs)("div",{className:(0,g.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[(null==t?void 0:t.icon)&&!l?(0,s.jsx)(t.icon,{}):(0,s.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==t?void 0:t.label]},e.value)})}):null});function eO(e,a,t){if("object"!=typeof a||null===a)return;let s="payload"in a&&"object"==typeof a.payload&&null!==a.payload?a.payload:void 0,l=t;return t in a&&"string"==typeof a[t]?l=a[t]:s&&t in s&&"string"==typeof s[t]&&(l=s[t]),l in e?e[l]:e[t]}e_.displayName="ChartLegend";var eB=t(58995),eM=t(79133),eG=t(54811);let eL=["hsl(var(--chart-1))","hsl(var(--chart-2))","hsl(var(--chart-3))","hsl(var(--chart-4))","hsl(var(--chart-5))","hsl(200 70% 50%)","hsl(300 70% 50%)","hsl(50 70% 50%)"];function eF(e){let{totalIncome:a,categories:t,balancesVisible:l}=e,n=a-t.reduce((e,a)=>e+a.budget,0),r=t.filter(e=>e.budget>0).map((e,a)=>{var t;return{name:e.name,value:e.budget,fill:eL[a%eL.length],isVisible:null===(t=e.isVisible)||void 0===t||t}});n>0&&r.push({name:"Unallocated",value:n,fill:"hsl(var(--muted))",isVisible:!0});let i=r.reduce((e,a)=>(e[a.name]={label:a.name,color:a.fill},e),{});if(0===a&&0===t.length)return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-2",children:(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(ew.A,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,s.jsx)(m.Wu,{children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Enter your income and add categories to see a visual breakdown."})})]});let o=0===r.length||r.every(e=>0===e.value);return(0,s.jsxs)(m.Zp,{children:[(0,s.jsx)(m.aR,{className:"pb-2",children:(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(ew.A,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,s.jsxs)(m.Wu,{children:[o?(0,s.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Allocate funds to categories to see the chart."}):(0,s.jsx)(eP,{config:i,className:"mx-auto aspect-square max-h-[250px] h-auto",children:(0,s.jsxs)(eB.r,{children:[(0,s.jsx)(eI,{formatter:(e,a,t)=>{var s,n;let r=null===(n=null===(s=t.payload)||void 0===s?void 0:s.isVisible)||void 0===n||n;return[l&&r?"R ".concat(Number(e).toFixed(2)):"R ••••",a]},content:(0,s.jsx)(eD,{nameKey:"name",hideLabel:!1})}),(0,s.jsx)(eM.F,{data:r,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:80,labelLine:!1,label:e=>{var a;let{percent:t,payload:s}=e,n=null===(a=null==s?void 0:s.isVisible)||void 0===a||a;return l&&n?"".concat((100*t).toFixed(0),"%"):""},children:r.map((e,a)=>(0,s.jsx)(eG.f,{fill:e.fill},"cell-".concat(a)))}),(0,s.jsx)(eV,{content:(0,s.jsx)(e_,{nameKey:"name",className:"text-xs flex-wrap justify-center gap-x-2 gap-y-1"})})]})}),t.map(e=>{var a;if(0===e.budget)return null;let t=l&&(null===(a=e.isVisible)||void 0===a||a),n=e.subCategories.reduce((e,a)=>e+a.allocatedAmount,0),r=e.budget>0?n/e.budget*100:0,i=n>e.budget;return(0,s.jsxs)("div",{className:"mt-3 text-xs",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-0.5",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name}),(0,s.jsx)("span",{className:"text-muted-foreground",children:t?"".concat(n.toFixed(2)," / ").concat(e.budget.toFixed(2)):"•••• / ••••"})]}),(0,s.jsx)(ea,{value:Math.min(r,100),className:"h-1.5 ".concat(i?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary")})]},e.id)})]})]})}var eU=t(93081),ez=t(77381),eY=t(10518);let eZ=eU.bL;eU.YJ;let eH=eU.WT,eW=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(eU.l9,{ref:a,className:(0,g.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...n,children:[l,(0,s.jsx)(eU.In,{asChild:!0,children:(0,s.jsx)(R.A,{className:"h-4 w-4 opacity-50"})})]})});eW.displayName=eU.l9.displayName;let e$=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(eU.PP,{ref:a,className:(0,g.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(ez.A,{className:"h-4 w-4"})})});e$.displayName=eU.PP.displayName;let eJ=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(eU.wn,{ref:a,className:(0,g.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,s.jsx)(R.A,{className:"h-4 w-4"})})});eJ.displayName=eU.wn.displayName;let eK=l.forwardRef((e,a)=>{let{className:t,children:l,position:n="popper",...r}=e;return(0,s.jsx)(eU.ZL,{children:(0,s.jsxs)(eU.UC,{ref:a,className:(0,g.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...r,children:[(0,s.jsx)(e$,{}),(0,s.jsx)(eU.LM,{className:(0,g.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(eJ,{})]})})});eK.displayName=eU.UC.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(eU.JU,{ref:a,className:(0,g.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=eU.JU.displayName;let eq=l.forwardRef((e,a)=>{let{className:t,children:l,...n}=e;return(0,s.jsxs)(eU.q7,{ref:a,className:(0,g.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(eU.VF,{children:(0,s.jsx)(eY.A,{className:"h-4 w-4"})})}),(0,s.jsx)(eU.p4,{children:l})]})});eq.displayName=eU.q7.displayName,l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,s.jsx)(eU.wv,{ref:a,className:(0,g.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=eU.wv.displayName;var eX=t(27300),eQ=t(58260),e0=t(42625),e1=t(28328),e2=t(18186),e5=t(57082),e4=t(27379),e3=t(68098),e6=t(2160);let e8=W.Ik({name:W.Yj().min(1,{message:"Goal name is required."}).max(50,{message:"Name must be 50 characters or less."}),targetAmount:W.vk(e=>"string"==typeof e?parseFloat(e):e,W.ai().min(1,{message:"Target amount must be greater than 0."})),icon:W.Yj().optional()}),e9=[{value:"Default",label:"Default",Icon:eX.A},{value:"Savings",label:"Savings",Icon:e=>{let{className:a}=e;return(0,s.jsxs)("svg",{className:a,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M10 21h4"}),(0,s.jsx)("path",{d:"M12 17v4"}),(0,s.jsx)("path",{d:"M10 3h4c2.2 0 4 1.8 4 4v2c0 1.1-.9 2-2 2h-1"}),(0,s.jsx)("path",{d:"M8 11V7c0-2.2 1.8-4 4-4"}),(0,s.jsx)("path",{d:"M19 13c0-1.66-1.34-3-3-3h-2V7"}),(0,s.jsx)("path",{d:"M10 13c2.2 0 4-1.8 4-4"}),(0,s.jsx)("path",{d:"M2 13c2.5 0 2.5-3 5-3s2.5 3 5 3c2.5 0 2.5-3 5-3s2.5 3 5 3"}),(0,s.jsx)("path",{d:"M7.5 13s.5-1 2.5-1 2.5 1 2.5 1"}),(0,s.jsx)("path",{d:"M14 13c2 0 2.5-1 2.5-1"}),(0,s.jsx)("path",{d:"M2 17h.01"})]})}},{value:"Vacation",label:"Vacation",Icon:eQ.A},{value:"Shopping",label:"Shopping",Icon:e0.A},{value:"Car",label:"Car",Icon:e1.A},{value:"Home",label:"Home Renovation",Icon:e2.A},{value:"Business",label:"Business",Icon:e5.A},{value:"Education",label:"Education",Icon:e4.A},{value:"Wedding",label:"Wedding",Icon:e3.A},{value:"Gift",label:"Gift",Icon:e6.A}];function e7(e){let{onSubmit:a,initialData:t,onClose:l}=e,n=(0,H.mN)({resolver:(0,Z.u)(e8),defaultValues:{name:(null==t?void 0:t.name)||"",targetAmount:(null==t?void 0:t.targetAmount)||0,icon:(null==t?void 0:t.icon)||"Default"}});return(0,s.jsx)($.lV,{...n,children:(0,s.jsxs)("form",{onSubmit:n.handleSubmit(e=>{a(e.name,e.targetAmount,e.icon),t||n.reset({name:"",targetAmount:0,icon:"Default"}),l()}),className:"space-y-4 pt-2",children:[(0,s.jsx)($.zB,{control:n.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Goal Name"}),(0,s.jsx)($.MJ,{children:(0,s.jsx)(d.p,{placeholder:"e.g., New Laptop, Vacation Fund",...a})}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsx)($.zB,{control:n.control,name:"targetAmount",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Target Amount (R)"}),(0,s.jsx)($.MJ,{children:(0,s.jsx)(d.p,{type:"number",placeholder:"e.g., 15000",...a,step:"any"})}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsx)($.zB,{control:n.control,name:"icon",render:e=>{let{field:a}=e;return(0,s.jsxs)($.eI,{children:[(0,s.jsx)($.lR,{children:"Goal Icon (Optional)"}),(0,s.jsxs)(eZ,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,s.jsx)($.MJ,{children:(0,s.jsx)(eW,{children:(0,s.jsx)(eH,{placeholder:"Select an icon"})})}),(0,s.jsx)(eK,{children:e9.map(e=>(0,s.jsx)(eq,{value:e.value,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(e.Icon,{className:"h-4 w-4"}),e.label]})},e.value))})]}),(0,s.jsx)($.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,s.jsx)(S.$,{type:"button",variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(S.$,{type:"submit",children:t?"Save Changes":"Set Goal"})]})]})})}var ae=t(519),aa=t(91467),at=t(58127);let as={Default:eX.A,Vacation:ae.A,Gadget:aa.A};function al(e){let{goal:a,onSetGoal:t,onUpdateProgress:n,onClearGoal:r,overallRemaining:i,balancesVisible:o}=e,[d,c]=(0,l.useState)(!1),[u,x]=(0,l.useState)(!1),[h,p]=(0,l.useState)(0),[g,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>{a&&p(a.savedAmount)},[a]);let j=e=>o?"R ".concat(e.toFixed(2)):"R ••••",v=(null==a?void 0:a.icon)&&as[a.icon]?as[a.icon]:b.A;if(!a||a.dateAchieved)return(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{className:"pb-2",children:[(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(null==a?void 0:a.dateAchieved)?(0,s.jsx)(at.A,{className:"h-5 w-5 text-green-500"}):(0,s.jsx)(b.A,{className:"h-5 w-5 text-primary"}),(null==a?void 0:a.dateAchieved)?"Goal Achieved!":"Financial Goal"]}),(null==a?void 0:a.dateAchieved)&&a&&(0,s.jsxs)(m.BT,{className:"text-sm",children:["Congrats on achieving: ",a.name,"!"]})]}),(0,s.jsxs)(m.Wu,{children:[(null==a?void 0:a.dateAchieved)&&a&&(0,s.jsxs)("div",{className:"space-y-1 text-center",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:j(a.targetAmount)}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Achieved on ",new Date(a.dateAchieved).toLocaleDateString()]})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ".concat((null==a?void 0:a.dateAchieved)?"mt-2 text-center":"text-center py-4"),children:(null==a?void 0:a.dateAchieved)?"Ready for a new challenge?":"Set a financial goal to start saving towards something important!"})]}),(0,s.jsx)(m.wL,{children:(0,s.jsxs)(_,{open:d,onOpenChange:c,children:[(0,s.jsx)(O,{asChild:!0,children:(0,s.jsxs)(S.$,{className:"w-full",onClick:()=>c(!0),children:[(0,s.jsx)(Q.A,{className:"mr-2 h-4 w-4"})," ",(null==a?void 0:a.dateAchieved)?"Set New Goal":"Set a Goal"]})}),(0,s.jsxs)(G,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(L,{children:(0,s.jsx)(F,{className:"font-headline",children:(null==a?void 0:a.dateAchieved)?"Set New Financial Goal":"Set Financial Goal"})}),(0,s.jsx)(e7,{onSubmit:(e,a,s)=>{t(e,a,s),c(!1)},initialData:null,onClose:()=>c(!1)})]})]})})]});let y=a.targetAmount>0?a.savedAmount/a.targetAmount*100:0;return(0,s.jsxs)(m.Zp,{children:[(0,s.jsxs)(m.aR,{className:"pb-3 pt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(m.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,s.jsx)(v,{className:"h-5 w-5 text-primary"}),a.name]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsxs)(_,{open:d,onOpenChange:c,children:[(0,s.jsx)(O,{asChild:!0,children:(0,s.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:()=>c(!0),children:(0,s.jsx)(U.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(G,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(L,{children:(0,s.jsx)(F,{className:"font-headline",children:"Edit Financial Goal"})}),(0,s.jsx)(e7,{onSubmit:(e,a,s)=>{t(e,a,s),c(!1)},initialData:a,onClose:()=>c(!1)})]})]}),(0,s.jsx)(S.$,{variant:"ghost",size:"icon",className:"h-7 w-7 text-destructive hover:text-destructive",onClick:()=>f(!0),children:(0,s.jsx)(z.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)(m.BT,{className:"text-xs pt-1",children:["Target: ",j(a.targetAmount)," | Saved: ",j(a.savedAmount)]})]}),(0,s.jsxs)(m.Wu,{className:"space-y-3",children:[(0,s.jsx)(ea,{value:y,className:"h-2 [&>*]:bg-primary"}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:[j(a.targetAmount-a.savedAmount)," still to go. You can do it!"]}),o&&i>0&&(0,s.jsxs)("p",{className:"text-xs text-green-600 bg-green-500/10 p-1.5 rounded-md",children:["You have ",j(i)," unallocated in your budget. Consider putting some towards your goal!"]})]}),(0,s.jsx)(m.wL,{children:(0,s.jsxs)(_,{open:u,onOpenChange:x,children:[(0,s.jsx)(O,{asChild:!0,children:(0,s.jsx)(S.$,{className:"w-full",variant:"outline",onClick:()=>{p(a.savedAmount),x(!0)},children:"Log Progress"})}),(0,s.jsxs)(G,{className:"sm:max-w-[425px]",children:[(0,s.jsx)(L,{children:(0,s.jsxs)(F,{className:"font-headline",children:["Log Progress for ",a.name]})}),(0,s.jsxs)("div",{className:"space-y-4 py-2",children:[(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current Target: ",j(a.targetAmount)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"progressAmount",className:"block text-sm font-medium text-foreground mb-1",children:"Total Amount Saved Towards Goal (R)"}),(0,s.jsx)("input",{id:"progressAmount",type:o?"number":"text",value:o?h:"••••",onChange:e=>{let a=parseFloat(e.target.value);p(Math.max(0,isNaN(a)?0:a))},readOnly:!o,className:"w-full p-2 border rounded-md border-input",step:"any"})]}),(0,s.jsx)(S.$,{onClick:()=>{n(h),x(!1)},className:"w-full",children:"Save Progress"})]})]})]})}),(0,s.jsx)(ei,{open:g,onOpenChange:f,children:(0,s.jsxs)(ec,{children:[(0,s.jsxs)(em,{children:[(0,s.jsx)(ex,{children:"Are you sure?"}),(0,s.jsxs)(eh,{children:['This will clear your current financial goal "',a.name,'". This action cannot be undone.']})]}),(0,s.jsxs)(eu,{children:[(0,s.jsx)(eg,{children:"Cancel"}),(0,s.jsx)(ep,{onClick:()=>{r(),f(!1)},className:"bg-destructive hover:bg-destructive/90",children:"Clear Goal"})]})]})})]})}var an=t(68856),ar=t(85268),ai=t(30462),ao=t(54913);let ad={trophy:ar.A,star:ai.A,target:b.A,zap:A.A,award:aa.A,crown:ao.A},ac={gold:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 border-yellow-500/30",silver:"bg-gradient-to-r from-gray-300 to-gray-500 text-gray-900 border-gray-400/30",bronze:"bg-gradient-to-r from-orange-400 to-orange-600 text-orange-900 border-orange-500/30",primary:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-primary/30",success:"bg-gradient-to-r from-green-400 to-green-600 text-green-900 border-green-500/30"},am={sm:"px-2 py-1 text-xs",md:"px-3 py-1.5 text-sm",lg:"px-4 py-2 text-base"};function au(e){let{title:a,description:t,icon:l="trophy",variant:n="gold",size:r="md",animated:o=!0,showConfetti:d=!1,className:c}=e,m=ad[l],u=(0,s.jsxs)("div",{className:(0,g.cn)("inline-flex items-center gap-2 rounded-full font-medium border shadow-lg",ac[n],am[r],o&&"hover:scale-105 transition-transform duration-200",c),children:[(0,s.jsx)(m,{className:(0,g.cn)("shrink-0","sm"===r?"h-3 w-3":"md"===r?"h-4 w-4":"h-5 w-5")}),(0,s.jsx)("span",{className:"font-semibold",children:a})]});return o?(0,s.jsxs)(i.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:260,damping:20,delay:.1},whileHover:{scale:1.05},whileTap:{scale:.95},className:"inline-block",children:[u,t&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-xs text-muted-foreground mt-1 text-center",children:t})]}):u}function ax(e){let{title:a,description:t,icon:l="trophy",variant:n="gold",onClose:r}=e,o=ad[l];return(0,s.jsxs)(i.P.div,{initial:{x:300,opacity:0},animate:{x:0,opacity:1},exit:{x:300,opacity:0},transition:{type:"spring",stiffness:300,damping:30},className:(0,g.cn)("flex items-center gap-3 p-4 rounded-lg border shadow-lg max-w-sm","bg-card/95 backdrop-blur-sm border-border/50"),children:[(0,s.jsx)(i.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.6,repeat:2},className:(0,g.cn)("flex items-center justify-center w-10 h-10 rounded-full",ac[n]),children:(0,s.jsx)(o,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:a}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]}),r&&(0,s.jsx)("button",{onClick:r,className:"text-muted-foreground hover:text-foreground transition-colors",children:"\xd7"})]})}var ah=t(90925),ap=t(64509);let ag={BUDGET_STARTER:"BUDGET_STARTER",GOAL_SETTER:"GOAL_SETTER",GOAL_CRUSHER:"GOAL_CRUSHER"},af={[ag.BUDGET_STARTER]:{title:"Budget Starter!",description:"You've set your income and added your first category!",IconComponent:ap.A},[ag.GOAL_SETTER]:{title:"Goal Setter!",description:"You've set your first financial goal!",IconComponent:b.A},[ag.GOAL_CRUSHER]:{title:"Goal Crusher!",description:"Congratulations! You've achieved your financial goal!",IconComponent:ar.A}};function aj(){var e,a;let{currentUser:t,loading:d}=(0,ah.A)(),c=(0,n.useRouter)(),[m,u]=(0,l.useState)(!1),[x,h]=(0,l.useState)(0),[g,f]=(0,l.useState)([]),[j,v]=(0,l.useState)(null),[y,b]=(0,l.useState)([]),[N,w]=(0,l.useState)(!0),[A,S]=(0,l.useState)(null),[k,R]=(0,l.useState)(!0),{toast:T}=(0,et.dj)(),P=(0,l.useCallback)(()=>t?"budgetWiseData_".concat(t.uid):null,[t]),E=(0,l.useCallback)(e=>{if(!y.includes(e)){b(a=>[...a,e]),S(e);let a=af[e];a&&(T({title:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.IconComponent,{className:"h-5 w-5 text-accent"}),(0,s.jsx)("span",{children:a.title})]}),description:a.description}),setTimeout(()=>S(null),5e3))}},[y,T]);(0,l.useEffect)(()=>{u(!0)},[]),(0,l.useEffect)(()=>{if(d)return;if(!t){c.push("/login");return}let e=P();if(e&&m){let a=localStorage.getItem(e);if(a)try{let e=JSON.parse(a);h(e.totalIncome||0),f((e.categories||[]).map(e=>({...e,isVisible:void 0===e.isVisible||e.isVisible,subCategories:e.subCategories||[]}))),v(e.financialGoal||null),b(e.achievements||[]),w(void 0===e.balancesVisible||e.balancesVisible)}catch(e){console.error("Failed to parse budget data from localStorage",e),h(0),f([]),v(null),b([]),w(!0)}else h(0),f([]),v(null),b([]),w(!0)}},[t,d,c,m,P]),(0,l.useEffect)(()=>{let e=P();e&&m&&!d&&t&&(localStorage.setItem(e,JSON.stringify({totalIncome:x,categories:g,financialGoal:j,achievements:y,balancesVisible:N})),x>0&&g.length>0&&E(ag.BUDGET_STARTER),(null==j?void 0:j.dateAchieved)&&E(ag.GOAL_CRUSHER))},[x,g,j,y,N,m,E,P,d,t]);let I=(0,l.useCallback)(()=>{w(e=>!e)},[]),D=(0,l.useCallback)(e=>{f(a=>a.map(a=>{var t;return a.id===e?{...a,isVisible:!(null===(t=a.isVisible)||void 0===t||t)}:a}))},[]),V=(0,l.useCallback)(e=>{h(e)},[]),_=(0,l.useCallback)((e,a)=>{let t={id:crypto.randomUUID(),name:e,budget:a,subCategories:[],isVisible:!0};f(e=>[...e,t])},[]),O=(0,l.useCallback)((e,a,t)=>{f(s=>s.map(s=>s.id===e?{...s,name:a,budget:t}:s))},[]),B=(0,l.useCallback)(e=>{f(a=>a.filter(a=>a.id!==e))},[]),M=(0,l.useCallback)((e,a,t)=>{let s=!1;return f(l=>l.map(l=>{if(l.id===e){if(l.subCategories.reduce((e,a)=>e+a.allocatedAmount,0)+t>l.budget)return s=!1,l;let e={id:crypto.randomUUID(),name:a,allocatedAmount:t};return s=!0,{...l,subCategories:[...l.subCategories,e]}}return l})),s},[]),G=(0,l.useCallback)((e,a,t,s)=>{let l=!1;return f(n=>n.map(n=>n.id===e?n.subCategories.filter(e=>e.id!==a).reduce((e,a)=>e+a.allocatedAmount,0)+s>n.budget?(l=!1,n):(l=!0,{...n,subCategories:n.subCategories.map(e=>e.id===a?{...e,name:t,allocatedAmount:s}:e)}):n)),l},[]),L=(0,l.useCallback)((e,a)=>{f(t=>t.map(t=>t.id===e?{...t,subCategories:t.subCategories.filter(e=>e.id!==a)}:t)),T({title:"Subcategory Deleted",description:"Subcategory has been removed."})},[T]),F=(0,l.useCallback)((e,a,t)=>{v({id:(null==j?void 0:j.id)||crypto.randomUUID(),name:e,targetAmount:a,savedAmount:(null==j?void 0:j.id)?j.savedAmount:0,icon:t,dateSet:(null==j?void 0:j.dateSet)||new Date().toISOString(),dateAchieved:null}),y.includes(ag.GOAL_SETTER)&&j||E(ag.GOAL_SETTER),T({title:"Financial Goal Updated!",description:'Your goal "'.concat(e,'" has been set/updated.')})},[j,E,T,y]),U=(0,l.useCallback)(e=>{if(j){let a={...j,savedAmount:e};e>=j.targetAmount&&!j.dateAchieved&&(a.dateAchieved=new Date().toISOString(),E(ag.GOAL_CRUSHER),T({title:"Goal Achieved!",description:"Congratulations on reaching your goal: ".concat(j.name,"!"),duration:5e3})),v(a)}},[j,E,T]),z=(0,l.useCallback)(()=>{v(null),T({title:"Financial Goal Cleared",description:"Your financial goal has been removed."})},[T]),Y=g.reduce((e,a)=>e+a.budget,0),Z=x-Y;return!d&&m&&t?(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,s.jsx)(o.default,{title:"BudgetWise",balancesVisible:N,onToggleBalances:I}),(0,s.jsx)(r.N,{children:A&&(0,s.jsx)(i.P.div,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0},className:"fixed top-20 right-4 z-50",children:(0,s.jsx)(ax,{title:(null===(e=af[A])||void 0===e?void 0:e.title)||"Achievement Unlocked!",description:(null===(a=af[A])||void 0===a?void 0:a.description)||"You've earned a new achievement!",icon:"trophy",variant:"gold",onClose:()=>S(null)})})}),(0,s.jsxs)("main",{className:"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:[y.length>0&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-6 flex flex-wrap gap-2",children:y.map(e=>{var a;return(0,s.jsx)(au,{title:(null===(a=af[e])||void 0===a?void 0:a.title)||"Achievement",icon:"trophy",variant:"gold",size:"sm",animated:!0},e)})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.1},className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,s.jsx)(p,{totalIncome:x,onIncomeChange:V,balancesVisible:N}),(0,s.jsx)(C,{totalIncome:x,overallTotalAllocated:Y,balancesVisible:N}),(0,s.jsx)(al,{goal:j,onSetGoal:F,onUpdateProgress:U,onClearGoal:z,overallRemaining:Z,balancesVisible:N}),(0,s.jsx)(eF,{totalIncome:x,categories:g,balancesVisible:N})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"md:col-span-2",children:(0,s.jsx)(eN,{categories:g,onAddCategory:_,onUpdateCategory:O,onDeleteCategory:B,onAddSubCategory:M,onUpdateSubCategory:G,onDeleteSubCategory:L,onToggleCategoryVisibility:D,balancesVisible:N})})]})]})]}):(0,s.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,s.jsx)(o.default,{title:"BudgetWise",balancesVisible:N,onToggleBalances:I}),(0,s.jsx)(an.eX,{})]})}},67813:(e,a,t)=>{Promise.resolve().then(t.bind(t,17153))},68856:(e,a,t)=>{"use strict";t.d(a,{EA:()=>n,eX:()=>o});var s=t(95155),l=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",a),...t})}function r(){return(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n,{className:"h-5 w-5 rounded"}),(0,s.jsx)(n,{className:"h-5 w-32"})]}),(0,s.jsx)(n,{className:"h-8 w-20 rounded-md"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(n,{className:"h-4 w-24"}),(0,s.jsx)(n,{className:"h-4 w-16"})]}),(0,s.jsx)(n,{className:"h-2 w-full rounded-full"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(n,{className:"h-4 w-20"}),(0,s.jsx)(n,{className:"h-4 w-16"})]})]})]})}function i(){return(0,s.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,s.jsx)(n,{className:"h-5 w-5 rounded"}),(0,s.jsx)(n,{className:"h-5 w-32"})]}),(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)(n,{className:"h-48 w-48 rounded-full"})}),(0,s.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2 justify-center",children:[(0,s.jsx)(n,{className:"h-4 w-16 rounded-full"}),(0,s.jsx)(n,{className:"h-4 w-20 rounded-full"}),(0,s.jsx)(n,{className:"h-4 w-14 rounded-full"}),(0,s.jsx)(n,{className:"h-4 w-18 rounded-full"})]})]})}function o(){return(0,s.jsx)("div",{className:"container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,s.jsxs)("div",{className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,s.jsx)(r,{}),(0,s.jsx)(r,{}),(0,s.jsx)(r,{})]}),(0,s.jsxs)("div",{className:"md:col-span-2 space-y-4 sm:space-y-6",children:[(0,s.jsx)(r,{}),(0,s.jsx)(i,{})]})]})})}},87481:(e,a,t)=>{"use strict";t.d(a,{dj:()=>u});var s=t(12115);let l=0,n=new Map,r=e=>{if(n.has(e))return;let a=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,a)},i=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=a;return t?r(t):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function m(e){let{...a}=e,t=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...a,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,a]=s.useState(d);return s.useEffect(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var a=a=>e(e.s=a);e.O(0,[73,671,440,521,422,59,575,375,321,441,684,358],()=>a(67813)),_N_E=e.O()}]);