
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', sans-serif;
}

@layer base {
  :root {
    --background: 220 40% 8%; /* Deeper Dark Slate Blue */
    --foreground: 220 15% 90%; /* Brighter Light Grayish Blue */

    --card: 220 40% 12%;
    --card-foreground: 220 15% 85%;

    --popover: 220 40% 10%; /* Slightly darker popover */
    --popover-foreground: 220 15% 88%;

    --primary: 180 75% 50%; /* Brighter Teal */
    --primary-foreground: 180 50% 98%; /* Very light for text on primary */

    --secondary: 220 35% 22%;
    --secondary-foreground: 220 10% 75%;

    --muted: 220 25% 25%;
    --muted-foreground: 220 10% 65%;

    --accent: 15 95% 65%; /* Brighter Coral */
    --accent-foreground: 15 90% 8%; /* Dark brown/black for text on accent */

    --destructive: 0 75% 55%; /* Brighter Red */
    --destructive-foreground: 0 0% 98%;

    --success: 142 76% 36%; /* Green for success states */
    --success-foreground: 142 76% 95%;

    --warning: 38 92% 50%; /* Orange for warning states */
    --warning-foreground: 38 92% 95%;

    --border: 220 35% 18%;
    --input: 220 35% 15%;
    --ring: 180 75% 55%; /* Primary color for focus rings */

    --chart-1: 180 75% 50%; /* Brighter Teal (Primary) */
    --chart-2: 15 95% 65%;  /* Brighter Coral (Accent) */
    --chart-3: 200 75% 60%; /* Brighter Blue */
    --chart-4: 300 65% 65%; /* Brighter Magenta/Purple */
    --chart-5: 50 85% 60%;  /* Brighter Yellow/Gold */
    --chart-6: 142 76% 45%; /* Green */
    --chart-7: 270 75% 60%; /* Purple */
    --chart-8: 340 75% 60%; /* Pink */

    --radius: 0.5rem;

    /* Gradient variables for enhanced visuals */
    --gradient-primary: linear-gradient(135deg, hsl(180 75% 50%) 0%, hsl(200 75% 55%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(15 95% 65%) 0%, hsl(25 95% 60%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220 40% 12%) 0%, hsl(220 35% 15%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(152 76% 40%) 100%);
  }

  .dark {
    --background: 220 40% 4%; /* Even Darker Slate Blue */
    --foreground: 220 15% 85%; /* Brighter text for dark mode */

    --card: 220 40% 8%;
    --card-foreground: 220 15% 80%;

    --popover: 220 40% 6%;
    --popover-foreground: 220 15% 85%;

    --primary: 180 75% 55%; /* Brighter Teal for Dark Mode */
    --primary-foreground: 180 50% 8%; /* Dark text on bright teal */

    --secondary: 220 35% 18%;
    --secondary-foreground: 220 10% 70%;

    --muted: 220 25% 20%;
    --muted-foreground: 220 10% 60%;

    --accent: 15 95% 70%; /* Brighter Coral for Dark Mode */
    --accent-foreground: 15 90% 5%; /* Dark text on bright coral */

    --destructive: 0 75% 58%;
    --destructive-foreground: 0 0% 98%;

    --success: 142 76% 40%;
    --success-foreground: 142 76% 95%;

    --warning: 38 92% 55%;
    --warning-foreground: 38 92% 95%;

    --border: 220 35% 15%;
    --input: 220 35% 12%;
    --ring: 180 75% 60%; /* Brighter Teal for ring */

    /* Enhanced gradients for dark mode */
    --gradient-primary: linear-gradient(135deg, hsl(180 75% 55%) 0%, hsl(200 75% 60%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(15 95% 70%) 0%, hsl(25 95% 65%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220 40% 8%) 0%, hsl(220 35% 12%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142 76% 40%) 0%, hsl(152 76% 45%) 100%);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    @apply min-h-screen antialiased;
  }
  .container {
    @apply px-2 sm:px-4;
  }
}

@layer components {
  /* Modern Glassmorphism Cards */
  .glass-card {
    @apply bg-card/40 backdrop-blur-xl border border-white/10 shadow-2xl rounded-2xl;
  }

  .glass-card-hover {
    @apply glass-card hover:bg-card/60 hover:border-white/20 hover:shadow-2xl transition-all duration-500 hover:scale-[1.02];
  }

  /* Modern Sidebar Styles */
  .sidebar-modern {
    @apply bg-gradient-to-b from-card/80 to-card/40 backdrop-blur-xl border-r border-white/10 shadow-2xl;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 hover:bg-white/10 hover:scale-105 cursor-pointer;
  }

  .sidebar-item-active {
    @apply sidebar-item bg-primary/20 border border-primary/30 shadow-lg shadow-primary/20;
  }

  /* Modern Button Styles */
  .btn-modern-primary {
    @apply bg-gradient-to-r from-primary via-primary/90 to-primary/80 hover:from-primary/90 hover:via-primary/80 hover:to-primary/70 text-primary-foreground font-semibold px-6 py-3 rounded-xl shadow-lg shadow-primary/30 hover:shadow-xl hover:shadow-primary/40 transition-all duration-300 transform hover:scale-105 active:scale-95 border border-primary/20;
  }

  .btn-modern-secondary {
    @apply bg-gradient-to-r from-secondary via-secondary/90 to-secondary/80 hover:from-secondary/90 hover:via-secondary/80 hover:to-secondary/70 text-secondary-foreground font-semibold px-6 py-3 rounded-xl shadow-lg shadow-secondary/30 hover:shadow-xl hover:shadow-secondary/40 transition-all duration-300 transform hover:scale-105 active:scale-95 border border-secondary/20;
  }

  .btn-modern-accent {
    @apply bg-gradient-to-r from-accent via-accent/90 to-accent/80 hover:from-accent/90 hover:via-accent/80 hover:to-accent/70 text-accent-foreground font-semibold px-6 py-3 rounded-xl shadow-lg shadow-accent/30 hover:shadow-xl hover:shadow-accent/40 transition-all duration-300 transform hover:scale-105 active:scale-95 border border-accent/20;
  }

  /* Modern Input Styles */
  .input-modern {
    @apply bg-input/30 backdrop-blur-sm border border-white/20 focus:border-primary/60 focus:ring-2 focus:ring-primary/30 rounded-xl px-4 py-3 transition-all duration-300 placeholder:text-muted-foreground/60;
  }

  /* Modern Progress Styles */
  .progress-modern {
    @apply bg-gradient-to-r from-muted/30 to-muted/20 rounded-full overflow-hidden backdrop-blur-sm border border-white/10;
  }

  .progress-modern > div {
    @apply bg-gradient-to-r from-primary via-primary/90 to-primary/80 transition-all duration-700 ease-out rounded-full shadow-lg shadow-primary/30;
  }

  /* Modern Metric Cards */
  .metric-card {
    @apply glass-card p-6 relative overflow-hidden;
  }

  .metric-card::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-accent to-primary;
  }

  /* Modern Chart Container */
  .chart-container {
    @apply glass-card p-6 relative overflow-hidden;
  }

  .chart-container::before {
    content: '';
    @apply absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/20 to-transparent rounded-bl-full;
  }

  /* Modern Navigation */
  .nav-modern {
    @apply bg-gradient-to-r from-card/90 via-card/80 to-card/90 backdrop-blur-xl border-b border-white/10 shadow-lg;
  }

  /* Floating Action Button */
  .fab {
    @apply fixed bottom-6 right-6 w-14 h-14 bg-gradient-to-r from-primary to-accent rounded-full shadow-2xl shadow-primary/40 flex items-center justify-center text-white hover:scale-110 transition-all duration-300 z-50;
  }

  /* Modern Badge */
  .badge-modern {
    @apply inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-gradient-to-r from-accent/20 to-accent/10 border border-accent/30 text-accent text-sm font-medium backdrop-blur-sm;
  }

  /* Glow Effects */
  .glow-primary-modern {
    @apply shadow-2xl shadow-primary/25 hover:shadow-2xl hover:shadow-primary/40 transition-shadow duration-500;
  }

  .glow-accent-modern {
    @apply shadow-2xl shadow-accent/25 hover:shadow-2xl hover:shadow-accent/40 transition-shadow duration-500;
  }

  .glow-success-modern {
    @apply shadow-2xl shadow-success/25 hover:shadow-2xl hover:shadow-success/40 transition-shadow duration-500;
  }
}

@layer utilities {
  /* Custom Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  @keyframes confetti {
    0% {
      transform: translateY(0) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(-100vh) rotate(720deg);
      opacity: 0;
    }
  }

  /* Animation Classes */
  .animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
  }

  .animate-confetti {
    animation: confetti 3s ease-out infinite;
  }
}
