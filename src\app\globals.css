
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: '<PERSON> Sans', sans-serif;
}

@layer base {
  :root {
    --background: 220 40% 8%; /* Deeper Dark Slate Blue */
    --foreground: 220 15% 90%; /* Brighter Light Grayish Blue */

    --card: 220 40% 12%;
    --card-foreground: 220 15% 85%;

    --popover: 220 40% 10%; /* Slightly darker popover */
    --popover-foreground: 220 15% 88%;

    --primary: 180 75% 50%; /* Brighter Teal */
    --primary-foreground: 180 50% 98%; /* Very light for text on primary */

    --secondary: 220 35% 22%;
    --secondary-foreground: 220 10% 75%;

    --muted: 220 25% 25%;
    --muted-foreground: 220 10% 65%;

    --accent: 15 95% 65%; /* Brighter Coral */
    --accent-foreground: 15 90% 8%; /* Dark brown/black for text on accent */

    --destructive: 0 75% 55%; /* Brighter Red */
    --destructive-foreground: 0 0% 98%;

    --success: 142 76% 36%; /* Green for success states */
    --success-foreground: 142 76% 95%;

    --warning: 38 92% 50%; /* Orange for warning states */
    --warning-foreground: 38 92% 95%;

    --border: 220 35% 18%;
    --input: 220 35% 15%;
    --ring: 180 75% 55%; /* Primary color for focus rings */

    --chart-1: 180 75% 50%; /* Brighter Teal (Primary) */
    --chart-2: 15 95% 65%;  /* Brighter Coral (Accent) */
    --chart-3: 200 75% 60%; /* Brighter Blue */
    --chart-4: 300 65% 65%; /* Brighter Magenta/Purple */
    --chart-5: 50 85% 60%;  /* Brighter Yellow/Gold */
    --chart-6: 142 76% 45%; /* Green */
    --chart-7: 270 75% 60%; /* Purple */
    --chart-8: 340 75% 60%; /* Pink */

    --radius: 0.5rem;

    /* Gradient variables for enhanced visuals */
    --gradient-primary: linear-gradient(135deg, hsl(180 75% 50%) 0%, hsl(200 75% 55%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(15 95% 65%) 0%, hsl(25 95% 60%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220 40% 12%) 0%, hsl(220 35% 15%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(152 76% 40%) 100%);
  }

  .dark {
    --background: 220 40% 4%; /* Even Darker Slate Blue */
    --foreground: 220 15% 85%; /* Brighter text for dark mode */

    --card: 220 40% 8%;
    --card-foreground: 220 15% 80%;

    --popover: 220 40% 6%;
    --popover-foreground: 220 15% 85%;

    --primary: 180 75% 55%; /* Brighter Teal for Dark Mode */
    --primary-foreground: 180 50% 8%; /* Dark text on bright teal */

    --secondary: 220 35% 18%;
    --secondary-foreground: 220 10% 70%;

    --muted: 220 25% 20%;
    --muted-foreground: 220 10% 60%;

    --accent: 15 95% 70%; /* Brighter Coral for Dark Mode */
    --accent-foreground: 15 90% 5%; /* Dark text on bright coral */

    --destructive: 0 75% 58%;
    --destructive-foreground: 0 0% 98%;

    --success: 142 76% 40%;
    --success-foreground: 142 76% 95%;

    --warning: 38 92% 55%;
    --warning-foreground: 38 92% 95%;

    --border: 220 35% 15%;
    --input: 220 35% 12%;
    --ring: 180 75% 60%; /* Brighter Teal for ring */

    /* Enhanced gradients for dark mode */
    --gradient-primary: linear-gradient(135deg, hsl(180 75% 55%) 0%, hsl(200 75% 60%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(15 95% 70%) 0%, hsl(25 95% 65%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220 40% 8%) 0%, hsl(220 35% 12%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142 76% 40%) 0%, hsl(152 76% 45%) 100%);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    @apply min-h-screen antialiased;
  }
  .container {
    @apply px-2 sm:px-4;
  }
}

@layer components {
  /* Enhanced Card Styles */
  .card-enhanced {
    @apply bg-gradient-to-br from-card to-card/80 backdrop-blur-sm border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* Gradient Button Styles */
  .btn-gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-primary-foreground font-medium transition-all duration-300 transform hover:scale-105 active:scale-95;
  }

  .btn-gradient-accent {
    @apply bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 text-accent-foreground font-medium transition-all duration-300 transform hover:scale-105 active:scale-95;
  }

  .btn-gradient-success {
    @apply bg-gradient-to-r from-success to-success/80 hover:from-success/90 hover:to-success/70 text-success-foreground font-medium transition-all duration-300 transform hover:scale-105 active:scale-95;
  }

  /* Enhanced Input Styles */
  .input-enhanced {
    @apply bg-input/50 backdrop-blur-sm border border-border/50 focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300;
  }

  /* Progress Bar Enhancements */
  .progress-enhanced {
    @apply bg-gradient-to-r from-muted to-muted/50 overflow-hidden;
  }

  .progress-enhanced > div {
    @apply bg-gradient-to-r from-primary to-primary/80 transition-all duration-500 ease-out;
  }

  /* Achievement Badge Styles */
  .achievement-badge {
    @apply inline-flex items-center gap-2 px-3 py-1 rounded-full bg-gradient-to-r from-accent to-accent/80 text-accent-foreground text-sm font-medium shadow-lg animate-pulse;
  }

  /* Floating Animation */
  .float-animation {
    animation: float 3s ease-in-out infinite;
  }

  /* Glow Effect */
  .glow-primary {
    @apply shadow-lg shadow-primary/20;
  }

  .glow-accent {
    @apply shadow-lg shadow-accent/20;
  }

  .glow-success {
    @apply shadow-lg shadow-success/20;
  }
}

@layer utilities {
  /* Custom Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  @keyframes confetti {
    0% {
      transform: translateY(0) rotate(0deg);
      opacity: 1;
    }
    100% {
      transform: translateY(-100vh) rotate(720deg);
      opacity: 0;
    }
  }

  /* Animation Classes */
  .animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    background-size: 200px 100%;
    animation: shimmer 2s infinite;
  }

  .animate-confetti {
    animation: confetti 3s ease-out infinite;
  }
}
