
'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Wallet } from 'lucide-react';
import { motion } from 'framer-motion';

interface IncomeInputProps {
  totalIncome: number;
  onIncomeChange: (amount: number) => void;
  balancesVisible: boolean;
}

export default function IncomeInput({ totalIncome, onIncomeChange, balancesVisible }: IncomeInputProps) {
  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    if (balancesVisible) {
      // Preserve the behavior of showing an empty string if totalIncome is 0 and input is not focused (simulated)
      // For simplicity in this controlled component, we'll just set it to totalIncome.toString()
      // The "empty string if 0 and not focused" logic is tricky without direct focus state management here.
      setInputValue(totalIncome.toString());
    } else {
      setInputValue('••••');
    }
  }, [totalIncome, balancesVisible]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const currentVal = event.target.value;
    
    if (balancesVisible) {
      setInputValue(currentVal);
    }
    // Always try to parse and update the underlying income value
    const amount = currentVal === '' ? 0 : parseFloat(currentVal);
    if (!isNaN(amount) && amount >= 0) {
      onIncomeChange(amount);
    } else if (currentVal === '') {
      onIncomeChange(0);
    }
    // If balances are hidden, useEffect will reset inputValue to '••••' after totalIncome updates
  };

  return (
    <Card className="card-enhanced glow-primary">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2 font-headline">
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          >
            <Wallet className="h-5 w-5 text-primary" />
          </motion.div>
          Total Income
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-3">
          <Label htmlFor="totalIncome" className="sr-only">Total Income</Label>
          <motion.span
            className="text-xl font-bold text-primary"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            R
          </motion.span>
          <Input
            id="totalIncome"
            type={balancesVisible ? "number" : "text"}
            placeholder="e.g., 6000"
            value={inputValue}
            onChange={handleInputChange}
            className="text-xl h-12 flex-grow input-enhanced font-semibold focus:ring-2 focus:ring-primary/20 transition-all duration-300"
            min="0"
            step="any"
            readOnly={!balancesVisible && inputValue === '••••'}
          />
        </div>
        {totalIncome > 0 && (
          <motion.div
            className="mt-3 text-xs text-muted-foreground text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            Monthly income set successfully
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
