(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{30347:()=>{},52558:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>w});var a=r(95155),o=r(87481),s=r(12115),i=r(26621),n=r(74466),l=r(25318),d=r(59434);let c=i.Kq,u=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(i.LM,{ref:t,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});u.displayName=i.LM.displayName;let p=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=s.forwardRef((e,t)=>{let{className:r,variant:o,...s}=e;return(0,a.jsx)(i.bL,{ref:t,className:(0,d.cn)(p({variant:o}),r),...s})});f.displayName=i.bL.displayName,s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(i.rc,{ref:t,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...o})}).displayName=i.rc.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(i.bm,{ref:t,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})});h.displayName=i.bm.displayName;let g=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(i.hE,{ref:t,className:(0,d.cn)("text-sm font-semibold",r),...o})});g.displayName=i.hE.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(i.VY,{ref:t,className:(0,d.cn)("text-sm opacity-90",r),...o})});function w(){let{toasts:e}=(0,o.dj)();return(0,a.jsxs)(c,{children:[e.map(function(e){let{id:t,title:r,description:o,action:s,...i}=e;return(0,a.jsxs)(f,{...i,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[r&&(0,a.jsx)(g,{children:r}),o&&(0,a.jsx)(m,{children:o})]}),s,(0,a.jsx)(h,{})]},t)}),(0,a.jsx)(u,{})]})}m.displayName=i.VY.displayName},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(52596),o=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}},87481:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p});var a=r(12115);let o=0,s=new Map,i=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=n(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,r=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function p(){let[e,t]=a.useState(d);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},89552:(e,t,r)=>{Promise.resolve().then(r.bind(r,48031)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,52558)),Promise.resolve().then(r.bind(r,90925))},90925:(e,t,r)=>{"use strict";let a,o,s;r.d(t,{AuthProvider:()=>h,A:()=>f});var i=r(95155),n=r(12115),l=r(93004),d=r(23915),c=r(90858);if((0,d.Dk)().length)a=(0,d.Sx)(),console.log("Firebase app already initialized, getting existing app.");else try{a=(0,d.Wp)({apiKey:"AIzaSyBGok32mQZDRrbt9yQ0VTuZSqzLIF7xj7A",authDomain:"budgetwise-1nj2w.firebaseapp.com",projectId:"budgetwise-1nj2w",storageBucket:"budgetwise-1nj2w.appspot.com",messagingSenderId:"153665844551",appId:"1:153665844551:web:dc9f0ba3384c9b23c1c862"}),console.log("Firebase app initialized successfully with hardcoded config.")}catch(e){console.error("Firebase initialization error with hardcoded config:",e),a=void 0}if(a){try{o=(0,l.xI)(a),console.log("Firebase Auth instance obtained successfully.")}catch(e){console.error("Firebase getAuth error after app initialization:",e),o=void 0}try{s=(0,c.c7)(a),console.log("Firebase Storage instance obtained successfully.")}catch(e){console.error("Firebase getStorage error after app initialization:",e),s=void 0}}else console.error("Firebase app was not initialized successfully, auth/storage instances cannot be created."),o=void 0,s=void 0;var u=r(35695);let p=(0,n.createContext)(void 0);function f(){let e=(0,n.useContext)(p);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h(e){let{children:t}=e,[r,a]=(0,n.useState)(null),[d,f]=(0,n.useState)(!0),[h,g]=(0,n.useState)(null),m=(0,u.useRouter)();(0,n.useEffect)(()=>{let e=(0,l.hg)(o,e=>{a(e),f(!1)});return()=>e()},[]);let w=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An unknown error occurred.",r=e.message||t;return"auth/user-not-found"===e.code&&(r="No user found with this email."),"auth/wrong-password"===e.code&&(r="Incorrect password."),"auth/email-already-in-use"===e.code&&(r="This email is already registered."),"auth/weak-password"===e.code&&(r="Password must be at least 6 characters."),"auth/popup-closed-by-user"===e.code&&(r="Google Sign-In cancelled."),"auth/requires-recent-login"===e.code&&(r="This operation is sensitive and requires recent authentication. Please log in again."),g(r),console.error("Auth Error:",e.code,e.message),null},v=async(e,t)=>{f(!0),g(null);try{let r=await (0,l.eJ)(o,e,t);return a(r.user),f(!1),r.user}catch(e){return f(!1),w(e,"Sign up failed.")}},y=async(e,t)=>{f(!0),g(null);try{let r=await (0,l.x9)(o,e,t);return a(r.user),f(!1),r.user}catch(e){return f(!1),w(e,"Sign in failed.")}},b=async()=>{f(!0),g(null);let e=new l.HF;try{let t=await (0,l.df)(o,e);return a(t.user),f(!1),t.user}catch(e){return f(!1),w(e,"Google sign-in failed.")}},x=async()=>{f(!0),g(null);try{await (0,l.CI)(o),a(null),m.push("/login")}catch(e){w(e,"Sign out failed.")}finally{f(!1)}},S=async(e,t)=>{if(!r)throw Error("User not authenticated.");f(!0),g(null);let i=r.photoURL;if(null===t){if(r.photoURL){let e=new RegExp("profilePictures%2F".concat(r.uid,"%2F([^?]+)")),t=r.photoURL.match(e);if(t&&t[1]){let e=decodeURIComponent(t[1]),a="profilePictures/".concat(r.uid,"/").concat(e);try{await (0,c.XR)((0,c.KR)(s,a))}catch(e){"storage/object-not-found"!==e.code&&console.warn("Failed to delete old photo by derived path ".concat(a,":"),e)}}else console.warn("Could not derive old photo path from URL for deletion.")}i=null}else if(t){let e=t.name.split(".").pop()||"jpg",a="profilePictures/".concat(r.uid,"/profileImage.").concat(e),o=(0,c.KR)(s,a);if(r.photoURL){let e=new RegExp("profilePictures%2F".concat(r.uid,"%2F([^?]+)")),t=r.photoURL.match(e);if(t&&t[1]){let e=decodeURIComponent(t[1]),o="profilePictures/".concat(r.uid,"/").concat(e);if(o!==a)try{await (0,c.XR)((0,c.KR)(s,o))}catch(e){"storage/object-not-found"!==e.code&&console.warn("Failed to delete old photo ".concat(o," before new upload:"),e)}}}let n=(0,c.bp)(o,t);await new Promise((e,t)=>{n.on("state_changed",()=>{},e=>{w(e,"Photo upload failed."),t(e)},async()=>{try{i=await (0,c.qk)(n.snapshot.ref),e()}catch(e){w(e,"Failed to get photo download URL."),t(e)}})})}try{await (0,l.r7)(r,{displayName:null===e?r.displayName:e,photoURL:i}),a(o.currentUser)}catch(e){throw w(e,"Profile update failed."),e}finally{f(!1)}},N=async(e,t)=>{if(!r||!r.email)throw Error("User not authenticated or email missing.");f(!0),g(null);let a=l.IX.credential(r.email,e);try{await (0,l.kZ)(r,a),await (0,l.f3)(r,t)}catch(e){throw w(e,"Password change failed."),e}finally{f(!1)}};return(0,i.jsx)(p.Provider,{value:{currentUser:r,loading:d,error:h,signUp:v,signIn:y,signInWithGoogle:b,signOut:x,clearError:()=>g(null),updateUserProfile:S,changeUserPassword:N},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[690,73,671,422,904,441,684,358],()=>t(89552)),_N_E=e.O()}]);