(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{519:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2V5z",key:"1ivx2i"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h.01",key:"xkw8gn"}]])},31949:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(40157).A)("<PERSON><PERSON><PERSON><PERSON>",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},54002:(e,a,s)=>{Promise.resolve().then(s.bind(s,56831))},55365:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>c,TN:()=>o,XL:()=>d});var t=s(95155),i=s(12115),r=s(74466),l=s(59434);let n=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=i.forwardRef((e,a)=>{let{className:s,variant:i,...r}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,l.cn)(n({variant:i}),s),...r})});c.displayName="Alert";let d=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("h5",{ref:a,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",s),...i})});d.displayName="AlertTitle";let o=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",s),...i})});o.displayName="AlertDescription"},56831:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>A});var t=s(95155),i=s(12115),r=s(6874),l=s.n(r),n=s(35695),c=s(1978),d=s(90925),o=s(62177),m=s(90221),x=s(55594),u=s(30285),h=s(62523),p=s(85057),y=s(17759),j=s(55365),f=s(31949);let g=x.Ik({email:x.Yj().email({message:"Invalid email address."}),password:x.Yj().min(1,{message:"Password is required."})});function v(){let{signIn:e,error:a,clearError:s}=(0,d.A)(),[r,l]=(0,i.useState)(!1),c=(0,n.useRouter)(),x=(0,o.mN)({resolver:(0,m.u)(g),defaultValues:{email:"",password:""}}),v=async a=>{l(!0),s();let t=await e(a.email,a.password);l(!1),t&&c.push("/")};return(0,t.jsx)(y.lV,{...x,children:(0,t.jsxs)("form",{onSubmit:x.handleSubmit(v),className:"space-y-4",children:[a&&(0,t.jsxs)(j.Fc,{variant:"destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(j.XL,{children:"Login Failed"}),(0,t.jsx)(j.TN,{children:a})]}),(0,t.jsx)(y.zB,{control:x.control,name:"email",render:e=>{let{field:a}=e;return(0,t.jsxs)(y.eI,{children:[(0,t.jsx)(p.J,{htmlFor:"email",children:"Email"}),(0,t.jsx)(y.MJ,{children:(0,t.jsx)(h.p,{id:"email",type:"email",placeholder:"<EMAIL>",...a})}),(0,t.jsx)(y.C5,{})]})}}),(0,t.jsx)(y.zB,{control:x.control,name:"password",render:e=>{let{field:a}=e;return(0,t.jsxs)(y.eI,{children:[(0,t.jsx)(p.J,{htmlFor:"password",children:"Password"}),(0,t.jsx)(y.MJ,{children:(0,t.jsx)(h.p,{id:"password",type:"password",placeholder:"••••••••",...a})}),(0,t.jsx)(y.C5,{})]})}}),(0,t.jsx)(u.$,{type:"submit",className:"w-full",disabled:r,children:r?"Signing In...":"Sign In"})]})})}let N=(0,s(40157).A)("Chrome",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["line",{x1:"21.17",x2:"12",y1:"8",y2:"8",key:"a0cw5f"}],["line",{x1:"3.95",x2:"8.54",y1:"6.06",y2:"14",key:"1kftof"}],["line",{x1:"10.88",x2:"15.46",y1:"21.94",y2:"14",key:"1ymyh8"}]]);function b(){let{signInWithGoogle:e,error:a,clearError:s}=(0,d.A)(),[r,l]=(0,i.useState)(!1),c=(0,n.useRouter)(),o=async()=>{l(!0),s();let a=await e();l(!1),a&&c.push("/")};return(0,t.jsxs)(t.Fragment,{children:[a&&(0,t.jsxs)(j.Fc,{variant:"destructive",className:"mb-4",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)(j.XL,{children:"Google Sign-In Failed"}),(0,t.jsx)(j.TN,{children:a})]}),(0,t.jsxs)(u.$,{variant:"outline",className:"w-full",onClick:o,disabled:r,children:[(0,t.jsx)(N,{className:"mr-2 h-4 w-4"})," ",r?"Signing in with Google...":"Sign in with Google"]})]})}var w=s(66695),k=s(519);function A(){let{currentUser:e,loading:a}=(0,d.A)(),s=(0,n.useRouter)();return((0,i.useEffect)(()=>{!a&&e&&s.push("/")},[e,a,s]),a)?(0,t.jsx)("div",{className:"flex justify-center items-center min-h-screen bg-background",children:(0,t.jsxs)(c.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):!a&&e?null:(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-background p-4 relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-accent/5"}),(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"relative z-10",children:(0,t.jsxs)(w.Zp,{className:"w-full max-w-md card-hover glass",children:[(0,t.jsxs)(w.aR,{className:"text-center",children:[(0,t.jsx)(c.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"flex justify-center items-center mb-4",children:(0,t.jsx)("div",{className:"p-3 rounded-full bg-gradient-to-r from-primary to-primary/80",children:(0,t.jsx)(k.A,{className:"h-12 w-12 text-primary-foreground"})})}),(0,t.jsxs)(c.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,t.jsx)(w.ZB,{className:"text-2xl font-headline bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",children:"Welcome Back to BudgetWise!"}),(0,t.jsx)(w.BT,{className:"mt-2",children:"Sign in to manage your finances."})]})]}),(0,t.jsxs)(w.Wu,{className:"space-y-6",children:[(0,t.jsx)(c.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:(0,t.jsx)(v,{})}),(0,t.jsxs)(c.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("span",{className:"w-full border-t border-border/50"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,t.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,t.jsx)(c.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,t.jsx)(b,{})}),(0,t.jsxs)(c.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.7},className:"text-center text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,t.jsx)(l(),{href:"/register",className:"font-medium text-primary hover:underline transition-colors",children:"Sign up"})]})]})]})})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[73,671,440,521,375,441,684,358],()=>a(54002)),_N_E=e.O()}]);