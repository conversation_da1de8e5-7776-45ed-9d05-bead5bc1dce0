(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[575],{675:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},2160:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},2348:(t,e,r)=>{"use strict";r.d(e,{W:()=>c});var n=r(12115),o=r(52596),i=r(70788),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=n.forwardRef(function(t,e){var r=t.children,c=t.className,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),s=(0,o.A)("recharts-layer",c);return n.createElement("g",u({className:s},(0,i.J9)(l,!0),{ref:e}),r)})},2494:(t,e,r)=>{"use strict";r.d(e,{s:()=>u});var n=r(3711),o=r.n(n),i=r(40139),a=r.n(i);function u(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},2564:(t,e,r)=>{"use strict";r.d(e,{s:()=>a});var n=r(12115),o=r(63655),i=r(95155),a=n.forwardRef((t,e)=>(0,i.jsx)(o.sG.span,{...t,ref:e,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...t.style}}));a.displayName="VisuallyHidden"},2682:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("ListCollapse",[["path",{d:"m3 10 2.5-2.5L3 5",key:"i6eama"}],["path",{d:"m3 19 2.5-2.5L3 14",key:"w2gmor"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}]])},3562:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},3698:(t,e,r)=>{var n=r(77969),o=r(69363);t.exports=function(t,e){return n(o(t,e),1)}},3711:(t,e,r)=>{var n=r(18028),o=r(65836);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},4217:(t,e,r)=>{var n=r(36713),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},4854:(t,e,r)=>{var n=r(67472),o=r(51911);t.exports=function(t,e,r,i){var a=r.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var l=r[a];if(c&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<u;){var s=(l=r[a])[0],f=t[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in t))return!1}else{var d=new n;if(i)var h=i(f,p,s,t,e,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},5516:(t,e,r)=>{var n=r(5658);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},5658:(t,e,r)=>{var n=r(30699);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},6305:(t,e,r)=>{var n=r(53516),o=r(22471);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},6997:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},7512:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},7548:(t,e,r)=>{var n=r(16746);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},7771:(t,e,r)=>{var n=r(31598),o=r(18686),i=r(88748);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},7985:(t,e,r)=>{t.exports="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g},8870:function(t,e,r){var n;!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),y={};function v(t,e){var r,n,o,i,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?P(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=o,u?P(e,p):e}function m(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function g(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=j(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=j(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return b(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return P(b(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(i))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(u=!1,e=b(A(this,o),A(t,o),o),u=!0,P(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c+"NaN");return this.s?(u=!1,e=b(this,t,0,1).times(t),u=!0,this.minus(e)):P(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return A(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(c+"NaN")}for(t=w(this),u=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=g(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new l(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(o.toString()),o=a=(r=l.precision)+3;;)if(n=(i=n).plus(b(this,i,a+2)).times(.5),g(i.d).slice(0,a)===(e=g(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(P(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return u=!0,P(n,r)},y.times=y.mul=function(t){var e,r,n,o,i,a,c,l,s,f=this.constructor,p=this.d,d=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(l=p.length)<(s=d.length)&&(i=p,p=d,d=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=l+n;o>n;)c=i[o]+d[n]*p[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,u?P(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(m(t,0,1e9),void 0===e?e=n.rounding:m(e,0,8),P(r,t+w(r)+1,e))},y.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=k(n,!0):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k(n=P(new o(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?k(this):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k((n=P(new o(this),t+w(this)+1,e)).abs(),!1,t+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return P(new t(this),w(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,o,a,l,s=this,p=s.constructor,d=+(t=new p(t));if(!t.s)return new p(i);if(!(s=new p(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(i))return s;if(n=p.precision,t.eq(i))return P(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=d<0?-d:d)<=0x1fffffffffffff){for(o=new p(i),e=Math.ceil(n/7+4),u=!1;r%2&&M((o=o.times(s)).d,e),0!==(r=f(r/2));)M((s=s.times(s)).d,e);return u=!0,t.s<0?new p(i).div(o):P(o,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,u=!1,o=t.times(A(s,n+12)),u=!0,(o=x(o)).s=a,o},y.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=w(o),n=k(o,r<=i.toExpNeg||r>=i.toExpPos)):(m(t,1,1e9),void 0===e?e=i.rounding:m(e,0,8),r=w(o=P(new i(o),t,e)),n=k(o,t<=r||r<=i.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(m(t,1,1e9),void 0===e?e=r.rounding:m(e,0,8)),P(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=w(this),e=this.constructor;return k(this,t<=e.toExpNeg||t>=e.toExpPos)};var b=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var u,l,s,f,p,d,h,y,v,m,g,b,x,O,j,A,S,E,k=n.constructor,M=n.s==o.s?1:-1,_=n.d,T=o.d;if(!n.s)return new k(n);if(!o.s)throw Error(c+"Division by zero");for(s=0,l=n.e-o.e,S=T.length,j=_.length,y=(h=new k(M)).d=[];T[s]==(_[s]||0);)++s;if(T[s]>(_[s]||0)&&--l,(b=null==i?i=k.precision:a?i+(w(n)-w(o))+1:i)<0)return new k(0);if(b=b/7+2|0,s=0,1==S)for(f=0,T=T[0],b++;(s<j||f)&&b--;s++)x=1e7*f+(_[s]||0),y[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=t(T,f),_=t(_,f),S=T.length,j=_.length),O=S,m=(v=_.slice(0,S)).length;m<S;)v[m++]=0;(E=T.slice()).unshift(0),A=T[0],T[1]>=1e7/2&&++A;do f=0,(u=e(T,v,S,m))<0?(g=v[0],S!=m&&(g=1e7*g+(v[1]||0)),(f=g/A|0)>1?(f>=1e7&&(f=1e7-1),d=(p=t(T,f)).length,m=v.length,1==(u=e(p,v,d,m))&&(f--,r(p,S<d?E:T,d))):(0==f&&(u=f=1),p=T.slice()),(d=p.length)<m&&p.unshift(0),r(v,p,m),-1==u&&(m=v.length,(u=e(T,v,S,m))<1&&(f++,r(v,S<m?E:T,m))),m=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[m++]=_[O]||0:(v=[_[O]],m=1);while((O++<j||void 0!==v[0])&&b--)}return y[0]||y.shift(),h.e=l,P(h,a?i+w(h)+1:i)}}();function x(t,e){var r,n,o,a,c,l=0,f=0,d=t.constructor,h=d.precision;if(w(t)>16)throw Error(s+w(t));if(!t.s)return new d(i);for(null==e?(u=!1,c=h):c=e,a=new d(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=o=new d(i),d.precision=c;;){if(n=P(n.times(t),c),r=r.times(++l),g((a=o.plus(b(n,r,c))).d).slice(0,c)===g(o.d).slice(0,c)){for(;f--;)o=P(o.times(o),c);return d.precision=h,null==e?(u=!0,P(o,h)):o}o=a}}function w(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function O(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(c+"LN10 precision limit exceeded");return P(new t(t.LN10),e)}function j(t){for(var e="";t--;)e+="0";return e}function A(t,e){var r,n,o,a,l,s,f,p,d,h=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==e?(u=!1,p=x):p=e,y.eq(10))return null==e&&(u=!0),O(m,p);if(m.precision=p+=10,n=(r=g(v)).charAt(0),!(15e14>Math.abs(a=w(y))))return f=O(m,p+2,x).times(a+""),y=A(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==e?(u=!0,P(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=g((y=y.times(t)).d)).charAt(0),h++;for(a=w(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=b(y.minus(i),y.plus(i),p),d=P(y.times(y),p),o=3;;){if(l=P(l.times(d),p),g((f=s.plus(b(l,new m(o),p))).d).slice(0,p)===g(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(O(m,p+2,x).times(a+""))),s=b(s,new m(h),p),m.precision=x,null==e?(u=!0,P(s,x)):s;s=f,o+=2}}function S(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=f((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>h||t.e<-h))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function P(t,e,r){var n,o,i,a,c,l,d,y,v=t.d;for(a=1,i=v[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,d=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=v.length))return t;for(a=1,d=i=v[y];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=d/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==v[y+1]||d%i,l=r<4?(c||l)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?d/p(10,a-o):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return l?(i=w(t),v.length=1,e=e-i-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,i=1,y--):(v.length=y+1,i=p(10,7-n),v[y]=o>0?(d/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;){if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++t.e);break}if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(t.e>h||t.e<-h))throw Error(s+w(t));return t}function E(t,e){var r,n,o,i,a,c,l,s,f,p,d=t.constructor,h=d.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new d(t),u?P(e,h):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=p.length):(r=p,n=s,c=l.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,u?P(e,h):e):new d(0)}function k(t,e,r){var n,o=w(t),i=g(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,r&&(n=r-a)>0&&(i+=j(n))):o>=a?(i+=j(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+j(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=j(n))),t.s<0?"-"+i:i}function M(t,e){if(t.length>e)return t.length=e,!0}function _(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(f(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(l+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n)}return this}(a=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return S(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,d.test(t))S(this,t);else throw Error(l+t)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=_,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(a)).default=a.Decimal=a,i=new a(1),void 0!==(n=(function(){return a}).call(e,r,e,t))&&(t.exports=n)}(0)},9041:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]])},9557:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>tA});var n=r(12115),o=r(38637),i=r.n(o),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function s(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var u=t(e,r,n);return o.delete(e),o.delete(r),u}}function f(t){return a(t).concat(u(t))}var p=Object.hasOwn||function(t,e){return c.call(t,e)};function d(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var h=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return d(t.getTime(),e.getTime())}function g(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function b(t,e){return t===e}function x(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.entries(),c=0;(n=u.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],c,f,t,e,r)&&r.equals(p[1],d[1],p[0],d[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function w(t,e,r){var n=y(t),o=n.length;if(y(e).length!==o)return!1;for(;o-- >0;)if(!k(t,e,r,n[o]))return!1;return!0}function O(t,e,r){var n,o,i,a=f(t),u=a.length;if(f(e).length!==u)return!1;for(;u-- >0;)if(!k(t,e,r,n=a[u])||(o=h(t,n),i=h(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function j(t,e){return d(t.valueOf(),e.valueOf())}function A(t,e){return t.source===e.source&&t.flags===e.flags}function S(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.values();(n=u.next())&&!n.done;){for(var c=e.values(),l=!1,s=0;(o=c.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function P(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function E(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function k(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var M=Array.isArray,_="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,T=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString),I=D();function D(t){void 0===t&&(t={});var e,r,n,o,i,a,u,c,f,p,h,y,k,I=t.circular,D=t.createInternalComparator,N=t.createState,R=t.strict,B=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?O:v,areDatesEqual:m,areErrorsEqual:g,areFunctionsEqual:b,areMapsEqual:n?l(x,O):x,areNumbersEqual:d,areObjectsEqual:n?O:w,arePrimitiveWrappersEqual:j,areRegExpsEqual:A,areSetsEqual:n?l(S,O):S,areTypedArraysEqual:n?O:P,areUrlsEqual:E};if(r&&(o=T({},o,r(o))),e){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),u=s(o.areObjectsEqual),c=s(o.areSetsEqual);o=T({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,u=e.areNumbersEqual,c=e.areObjectsEqual,f=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,h=e.areSetsEqual,y=e.areTypedArraysEqual,k=e.areUrlsEqual,function(t,e,l){if(t===e)return!0;if(null==t||null==e)return!1;var s=typeof t;if(s!==typeof e)return!1;if("object"!==s)return"number"===s?u(t,e,l):"function"===s&&i(t,e,l);var d=t.constructor;if(d!==e.constructor)return!1;if(d===Object)return c(t,e,l);if(M(t))return r(t,e,l);if(null!=_&&_(t))return y(t,e,l);if(d===Date)return n(t,e,l);if(d===RegExp)return p(t,e,l);if(d===Map)return a(t,e,l);if(d===Set)return h(t,e,l);var v=C(t);return"[object Date]"===v?n(t,e,l):"[object RegExp]"===v?p(t,e,l):"[object Map]"===v?a(t,e,l):"[object Set]"===v?h(t,e,l):"[object Object]"===v?"function"!=typeof t.then&&"function"!=typeof e.then&&c(t,e,l):"[object URL]"===v?k(t,e,l):"[object Error]"===v?o(t,e,l):"[object Arguments]"===v?c(t,e,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(t,e,l)}),L=D?D(B):function(t,e,r,n,o,i,a){return B(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var u=n(),c=u.cache;return r(t,a,{cache:void 0===c?e?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==I&&I,comparator:B,createState:N,equals:L,strict:void 0!==R&&R})}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){z(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function z(t,e,r){var n;return(n=function(t,e){if("object"!==L(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===L(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}D({strict:!0}),D({circular:!0}),D({circular:!0,strict:!0}),D({createInternalComparator:function(){return d}}),D({strict:!0,createInternalComparator:function(){return d}}),D({circular:!0,createInternalComparator:function(){return d}}),D({circular:!0,createInternalComparator:function(){return d},strict:!0});var W=function(t){return t},$=function(t,e){return Object.keys(e).reduce(function(r,n){return F(F({},r),{},z({},n,t(n,e[n])))},{})},H=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},q=function(t,e,r,n,o,i,a,u){};function V(t,e){if(t){if("string"==typeof t)return X(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(t,e)}}function X(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var G=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},K=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},Y=function(t,e){return function(r){return K(G(t,e),r)}},Z=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(s,4)||V(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else q(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}q([i,u,a,c].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=Y(i,u),d=Y(a,c),h=(t=i,e=u,function(r){var n;return K([].concat(function(t){if(Array.isArray(t))return X(t)}(n=G(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||V(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=h(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},J=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,u=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,u=n*a/1e3+t;return 1e-4>Math.abs(u-e)&&1e-4>Math.abs(i)?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},Q=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Z(n);case"spring":return J();default:if("cubic-bezier"===n.split("(")[0])return Z(n);q(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(q(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(t){return function(t){if(Array.isArray(t))return ta(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ti(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){to(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(t,e,r){var n;return(n=function(t,e){if("object"!==tt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===tt(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ti(t,e){if(t){if("string"==typeof t)return ta(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ta(t,e)}}function ta(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tu=function(t,e,r){return t+(e-t)*r},tc=function(t){return t.from!==t.to},tl=function t(e,r,n){var o=$(function(t,r){if(tc(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(n,2)||ti(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return tn(tn({},r),{},{from:i,velocity:a})}return r},r);return n<1?$(function(t,e){return tc(e)?tn(tn({},e),{},{velocity:tu(e.velocity,o[t].velocity,n),from:tu(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let ts=function(t,e,r,n,o){var i,a,u=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),c=u.reduce(function(r,n){return tn(tn({},r),{},to({},n,[t[n],e[n]]))},{}),l=u.reduce(function(r,n){return tn(tn({},r),{},to({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=tl(r,l,a),o(tn(tn(tn({},t),e),$(function(t,e){return e.from},l))),i=n,Object.values(l).filter(tc).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var u=(i-a)/n,l=$(function(t,e){return tu.apply(void 0,te(e).concat([r(u)]))},c);if(o(tn(tn(tn({},t),e),l)),u<1)s=requestAnimationFrame(f);else{var p=$(function(t,e){return tu.apply(void 0,te(e).concat([r(1)]))},c);o(tn(tn(tn({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tf(t){return(tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tp=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function td(t){return function(t){if(Array.isArray(t))return th(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return th(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return th(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function th(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ty(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ty(Object(r),!0).forEach(function(e){tm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tm(t,e,r){return(e=tg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tg(t){var e=function(t,e){if("object"!==tf(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tf(e)?e:String(e)}function tb(t,e){return(tb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tx(t,e){if(e&&("object"===tf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tw(t)}function tw(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tO(t){return(tO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tj=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tb(t,e)}(i,t);var e,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tO(i);return t=e?Reflect.construct(r,arguments,tO(this).constructor):r.apply(this,arguments),tx(this,t)});function i(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i);var r=o.call(this,t,e),n=r.props,a=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tw(r)),r.changeStyle=r.changeStyle.bind(tw(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tx(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"==typeof f)return r.state={style:c},tx(r);r.state={style:u?tm({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n){if(!r){var l={style:o?tm({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(l);return}if(!I(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?u:t.to;if(this.state&&c){var p={style:o?tm({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(tv(tv({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=ts(r,n,Q(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,u=i.duration;return this.manager.start([o].concat(td(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(td(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var d=H(p,i,u),h=tv(tv(tv({},f.style),c),{},{transition:d});return[].concat(td(t),[h,i,s]).filter(W)},[a,Math.max(void 0===u?0:u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n,o;this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return B(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return B(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){N(t.bind(null,a),i);return}t(i),N(t.bind(null,a));return}"object"===R(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}})}var i=t.begin,a=t.duration,u=t.attributeName,c=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,d=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof d||"spring"===l){this.runJSAnimation(t);return}if(p.length>1){this.runStepAnimation(t);return}var y=u?tm({},u,c):c,v=H(Object.keys(y),a,l);h.start([s,i,tv(tv({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tp)),a=n.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!o||0===a||r<=0)return e;var c=function(t){var e=t.props,r=e.style,o=e.className;return(0,n.cloneElement)(t,tv(tv({},i),{},{style:tv(tv({},void 0===r?{}:r),u),className:o}))};return 1===a?c(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tg(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);tj.displayName="Animate",tj.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tj.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};let tA=tj},9699:(t,e,r)=>{var n=r(11011);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=r.length;++o<u;){var l=n(i[o],a[o]);if(l){if(o>=c)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},9795:(t,e,r)=>{"use strict";r.d(e,{i:()=>I});var n=r(12115),o=r(23633),i=r.n(o);let a=Math.cos,u=Math.sin,c=Math.sqrt,l=Math.PI,s=2*l,f={draw(t,e){let r=c(e/l);t.moveTo(r,0),t.arc(0,0,r,0,s)}},p=c(1/3),d=2*p,h=u(l/10)/u(7*l/10),y=u(s/10)*h,v=-a(s/10)*h,m=c(3),g=c(3)/2,b=1/c(12),x=(b/2+1)*3;var w=r(85654),O=r(31847);c(3),c(3);var j=r(52596),A=r(70788);function S(t){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var P=["type","size","sizeType"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=S(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=S(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==S(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var _={symbolCircle:f,symbolCross:{draw(t,e){let r=c(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=c(e/d),n=r*p;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=c(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=c(.8908130915292852*e),n=y*r,o=v*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=s*e/5,c=a(i),l=u(i);t.lineTo(l*r,-c*r),t.lineTo(c*n-l*o,l*n+c*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-c(e/(3*m));t.moveTo(0,2*r),t.lineTo(-m*r,-r),t.lineTo(m*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=c(e/x),n=r/2,o=r*b,i=r*b+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-g*o,g*n+-.5*o),t.lineTo(-.5*n-g*i,g*n+-.5*i),t.lineTo(-.5*a-g*i,g*a+-.5*i),t.lineTo(-.5*n+g*o,-.5*o-g*n),t.lineTo(-.5*n+g*i,-.5*i-g*n),t.lineTo(-.5*a+g*i,-.5*i-g*a),t.closePath()}}},T=Math.PI/180,C=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*T;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},I=function(t){var e,r=t.type,o=void 0===r?"circle":r,a=t.size,u=void 0===a?64:a,c=t.sizeType,l=void 0===c?"area":c,s=M(M({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,P)),{},{type:o,size:u,sizeType:l}),p=s.className,d=s.cx,h=s.cy,y=(0,A.J9)(s,!0);return d===+d&&h===+h&&u===+u?n.createElement("path",E({},y,{className:(0,j.A)("recharts-symbols",p),transform:"translate(".concat(d,", ").concat(h,")"),d:(e=_["symbol".concat(i()(o))]||f,(function(t,e){let r=null,n=(0,O.i)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:(0,w.A)(t||f),e="function"==typeof e?e:(0,w.A)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,w.A)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,w.A)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(C(u,l,o))())})):null};I.registerSymbol=function(t,e){_["symbol".concat(i()(t))]=e}},9813:(t,e,r)=>{var n=r(22143),o=r(48611),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!u.call(t,"callee")}},9819:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},10537:(t,e,r)=>{var n=r(96540),o=r(31598),i=r(18686);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},11011:(t,e,r)=>{var n=r(70771);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),u=void 0!==e,c=null===e,l=e==e,s=n(e);if(!c&&!s&&!a&&t>e||a&&u&&l&&!c&&!s||o&&u&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!l)return -1}return 0}},11670:(t,e,r)=>{var n=r(79401),o=r(9813),i=r(39608),a=r(33497),u=r(99544),c=r(35190),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&c(t),d=r||s||f||p,h=d?n(t.length,String):[],y=h.length;for(var v in t)(e||l.call(t,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y)))&&h.push(v);return h}},12486:t=>{t.exports=function(t){return t!=t}},13122:(t,e,r)=>{var n=r(40139),o=r(38985),i=r(67460),a=r(7512),u=/^\[object .+?Constructor\]$/,c=Object.prototype,l=Function.prototype.toString,s=c.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:u).test(a(t))}},13364:(t,e,r)=>{var n=r(75899),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},13465:t=>{t.exports=function(t){return t}},13908:(t,e,r)=>{var n=r(40566);t.exports=function(t){return n(t)&&t!=+t}},14268:(t,e,r)=>{var n=r(58918),o=r(18028),i=r(52521);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},15222:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},15232:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{b:()=>n})},15438:(t,e,r)=>{var n=r(98233),o=r(39608),i=r(48611);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},15452:(t,e,r)=>{"use strict";r.d(e,{G$:()=>X,Hs:()=>w,UC:()=>te,VY:()=>tn,ZL:()=>Q,bL:()=>Z,bm:()=>to,hE:()=>tr,hJ:()=>tt,l9:()=>J});var n=r(12115),o=r(85185),i=r(6101),a=r(46081),u=r(61285),c=r(5845),l=r(19178),s=r(25519),f=r(34378),p=r(28905),d=r(63655),h=r(92293),y=r(31114),v=r(38168),m=r(99708),g=r(95155),b="Dialog",[x,w]=(0,a.A)(b),[O,j]=x(b),A=t=>{let{__scopeDialog:e,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:l=!0}=t,s=n.useRef(null),f=n.useRef(null),[p=!1,d]=(0,c.i)({prop:o,defaultProp:i,onChange:a});return(0,g.jsx)(O,{scope:e,triggerRef:s,contentRef:f,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:p,onOpenChange:d,onOpenToggle:n.useCallback(()=>d(t=>!t),[d]),modal:l,children:r})};A.displayName=b;var S="DialogTrigger",P=n.forwardRef((t,e)=>{let{__scopeDialog:r,...n}=t,a=j(S,r),u=(0,i.s)(e,a.triggerRef);return(0,g.jsx)(d.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":q(a.open),...n,ref:u,onClick:(0,o.m)(t.onClick,a.onOpenToggle)})});P.displayName=S;var E="DialogPortal",[k,M]=x(E,{forceMount:void 0}),_=t=>{let{__scopeDialog:e,forceMount:r,children:o,container:i}=t,a=j(E,e);return(0,g.jsx)(k,{scope:e,forceMount:r,children:n.Children.map(o,t=>(0,g.jsx)(p.C,{present:r||a.open,children:(0,g.jsx)(f.Z,{asChild:!0,container:i,children:t})}))})};_.displayName=E;var T="DialogOverlay",C=n.forwardRef((t,e)=>{let r=M(T,t.__scopeDialog),{forceMount:n=r.forceMount,...o}=t,i=j(T,t.__scopeDialog);return i.modal?(0,g.jsx)(p.C,{present:n||i.open,children:(0,g.jsx)(I,{...o,ref:e})}):null});C.displayName=T;var I=n.forwardRef((t,e)=>{let{__scopeDialog:r,...n}=t,o=j(T,r);return(0,g.jsx)(y.A,{as:m.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(d.sG.div,{"data-state":q(o.open),...n,ref:e,style:{pointerEvents:"auto",...n.style}})})}),D="DialogContent",N=n.forwardRef((t,e)=>{let r=M(D,t.__scopeDialog),{forceMount:n=r.forceMount,...o}=t,i=j(D,t.__scopeDialog);return(0,g.jsx)(p.C,{present:n||i.open,children:i.modal?(0,g.jsx)(R,{...o,ref:e}):(0,g.jsx)(B,{...o,ref:e})})});N.displayName=D;var R=n.forwardRef((t,e)=>{let r=j(D,t.__scopeDialog),a=n.useRef(null),u=(0,i.s)(e,r.contentRef,a);return n.useEffect(()=>{let t=a.current;if(t)return(0,v.Eq)(t)},[]),(0,g.jsx)(L,{...t,ref:u,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(t.onCloseAutoFocus,t=>{var e;t.preventDefault(),null===(e=r.triggerRef.current)||void 0===e||e.focus()}),onPointerDownOutside:(0,o.m)(t.onPointerDownOutside,t=>{let e=t.detail.originalEvent,r=0===e.button&&!0===e.ctrlKey;(2===e.button||r)&&t.preventDefault()}),onFocusOutside:(0,o.m)(t.onFocusOutside,t=>t.preventDefault())})}),B=n.forwardRef((t,e)=>{let r=j(D,t.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,g.jsx)(L,{...t,ref:e,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{var n,a;null===(n=t.onCloseAutoFocus)||void 0===n||n.call(t,e),e.defaultPrevented||(o.current||null===(a=r.triggerRef.current)||void 0===a||a.focus(),e.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:e=>{var n,a;null===(n=t.onInteractOutside)||void 0===n||n.call(t,e),e.defaultPrevented||(o.current=!0,"pointerdown"!==e.detail.originalEvent.type||(i.current=!0));let u=e.target;(null===(a=r.triggerRef.current)||void 0===a?void 0:a.contains(u))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&i.current&&e.preventDefault()}})}),L=n.forwardRef((t,e)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:u,...c}=t,f=j(D,r),p=n.useRef(null),d=(0,i.s)(e,p);return(0,h.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:u,children:(0,g.jsx)(l.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":q(f.open),...c,ref:d,onDismiss:()=>f.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(K,{titleId:f.titleId}),(0,g.jsx)(Y,{contentRef:p,descriptionId:f.descriptionId})]})]})}),U="DialogTitle",F=n.forwardRef((t,e)=>{let{__scopeDialog:r,...n}=t,o=j(U,r);return(0,g.jsx)(d.sG.h2,{id:o.titleId,...n,ref:e})});F.displayName=U;var z="DialogDescription",W=n.forwardRef((t,e)=>{let{__scopeDialog:r,...n}=t,o=j(z,r);return(0,g.jsx)(d.sG.p,{id:o.descriptionId,...n,ref:e})});W.displayName=z;var $="DialogClose",H=n.forwardRef((t,e)=>{let{__scopeDialog:r,...n}=t,i=j($,r);return(0,g.jsx)(d.sG.button,{type:"button",...n,ref:e,onClick:(0,o.m)(t.onClick,()=>i.onOpenChange(!1))})});function q(t){return t?"open":"closed"}H.displayName=$;var V="DialogTitleWarning",[X,G]=(0,a.q)(V,{contentName:D,titleName:U,docsSlug:"dialog"}),K=t=>{let{titleId:e}=t,r=G(V),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(o)},[o,e]),null},Y=t=>{let{contentRef:e,descriptionId:r}=t,o=G("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var t;let n=null===(t=e.current)||void 0===t?void 0:t.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(i)},[i,e,r]),null},Z=A,J=P,Q=_,tt=C,te=N,tr=F,tn=W,to=H},15473:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},15631:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},16377:(t,e,r)=>{"use strict";r.d(e,{CG:()=>b,Dj:()=>x,Et:()=>d,F4:()=>m,NF:()=>v,_3:()=>p,eP:()=>w,lX:()=>g,sA:()=>f,vh:()=>h});var n=r(15438),o=r.n(n),i=r(13908),a=r.n(i),u=r(48973),c=r.n(u),l=r(40566),s=r.n(l),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return o()(t)&&t.indexOf("%")===t.length-1},d=function(t){return s()(t)&&!a()(t)},h=function(t){return d(t)||o()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!d(t)&&!o()(t))return n;if(p(t)){var u=t.indexOf("%");r=e*parseFloat(t.slice(0,u))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},g=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},b=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},x=function(t,e){return d(t)&&d(e)?function(r){return t+r*(e-t)}:function(){return e}};function w(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===r}):null}},16571:(t,e,r)=>{var n=r(50687),o=r(54906),i=r(13465);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},16613:(t,e,r)=>{var n=r(24376),o=r(57213),i=r(39608),a=r(70771),u=1/0,c=n?n.prototype:void 0,l=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-u?"-0":r}},16746:(t,e,r)=>{var n=r(3562),o=r(12486),i=r(69806);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},17489:(t,e,r)=>{var n=r(91113);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},17649:(t,e,r)=>{"use strict";r.d(e,{UC:()=>D,VY:()=>L,ZD:()=>R,ZL:()=>C,bL:()=>_,hE:()=>B,hJ:()=>I,l9:()=>T,rc:()=>N});var n=r(12115),o=r(46081),i=r(6101),a=r(15452),u=r(85185),c=r(99708),l=r(95155),s="AlertDialog",[f,p]=(0,o.A)(s,[a.Hs]),d=(0,a.Hs)(),h=t=>{let{__scopeAlertDialog:e,...r}=t,n=d(e);return(0,l.jsx)(a.bL,{...n,...r,modal:!0})};h.displayName=s;var y=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,...n}=t,o=d(r);return(0,l.jsx)(a.l9,{...o,...n,ref:e})});y.displayName="AlertDialogTrigger";var v=t=>{let{__scopeAlertDialog:e,...r}=t,n=d(e);return(0,l.jsx)(a.ZL,{...n,...r})};v.displayName="AlertDialogPortal";var m=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,...n}=t,o=d(r);return(0,l.jsx)(a.hJ,{...o,...n,ref:e})});m.displayName="AlertDialogOverlay";var g="AlertDialogContent",[b,x]=f(g),w=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,children:o,...s}=t,f=d(r),p=n.useRef(null),h=(0,i.s)(e,p),y=n.useRef(null);return(0,l.jsx)(a.G$,{contentName:g,titleName:O,docsSlug:"alert-dialog",children:(0,l.jsx)(b,{scope:r,cancelRef:y,children:(0,l.jsxs)(a.UC,{role:"alertdialog",...f,...s,ref:h,onOpenAutoFocus:(0,u.m)(s.onOpenAutoFocus,t=>{var e;t.preventDefault(),null===(e=y.current)||void 0===e||e.focus({preventScroll:!0})}),onPointerDownOutside:t=>t.preventDefault(),onInteractOutside:t=>t.preventDefault(),children:[(0,l.jsx)(c.xV,{children:o}),(0,l.jsx)(M,{contentRef:p})]})})})});w.displayName=g;var O="AlertDialogTitle",j=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,...n}=t,o=d(r);return(0,l.jsx)(a.hE,{...o,...n,ref:e})});j.displayName=O;var A="AlertDialogDescription",S=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,...n}=t,o=d(r);return(0,l.jsx)(a.VY,{...o,...n,ref:e})});S.displayName=A;var P=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,...n}=t,o=d(r);return(0,l.jsx)(a.bm,{...o,...n,ref:e})});P.displayName="AlertDialogAction";var E="AlertDialogCancel",k=n.forwardRef((t,e)=>{let{__scopeAlertDialog:r,...n}=t,{cancelRef:o}=x(E,r),u=d(r),c=(0,i.s)(e,o);return(0,l.jsx)(a.bm,{...u,...n,ref:c})});k.displayName=E;var M=t=>{let{contentRef:e}=t,r="`".concat(g,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(g,"` by passing a `").concat(A,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(g,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var t;document.getElementById(null===(t=e.current)||void 0===t?void 0:t.getAttribute("aria-describedby"))||console.warn(r)},[r,e]),null},_=h,T=y,C=v,I=m,D=w,N=P,R=k,B=j,L=S},17855:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(c+u+l),"g");t.exports=function(t){return t.match(s)||[]}},18028:(t,e,r)=>{var n=r(55910),o=r(96699),i=r(13465),a=r(39608),u=r(28126);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):u(t)}},18186:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},18489:(t,e,r)=>{var n=r(96294),o=r(72043),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},18686:(t,e,r)=>{t.exports=r(83711)(r(82500),"Map")},18940:(t,e,r)=>{t.exports=r(64189)()},19591:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},20134:(t,e,r)=>{var n=r(86452),o=r(50111);t.exports=function(t,e){return null!=t&&o(t,e,n)}},20480:(t,e,r)=>{var n=r(86216),o=r(35095);t.exports=function(t,e){return t&&n(t,e,o)}},20570:(t,e,r)=>{var n=r(24376),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,u=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,u),r=t[u];try{t[u]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[u]=r:delete t[u]),o}},20963:(t,e,r)=>{var n=r(65646),o=r(38649),i=r(35095);t.exports=function(t){return n(t,i,o)}},20988:(t,e,r)=>{var n=r(75899);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},21087:(t,e,r)=>{var n=r(13465),o=r(64588),i=r(61632);t.exports=function(t,e){return i(o(t,e,n),t+"")}},21582:t=>{t.exports=function(t,e){return t>e}},21790:(t,e,r)=>{var n=r(54360);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},22143:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},22315:(t,e,r)=>{var n=r(58918),o=r(21582),i=r(13465);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},22471:(t,e,r)=>{var n=r(40139),o=r(15631);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},23360:(t,e,r)=>{var n=r(42233);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},23478:(t,e,r)=>{"use strict";r.d(e,{UC:()=>ta,Y9:()=>to,q7:()=>tn,bL:()=>tr,l9:()=>ti});var n=r(12115),o=r(46081),i=r(82284),a=r(6101),u=r(85185),c=r(5845),l=r(63655),s=r(52712),f=r(28905),p=r(61285),d=r(95155),h="Collapsible",[y,v]=(0,o.A)(h),[m,g]=y(h),b=n.forwardRef((t,e)=>{let{__scopeCollapsible:r,open:o,defaultOpen:i,disabled:a,onOpenChange:u,...s}=t,[f=!1,h]=(0,c.i)({prop:o,defaultProp:i,onChange:u});return(0,d.jsx)(m,{scope:r,disabled:a,contentId:(0,p.B)(),open:f,onOpenToggle:n.useCallback(()=>h(t=>!t),[h]),children:(0,d.jsx)(l.sG.div,{"data-state":S(f),"data-disabled":a?"":void 0,...s,ref:e})})});b.displayName=h;var x="CollapsibleTrigger",w=n.forwardRef((t,e)=>{let{__scopeCollapsible:r,...n}=t,o=g(x,r);return(0,d.jsx)(l.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":S(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:e,onClick:(0,u.m)(t.onClick,o.onOpenToggle)})});w.displayName=x;var O="CollapsibleContent",j=n.forwardRef((t,e)=>{let{forceMount:r,...n}=t,o=g(O,t.__scopeCollapsible);return(0,d.jsx)(f.C,{present:r||o.open,children:t=>{let{present:r}=t;return(0,d.jsx)(A,{...n,ref:e,present:r})}})});j.displayName=O;var A=n.forwardRef((t,e)=>{let{__scopeCollapsible:r,present:o,children:i,...u}=t,c=g(O,r),[f,p]=n.useState(o),h=n.useRef(null),y=(0,a.s)(e,h),v=n.useRef(0),m=v.current,b=n.useRef(0),x=b.current,w=c.open||f,j=n.useRef(w),A=n.useRef(void 0);return n.useEffect(()=>{let t=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,s.N)(()=>{let t=h.current;if(t){A.current=A.current||{transitionDuration:t.style.transitionDuration,animationName:t.style.animationName},t.style.transitionDuration="0s",t.style.animationName="none";let e=t.getBoundingClientRect();v.current=e.height,b.current=e.width,j.current||(t.style.transitionDuration=A.current.transitionDuration,t.style.animationName=A.current.animationName),p(o)}},[c.open,o]),(0,d.jsx)(l.sG.div,{"data-state":S(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!w,...u,ref:y,style:{"--radix-collapsible-content-height":m?"".concat(m,"px"):void 0,"--radix-collapsible-content-width":x?"".concat(x,"px"):void 0,...t.style},children:w&&i})});function S(t){return t?"open":"closed"}var P=r(94315),E="Accordion",k=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[M,_,T]=(0,i.N)(E),[C,I]=(0,o.A)(E,[T,v]),D=v(),N=n.forwardRef((t,e)=>{let{type:r,...n}=t;return(0,d.jsx)(M.Provider,{scope:t.__scopeAccordion,children:"multiple"===r?(0,d.jsx)(z,{...n,ref:e}):(0,d.jsx)(F,{...n,ref:e})})});N.displayName=E;var[R,B]=C(E),[L,U]=C(E,{collapsible:!1}),F=n.forwardRef((t,e)=>{let{value:r,defaultValue:o,onValueChange:i=()=>{},collapsible:a=!1,...u}=t,[l,s]=(0,c.i)({prop:r,defaultProp:o,onChange:i});return(0,d.jsx)(R,{scope:t.__scopeAccordion,value:l?[l]:[],onItemOpen:s,onItemClose:n.useCallback(()=>a&&s(""),[a,s]),children:(0,d.jsx)(L,{scope:t.__scopeAccordion,collapsible:a,children:(0,d.jsx)(H,{...u,ref:e})})})}),z=n.forwardRef((t,e)=>{let{value:r,defaultValue:o,onValueChange:i=()=>{},...a}=t,[u=[],l]=(0,c.i)({prop:r,defaultProp:o,onChange:i}),s=n.useCallback(t=>l(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...e,t]}),[l]),f=n.useCallback(t=>l(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e!==t)}),[l]);return(0,d.jsx)(R,{scope:t.__scopeAccordion,value:u,onItemOpen:s,onItemClose:f,children:(0,d.jsx)(L,{scope:t.__scopeAccordion,collapsible:!0,children:(0,d.jsx)(H,{...a,ref:e})})})}),[W,$]=C(E),H=n.forwardRef((t,e)=>{let{__scopeAccordion:r,disabled:o,dir:i,orientation:c="vertical",...s}=t,f=n.useRef(null),p=(0,a.s)(f,e),h=_(r),y="ltr"===(0,P.jH)(i),v=(0,u.m)(t.onKeyDown,t=>{var e;if(!k.includes(t.key))return;let r=t.target,n=h().filter(t=>{var e;return!(null===(e=t.ref.current)||void 0===e?void 0:e.disabled)}),o=n.findIndex(t=>t.ref.current===r),i=n.length;if(-1===o)return;t.preventDefault();let a=o,u=i-1,l=()=>{(a=o+1)>u&&(a=0)},s=()=>{(a=o-1)<0&&(a=u)};switch(t.key){case"Home":a=0;break;case"End":a=u;break;case"ArrowRight":"horizontal"===c&&(y?l():s());break;case"ArrowDown":"vertical"===c&&l();break;case"ArrowLeft":"horizontal"===c&&(y?s():l());break;case"ArrowUp":"vertical"===c&&s()}null===(e=n[a%i].ref.current)||void 0===e||e.focus()});return(0,d.jsx)(W,{scope:r,disabled:o,direction:i,orientation:c,children:(0,d.jsx)(M.Slot,{scope:r,children:(0,d.jsx)(l.sG.div,{...s,"data-orientation":c,ref:p,onKeyDown:o?void 0:v})})})}),q="AccordionItem",[V,X]=C(q),G=n.forwardRef((t,e)=>{let{__scopeAccordion:r,value:n,...o}=t,i=$(q,r),a=B(q,r),u=D(r),c=(0,p.B)(),l=n&&a.value.includes(n)||!1,s=i.disabled||t.disabled;return(0,d.jsx)(V,{scope:r,open:l,disabled:s,triggerId:c,children:(0,d.jsx)(b,{"data-orientation":i.orientation,"data-state":te(l),...u,...o,ref:e,disabled:s,open:l,onOpenChange:t=>{t?a.onItemOpen(n):a.onItemClose(n)}})})});G.displayName=q;var K="AccordionHeader",Y=n.forwardRef((t,e)=>{let{__scopeAccordion:r,...n}=t,o=$(E,r),i=X(K,r);return(0,d.jsx)(l.sG.h3,{"data-orientation":o.orientation,"data-state":te(i.open),"data-disabled":i.disabled?"":void 0,...n,ref:e})});Y.displayName=K;var Z="AccordionTrigger",J=n.forwardRef((t,e)=>{let{__scopeAccordion:r,...n}=t,o=$(E,r),i=X(Z,r),a=U(Z,r),u=D(r);return(0,d.jsx)(M.ItemSlot,{scope:r,children:(0,d.jsx)(w,{"aria-disabled":i.open&&!a.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId,...u,...n,ref:e})})});J.displayName=Z;var Q="AccordionContent",tt=n.forwardRef((t,e)=>{let{__scopeAccordion:r,...n}=t,o=$(E,r),i=X(Q,r),a=D(r);return(0,d.jsx)(j,{role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation,...a,...n,ref:e,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...t.style}})});function te(t){return t?"open":"closed"}tt.displayName=Q;var tr=N,tn=G,to=Y,ti=J,ta=tt},23633:(t,e,r)=>{t.exports=r(62962)("toUpperCase")},24026:(t,e,r)=>{"use strict";r.d(e,{s:()=>I});var n=r(12115),o=r(40139),i=r.n(o),a=r(52596),u=r(675),c=r(72790),l=r(9795),s=r(43597);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h=function(){return!!t})()}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function v(t,e){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function m(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var b=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=y(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,h()?Reflect.construct(t,e||[],y(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,o=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){m(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,n.cloneElement(t.legendIcon,a)}return n.createElement(l.i,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,d=e.inactiveColor,h={x:0,y:0,width:32,height:32},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var l=e.formatter||f,g=(0,a.A)(m(m({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var b=i()(e.value)?null:e.value;(0,u.R)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?d:e.color;return n.createElement("li",p({className:g,style:y,key:"legend-item-".concat(r)},(0,s.XC)(t.props,e,r)),n.createElement(c.u,{width:o,height:o,viewBox:h,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(b,e,r):b))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?o:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,g(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);m(b,"displayName","Legend"),m(b,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var x=r(16377),w=r(2494);function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var j=["ref"];function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function _(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}function C(t){return t.value}var I=function(t){var e,r;function o(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);for(var t,e,r,n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=o,r=[].concat(i),e=k(e),_(t=function(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,E()?Reflect.construct(e,r||[],k(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(o,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?S({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),S(S({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=S(S({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,j);return n.createElement(b,r)}(r,S(S({},this.props),{},{payload:(0,w.s)(c,u,C)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=S(S({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,x.Et)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&P(o.prototype,e),r&&P(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);_(I,"displayName","Legend"),_(I,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},24376:(t,e,r)=>{t.exports=r(82500).Symbol},25318:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},25641:(t,e,r)=>{"use strict";r.d(e,{IZ:()=>v,Kg:()=>y,Zk:()=>j,lY:()=>m,pr:()=>g,yy:()=>O});var n=r(59882),o=r.n(n),i=r(12115),a=r(40139),u=r.n(a),c=r(16377),l=r(49754);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){d(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e,r){var n;return(n=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=Math.PI/180,v=function(t,e,r,n){return{x:t+Math.cos(-y*n)*r,y:e+Math.sin(-y*n)*r}},m=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},g=function(t,e,r,n,i){var a=t.width,u=t.height,s=t.startAngle,f=t.endAngle,y=(0,c.F4)(t.cx,a,a/2),v=(0,c.F4)(t.cy,u,u/2),g=m(a,u,r),b=(0,c.F4)(t.innerRadius,g,0),x=(0,c.F4)(t.outerRadius,g,.8*g);return Object.keys(e).reduce(function(t,r){var a,u=e[r],c=u.domain,m=u.reversed;if(o()(u.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[b,x]),m&&(a=[a[1],a[0]]);else{var g,w=function(t){if(Array.isArray(t))return t}(g=a=u.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(g,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=w[0],f=w[1]}var O=(0,l.W7)(u,i),j=O.realScaleType,A=O.scale;A.domain(c).range(a),(0,l.YB)(A);var S=(0,l.w7)(A,p(p({},u),{},{realScaleType:j})),P=p(p(p({},u),S),{},{range:a,radius:x,realScaleType:j,scale:A,cx:y,cy:v,innerRadius:b,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},d({},r,P))},{})},b=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},x=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=b({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((r-o)/a);return n>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},w=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},O=function(t,e){var r,n=x({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,u=e.outerRadius;if(o<a||o>u)return!1;if(0===o)return!0;var c=w(e),l=c.startAngle,s=c.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?p(p({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},j=function(t){return(0,i.isValidElement)(t)||u()(t)||"boolean"==typeof t?"":t.className}},26151:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=+!!e,e}},27300:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]])},27379:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},27569:(t,e,r)=>{var n=r(32197),o=r(35095);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},28126:(t,e,r)=>{var n=r(96548),o=r(93294),i=r(79595),a=r(94356);t.exports=function(t){return i(t)?n(a(t)):o(t)}},28328:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},28749:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>o})},28897:(t,e,r)=>{var n=r(54906);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},29794:(t,e,r)=>{var n=r(48628),o=r(74888),i=r(18028),a=r(39608),u=r(39641);t.exports=function(t,e,r){var c=a(t)?n:o;return r&&u(t,e,r)&&(e=void 0),c(t,i(e,3))}},30152:(t,e,r)=>{t.exports=r(82500)["__core-js_shared__"]},30294:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case u:case a:case p:case d:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case h:case c:return t;default:return e}}case o:return e}}}(t)===i}},30462:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},30699:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},30716:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===r(i[c],c,i))break}return e}}},31431:t=>{t.exports=function(){}},31545:(t,e,r)=>{var n=r(75899);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},31598:(t,e,r)=>{var n=r(31887),o=r(90929),i=r(45170),a=r(61830),u=r(21790);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},31847:(t,e,r)=>{"use strict";r.d(e,{i:()=>c});let n=Math.PI,o=2*n,i=o-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,o,i){if(t*=1,e*=1,r*=1,o*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,u=this._y1,c=r-t,l=o-e,s=a-t,f=u-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6){if(Math.abs(f*c-l*s)>1e-6&&i){let d=r-a,h=o-u,y=c*c+l*l,v=Math.sqrt(y),m=Math.sqrt(p),g=i*Math.tan((n-Math.acos((y+p-(d*d+h*h))/(2*v*m)))/2),b=g/m,x=g/v;Math.abs(b-1)>1e-6&&this._append`L${t+b*s},${e+b*f}`,this._append`A${i},${i},0,0,${+(f*d>s*h)},${this._x1=t+x*c},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,a,u,c){if(t*=1,e*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,p=e+s,d=1^c,h=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(h<0&&(h=h%o+o),h>i?this._append`A${r},${r},0,1,${d},${t-l},${e-s}A${r},${r},0,1,${d},${this._x1=f},${this._y1=p}`:h>1e-6&&this._append`A${r},${r},0,${+(h>=n)},${d},${this._x1=t+r*Math.cos(u)},${this._y1=e+r*Math.sin(u)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new u(e)}u.prototype},31887:t=>{t.exports=function(){this.__data__=[],this.size=0}},31949:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},32197:(t,e,r)=>{var n=r(67460);t.exports=function(t){return t==t&&!n(t)}},33332:t=>{t.exports=function(t){return function(e){return t(e)}}},33497:(t,e,r)=>{t=r.nmd(t);var n=r(82500),o=r(44158),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,u=a&&a.exports===i?n.Buffer:void 0,c=u?u.isBuffer:void 0;t.exports=c||o},34210:t=>{t.exports=function(t){return this.__data__.has(t)}},34301:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},34711:(t,e,r)=>{var n=r(73800),o=r(94356);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},35095:(t,e,r)=>{var n=r(11670),o=r(18489),i=r(22471);t.exports=function(t){return i(t)?n(t):o(t)}},35190:(t,e,r)=>{var n=r(89316),o=r(33332),i=r(49840),a=i&&i.isTypedArray;t.exports=a?o(a):n},36314:(t,e,r)=>{var n=r(24376),o=r(9813),i=r(39608),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},36713:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},36730:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},36815:(t,e,r)=>{var n=r(4217),o=r(67460),i=r(70771),a=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=c.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):u.test(t)?a:+t}},37835:(t,e,r)=>{var n=r(17489),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},37929:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},38008:(t,e,r)=>{t.exports=r(83711)(r(82500),"Set")},38406:(t,e,r)=>{var n=r(85090),o=r(92313),i=r(82954);t.exports=function(t,e,r,a,u,c){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(t),d=c.get(e);if(p&&d)return p==e&&d==t;var h=-1,y=!0,v=2&r?new n:void 0;for(c.set(t,e),c.set(e,t);++h<s;){var m=t[h],g=e[h];if(a)var b=l?a(g,m,h,e,t,c):a(m,g,h,t,e,c);if(void 0!==b){if(b)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||u(m,t,r,a,c)))return v.push(e)})){y=!1;break}}else if(!(m===g||u(m,g,r,a,c))){y=!1;break}}return c.delete(t),c.delete(e),y}},38637:(t,e,r)=>{t.exports=r(79399)()},38649:(t,e,r)=>{var n=r(38675),o=r(43720),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},38675:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},38985:(t,e,r)=>{var n=r(30152),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},39608:t=>{t.exports=Array.isArray},39641:(t,e,r)=>{var n=r(58817),o=r(22471),i=r(99544),a=r(67460);t.exports=function(t,e,r){if(!a(r))return!1;var u=typeof e;return("number"==u?!!(o(r)&&i(e,r.length)):"string"==u&&e in r)&&n(r[e],t)}},39984:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},40139:(t,e,r)=>{var n=r(98233),o=r(67460);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},40382:(t,e,r)=>{t.exports=r(83711)(r(82500),"WeakMap")},40566:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},41643:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},42233:(t,e,r)=>{var n=r(36815),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},42625:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},43369:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("ListTree",[["path",{d:"M21 12h-8",key:"1bmf0i"}],["path",{d:"M21 6H8",key:"1pqkrb"}],["path",{d:"M21 18h-8",key:"1tm79t"}],["path",{d:"M3 6v4c0 1.1.9 2 2 2h3",key:"1ywdgy"}],["path",{d:"M3 10v6c0 1.1.9 2 2 2h3",key:"2wc746"}]])},43597:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>u,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(12115),o=r(67460),i=r.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n}},43720:t=>{t.exports=function(){return[]}},44101:(t,e,r)=>{t.exports=r(83711)(r(82500),"DataView")},44158:t=>{t.exports=function(){return!1}},44482:(t,e,r)=>{var n=r(98233),o=r(73726),i=r(48611),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,l=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=c.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==l}},44538:(t,e,r)=>{"use strict";r.d(e,{J:()=>d,M:()=>y});var n=r(12115),o=r(52596),i=r(9557),a=r(70788);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),i+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),i+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},d=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,u=e.height;if(Math.abs(a)>0&&Math.abs(u)>0){var c=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+u),f=Math.max(i,i+u);return r>=c&&r<=l&&n>=s&&n<=f}return!1},h={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,r=f(f({},h),t),u=(0,n.useRef)(),s=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return l(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),d=s[0],y=s[1];(0,n.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var t=u.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=r.x,m=r.y,g=r.width,b=r.height,x=r.radius,w=r.className,O=r.animationEasing,j=r.animationDuration,A=r.animationBegin,S=r.isAnimationActive,P=r.isUpdateAnimationActive;if(v!==+v||m!==+m||g!==+g||b!==+b||0===g||0===b)return null;var E=(0,o.A)("recharts-rectangle",w);return P?n.createElement(i.Ay,{canBegin:d>0,from:{width:g,height:b,x:v,y:m},to:{width:g,height:b,x:v,y:m},duration:j,animationEasing:O,isActive:P},function(t){var e=t.width,o=t.height,l=t.x,s=t.y;return n.createElement(i.Ay,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:A,duration:j,isActive:S,easing:O},n.createElement("path",c({},(0,a.J9)(r,!0),{className:E,d:p(l,s,e,o,x),ref:u})))}):n.createElement("path",c({},(0,a.J9)(r,!0),{className:E,d:p(v,m,g,b,x)}))}},45170:(t,e,r)=>{var n=r(54360);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},45964:(t,e,r)=>{var n=r(67460),o=r(76685),i=r(36815),a=Math.max,u=Math.min;t.exports=function(t,e,r){var c,l,s,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function g(e){var r=c,n=l;return c=l=void 0,h=e,f=t.apply(n,r)}function b(t){var r=t-d,n=t-h;return void 0===d||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(b(i))return w(i);p=setTimeout(x,(t=i-d,r=i-h,n=e-t,v?u(n,s-r):n))}function w(t){return(p=void 0,m&&c)?g(t):(c=l=void 0,f)}function O(){var t,r=o(),n=b(r);if(c=arguments,l=this,d=r,n){if(void 0===p)return h=t=d,p=setTimeout(x,e),y?g(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),g(d)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,c=d=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},46287:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("LayoutList",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]])},46605:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(41643);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var c={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",f=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var o=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:o});if(c.widthCache[i])return c.widthCache[i];try{var u=document.getElementById(s);u||((u=document.createElement("span")).setAttribute("id",s),u.setAttribute("aria-hidden","true"),document.body.appendChild(u));var f=a(a({},l),o);Object.assign(u.style,f),u.textContent="".concat(t);var p=u.getBoundingClientRect(),d={width:p.width,height:p.height};return c.widthCache[i]=d,++c.cacheCount>2e3&&(c.cacheCount=0,c.widthCache={}),d}catch(t){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},47995:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},48611:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},48628:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},48659:(t,e,r)=>{var n=r(37929);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},48973:(t,e,r)=>{var n=r(34711);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},49754:(t,e,r)=>{"use strict";r.d(e,{s0:()=>oa,gH:()=>or,YB:()=>oy,HQ:()=>od,Hj:()=>oS,BX:()=>oi,tA:()=>oo,Ay:()=>oe,vf:()=>ol,Mk:()=>ow,Ps:()=>on,Mn:()=>og,kA:()=>ox,Rh:()=>of,w7:()=>ob,zb:()=>oE,kr:()=>ot,_L:()=>os,KC:()=>oP,A1:()=>oc,W7:()=>oh,AQ:()=>oA});var n,o,i,a,u,c,l,s={};r.r(s),r.d(s,{scaleBand:()=>f.A,scaleDiverging:()=>function t(){var e=tN(rJ()(tv));return e.copy=function(){return rK(e,t())},tj.K.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tH(rJ()).domain([.1,1,10]);return e.copy=function(){return rK(e,t()).base(e.base())},tj.K.apply(e,arguments)},scaleDivergingPow:()=>rQ,scaleDivergingSqrt:()=>r0,scaleDivergingSymlog:()=>function t(){var e=tX(rJ());return e.copy=function(){return rK(e,t()).constant(e.constant())},tj.K.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,th),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,th):[0,1],tN(n)},scaleImplicit:()=>tG.h,scaleLinear:()=>tR,scaleLog:()=>function t(){let e=tH(tw()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tj.C.apply(e,arguments),e},scaleOrdinal:()=>tG.A,scalePoint:()=>f.z,scalePow:()=>tQ,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=j){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[S(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(b),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tj.C.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[S(i,t,0,o)]:e}function c(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,c()):[r,n]},u.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return i.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tj.C.apply(tN(u),arguments)},scaleRadial:()=>function t(){var e,r=tO(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(t1(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,th)).map(t1)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},tj.C.apply(i,arguments),tN(i)},scaleSequential:()=>function t(){var e=tN(rG()(tv));return e.copy=function(){return rK(e,t())},tj.K.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tH(rG()).domain([1,10]);return e.copy=function(){return rK(e,t()).base(e.base())},tj.K.apply(e,arguments)},scaleSequentialPow:()=>rY,scaleSequentialQuantile:()=>function t(){var e=[],r=tv;function n(t){if(null!=t&&!isNaN(t*=1))return r((S(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(b),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return t5(t);if(e>=1)return t2(t);var n,o=(n-1)*e,i=Math.floor(o),a=t2((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?t3:function(t=b){if(t===b)return t3;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),p=Math.min(o,Math.floor(r+(a-u)*l/a+s));t(e,r,f,p,i)}let a=e[r],u=n,c=o;for(t6(e,n,r),i(e[o],a)>0&&t6(e,n,o);u<c;){for(t6(e,u,c),++u,--c;0>i(e[u],a);)++u;for(;i(e[c],a)>0;)--c}0===i(e[n],a)?t6(e,n,c):t6(e,++c,o),c<=r&&(n=c+1),r<=c&&(o=c-1)}return e})(t,i).subarray(0,i+1));return a+(t5(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tj.K.apply(n,arguments)},scaleSequentialSqrt:()=>rZ,scaleSequentialSymlog:()=>function t(){var e=tX(rG());return e.copy=function(){return rK(e,t()).constant(e.constant())},tj.K.apply(e,arguments)},scaleSqrt:()=>t0,scaleSymlog:()=>function t(){var e=tX(tw());return e.copy=function(){return tx(e,t()).constant(e.constant())},tj.C.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[S(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},tj.C.apply(i,arguments)},scaleTime:()=>rV,scaleUtc:()=>rX,tickFormat:()=>tD});var f=r(81519);let p=Math.sqrt(50),d=Math.sqrt(10),h=Math.sqrt(2);function y(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=p?10:c>=d?5:c>=h?2:1;return(u<0?(n=Math.round(t*(i=Math.pow(10,-u)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,u)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?y(t,e,2*r):[n,o,i]}function v(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?y(e,t,r):y(t,e,r);if(!(i>=o))return[];let u=i-o+1,c=Array(u);if(n){if(a<0)for(let t=0;t<u;++t)c[t]=-((i-t)/a);else for(let t=0;t<u;++t)c[t]=(i-t)*a}else if(a<0)for(let t=0;t<u;++t)c[t]=-((o+t)/a);else for(let t=0;t<u;++t)c[t]=(o+t)*a;return c}function m(t,e,r){return y(t*=1,e*=1,r*=1)[2]}function g(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?m(e,t,r):m(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function b(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function w(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=b,r=(e,r)=>b(t(e),r),n=(e,r)=>t(e)-r):(e=t===b||t===x?t:O,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function O(){return 0}function j(t){return null===t?NaN:+t}let A=w(b),S=A.right;function P(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function E(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function k(){}A.left,w(j).center;var M="\\s*([+-]?\\d+)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,I=RegExp(`^rgb\\(${M},${M},${M}\\)$`),D=RegExp(`^rgb\\(${T},${T},${T}\\)$`),N=RegExp(`^rgba\\(${M},${M},${M},${_}\\)$`),R=RegExp(`^rgba\\(${T},${T},${T},${_}\\)$`),B=RegExp(`^hsl\\(${_},${T},${T}\\)$`),L=RegExp(`^hsla\\(${_},${T},${T},${_}\\)$`),U={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function F(){return this.rgb().formatHex()}function z(){return this.rgb().formatRgb()}function W(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=C.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?$(e):3===r?new V(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?H(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?H(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=I.exec(t))?new V(e[1],e[2],e[3],1):(e=D.exec(t))?new V(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?H(e[1],e[2],e[3],e[4]):(e=R.exec(t))?H(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=B.exec(t))?J(e[1],e[2]/100,e[3]/100,1):(e=L.exec(t))?J(e[1],e[2]/100,e[3]/100,e[4]):U.hasOwnProperty(t)?$(U[t]):"transparent"===t?new V(NaN,NaN,NaN,0):null}function $(t){return new V(t>>16&255,t>>8&255,255&t,1)}function H(t,e,r,n){return n<=0&&(t=e=r=NaN),new V(t,e,r,n)}function q(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof k||(o=W(o)),o)?new V((o=o.rgb()).r,o.g,o.b,o.opacity):new V:new V(t,e,r,null==n?1:n)}function V(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function X(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}`}function G(){let t=K(this.opacity);return`${1===t?"rgb(":"rgba("}${Y(this.r)}, ${Y(this.g)}, ${Y(this.b)}${1===t?")":`, ${t})`}`}function K(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Y(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Z(t){return((t=Y(t))<16?"0":"")+t.toString(16)}function J(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,r,n)}function Q(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof k||(t=W(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(r-n)/u+(r<n)*6:r===i?(n-e)/u+2:(e-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new tt(a,u,c,t.opacity)}function tt(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function te(t){return(t=(t||0)%360)<0?t+360:t}function tr(t){return Math.max(0,Math.min(1,t||0))}function tn(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function to(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}P(k,W,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:F,formatHex:F,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:z,toString:z}),P(V,q,E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new V(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new V(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new V(Y(this.r),Y(this.g),Y(this.b),K(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:X,formatHex:X,formatHex8:function(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}${Z((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:G,toString:G})),P(tt,function(t,e,r,n){return 1==arguments.length?Q(t):new tt(t,e,r,null==n?1:n)},E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new V(tn(t>=240?t-240:t+120,o,n),tn(t,o,n),tn(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new tt(te(this.h),tr(this.s),tr(this.l),K(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=K(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tr(this.s)}%, ${100*tr(this.l)}%${1===t?")":`, ${t})`}`}}));let ti=t=>()=>t;function ta(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):ti(isNaN(t)?e:t)}let tu=function t(e){var r,n=1==(r=+e)?ta:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):ti(isNaN(t)?e:t)};function o(t,e){var r=n((t=q(t)).r,(e=q(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function tc(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),u=Array(o);for(r=0;r<o;++r)n=q(e[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=t(i),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=u(t),n+""}}}tc(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,u=n<e-1?t[n+2]:2*i-o;return to((r-n/e)*e,a,o,i,u)}}),tc(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return to((r-n/e)*e,o,i,a,u)}});function tl(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var ts=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(ts.source,"g");function tp(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?ti(e):("number"===o?tl:"string"===o?(n=W(e))?(e=n,tu):function(t,e){var r,n,o,i,a,u=ts.lastIndex=tf.lastIndex=0,c=-1,l=[],s=[];for(t+="",e+="";(o=ts.exec(t))&&(i=tf.exec(e));)(a=i.index)>u&&(a=e.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(o=o[0])===(i=i[0])?l[c]?l[c]+=i:l[++c]=i:(l[++c]=null,s.push({i:c,x:tl(o,i)})),u=tf.lastIndex;return u<e.length&&(a=e.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof W?tu:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=tp(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tp(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:tl:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function td(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function th(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tg(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=tm(o,n),i=r(a,i)):(n=tm(n,o),i=r(i,a)),function(t){return i(n(t))}}function tb(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=tm(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=S(t,e,1,n)-1;return i[r](o[r](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tw(){var t,e,r,n,o,i,a=ty,u=ty,c=tp,l=tv;function s(){var t,e,r,c=Math.min(a.length,u.length);return l!==tv&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tb:tg,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(u,a.map(t),tl)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,th),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=td,s()},f.clamp=function(t){return arguments.length?(l=!!t||tv,s()):l!==tv},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tO(){return tw()(tv,tv)}var tj=r(28749),tA=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tS(t){var e;if(!(e=tA.exec(t)))throw Error("invalid format: "+t);return new tP({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tP(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tk(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tM(t,e){var r=tE(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}tS.prototype=tP.prototype,tP.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let t_={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tM(100*t,e),r:tM,s:function(t,e){var r=tE(t,e);if(!r)return t+"";var o=r[0],i=r[1],a=i-(n=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=o.length;return a===u?o:a>u?o+Array(a-u+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tT(t){return t}var tC=Array.prototype.map,tI=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tD(t,e,r,n){var o,u,c,l=g(t,e,r);switch((n=tS(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null==n.precision&&!isNaN(c=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tk(s)/3)))-tk(Math.abs(l))))&&(n.precision=c),a(n,s);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(c=Math.max(0,tk(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=l)))-tk(o))+1)&&(n.precision=c-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(c=Math.max(0,-tk(Math.abs(l))))&&(n.precision=c-("%"===n.type)*2)}return i(n)}function tN(t){var e=t.domain;return t.ticks=function(t){var r=e();return v(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tD(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=m(c,l,r))===n)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else if(o<0)c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function tR(){var t=tO();return t.copy=function(){return tx(t,tR())},tj.C.apply(t,arguments),tN(t)}function tB(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function tL(t){return Math.log(t)}function tU(t){return Math.exp(t)}function tF(t){return-Math.log(-t)}function tz(t){return-Math.exp(-t)}function tW(t){return isFinite(t)?+("1e"+t):t<0?0:t}function t$(t){return(e,r)=>-t(-e,r)}function tH(t){let e,r;let n=t(tL,tU),o=n.domain,a=10;function u(){var i,u;return e=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),t=>Math.log(t)/i),r=10===(u=a)?tW:u===Math.E?Math.exp:t=>Math.pow(u,t),o()[0]<0?(e=t$(e),r=t$(r),t(tF,tz)):t(tL,tU),n}return n.base=function(t){return arguments.length?(a=+t,u()):a},n.domain=function(t){return arguments.length?(o(t),u()):o()},n.ticks=t=>{let n,i;let u=o(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=e(c),p=e(l),d=null==t?10:+t,h=[];if(!(a%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((i=f<0?n/r(-f):n*r(f))<c)){if(i>l)break;h.push(i)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((i=f>0?n/r(-f):n*r(f))<c)){if(i>l)break;h.push(i)}2*h.length<d&&(h=v(c,l,d))}else h=v(f,p,Math.min(p-f,d)).map(r);return s?h.reverse():h},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=tS(o)).precision||(o.trim=!0),o=i(o)),t===1/0)return o;let u=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=u?o(t):""}},n.nice=()=>o(tB(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tq(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tV(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tX(t){var e=1,r=t(tq(1),tV(e));return r.constant=function(r){return arguments.length?t(tq(e=+r),tV(e)):e},tN(r)}i=(o=function(t){var e,r,o,i=void 0===t.grouping||void 0===t.thousands?tT:(e=tC.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return i.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?tT:(o=tC.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return o[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function d(t){var e=(t=tS(t)).fill,r=t.align,o=t.sign,d=t.symbol,h=t.zero,y=t.width,v=t.comma,m=t.precision,g=t.trim,b=t.type;"n"===b?(v=!0,b="g"):t_[b]||(void 0===m&&(m=12),g=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var x="$"===d?a:"#"===d&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===d?u:/[%p]/.test(b)?s:"",O=t_[b],j=/[defgprs%]/.test(b);function A(t){var a,u,s,d=x,A=w;if("c"===b)A=O(t)+A,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?p:O(Math.abs(t),m),g&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==o&&(S=!1),d=(S?"("===o?o:f:"-"===o||"("===o?"":o)+d,A=("s"===b?tI[8+n/3]:"")+A+(S&&"("===o?")":""),j){for(a=-1,u=t.length;++a<u;)if(48>(s=t.charCodeAt(a))||s>57){A=(46===s?c+t.slice(a+1):t.slice(a))+A,t=t.slice(0,a);break}}}v&&!h&&(t=i(t,1/0));var P=d.length+t.length+A.length,E=P<y?Array(y-P+1).join(e):"";switch(v&&h&&(t=i(E+t,E.length?y-A.length:1/0),E=""),r){case"<":t=d+t+A+E;break;case"=":t=d+E+t+A;break;case"^":t=E.slice(0,P=E.length>>1)+d+t+A+E.slice(P);break;default:t=E+d+t+A}return l(t)}return m=void 0===m?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),A.toString=function(){return t+""},A}return{format:d,formatPrefix:function(t,e){var r=d(((t=tS(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tk(e)/3))),o=Math.pow(10,-n),i=tI[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;var tG=r(95442);function tK(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tY(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tZ(t){return t<0?-t*t:t*t}function tJ(t){var e=t(tv,tv),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tv,tv):.5===r?t(tY,tZ):t(tK(r),tK(1/r)):r},tN(e)}function tQ(){var t=tJ(tw());return t.copy=function(){return tx(t,tQ()).exponent(t.exponent())},tj.C.apply(t,arguments),t}function t0(){return tQ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function t5(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function t3(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function t6(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t8=new Date,t4=new Date;function t9(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let u=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return u;do u.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return u},o.filter=r=>t9(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(t8.setTime(+e),t4.setTime(+n),t(t8),t(t4),Math.floor(r(t8,t4))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let t7=t9(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t7.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t9(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t7:null,t7.range;let et=t9(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());et.range;let ee=t9(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ee.range;let er=t9(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());er.range;let en=t9(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());en.range;let eo=t9(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());eo.range;let ei=t9(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);ei.range;let ea=t9(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ea.range;let eu=t9(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ec(t){return t9(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}eu.range;let el=ec(0),es=ec(1),ef=ec(2),ep=ec(3),ed=ec(4),eh=ec(5),ey=ec(6);function ev(t){return t9(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}el.range,es.range,ef.range,ep.range,ed.range,eh.range,ey.range;let em=ev(0),eg=ev(1),eb=ev(2),ex=ev(3),ew=ev(4),eO=ev(5),ej=ev(6);em.range,eg.range,eb.range,ex.range,ew.range,eO.range,ej.range;let eA=t9(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());eA.range;let eS=t9(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eS.range;let eP=t9(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eP.every=t=>isFinite(t=Math.floor(t))&&t>0?t9(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eP.range;let eE=t9(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ek(t,e,r,n,o,i){let a=[[et,1,1e3],[et,5,5e3],[et,15,15e3],[et,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function u(e,r,n){let o=Math.abs(r-e)/n,i=w(([,,t])=>t).right(a,o);if(i===a.length)return t.every(g(e/31536e6,r/31536e6,n));if(0===i)return t7.every(Math.max(g(e,r,n),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:u(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},u]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t9(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eE.range;let[eM,e_]=ek(eE,eS,em,eu,eo,er),[eT,eC]=ek(eP,eA,el,ei,en,ee);function eI(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eD(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eN(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eR={"-":"",_:" ",0:"0"},eB=/^\s*\d+/,eL=/^%/,eU=/[\\^$*+?|[\]().{}]/g;function eF(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function ez(t){return t.replace(eU,"\\$&")}function eW(t){return RegExp("^(?:"+t.map(ez).join("|")+")","i")}function e$(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eH(t,e,r){var n=eB.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eq(t,e,r){var n=eB.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eX(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eB.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eZ(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eJ(t,e,r){var n=eB.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eQ(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e0(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eB.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=eB.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eB.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function e8(t,e,r){var n=eB.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e4(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e9(t,e,r){var n=eB.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=eB.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function rt(t,e){return eF(t.getDate(),e,2)}function re(t,e){return eF(t.getHours(),e,2)}function rr(t,e){return eF(t.getHours()%12||12,e,2)}function rn(t,e){return eF(1+ei.count(eP(t),t),e,3)}function ro(t,e){return eF(t.getMilliseconds(),e,3)}function ri(t,e){return ro(t,e)+"000"}function ra(t,e){return eF(t.getMonth()+1,e,2)}function ru(t,e){return eF(t.getMinutes(),e,2)}function rc(t,e){return eF(t.getSeconds(),e,2)}function rl(t){var e=t.getDay();return 0===e?7:e}function rs(t,e){return eF(el.count(eP(t)-1,t),e,2)}function rf(t){var e=t.getDay();return e>=4||0===e?ed(t):ed.ceil(t)}function rp(t,e){return t=rf(t),eF(ed.count(eP(t),t)+(4===eP(t).getDay()),e,2)}function rd(t){return t.getDay()}function rh(t,e){return eF(es.count(eP(t)-1,t),e,2)}function ry(t,e){return eF(t.getFullYear()%100,e,2)}function rv(t,e){return eF((t=rf(t)).getFullYear()%100,e,2)}function rm(t,e){return eF(t.getFullYear()%1e4,e,4)}function rg(t,e){var r=t.getDay();return eF((t=r>=4||0===r?ed(t):ed.ceil(t)).getFullYear()%1e4,e,4)}function rb(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eF(e/60|0,"0",2)+eF(e%60,"0",2)}function rx(t,e){return eF(t.getUTCDate(),e,2)}function rw(t,e){return eF(t.getUTCHours(),e,2)}function rO(t,e){return eF(t.getUTCHours()%12||12,e,2)}function rj(t,e){return eF(1+ea.count(eE(t),t),e,3)}function rA(t,e){return eF(t.getUTCMilliseconds(),e,3)}function rS(t,e){return rA(t,e)+"000"}function rP(t,e){return eF(t.getUTCMonth()+1,e,2)}function rE(t,e){return eF(t.getUTCMinutes(),e,2)}function rk(t,e){return eF(t.getUTCSeconds(),e,2)}function rM(t){var e=t.getUTCDay();return 0===e?7:e}function r_(t,e){return eF(em.count(eE(t)-1,t),e,2)}function rT(t){var e=t.getUTCDay();return e>=4||0===e?ew(t):ew.ceil(t)}function rC(t,e){return t=rT(t),eF(ew.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function rI(t){return t.getUTCDay()}function rD(t,e){return eF(eg.count(eE(t)-1,t),e,2)}function rN(t,e){return eF(t.getUTCFullYear()%100,e,2)}function rR(t,e){return eF((t=rT(t)).getUTCFullYear()%100,e,2)}function rB(t,e){return eF(t.getUTCFullYear()%1e4,e,4)}function rL(t,e){var r=t.getUTCDay();return eF((t=r>=4||0===r?ew(t):ew.ceil(t)).getUTCFullYear()%1e4,e,4)}function rU(){return"+0000"}function rF(){return"%"}function rz(t){return+t}function rW(t){return Math.floor(+t/1e3)}function r$(t){return new Date(t)}function rH(t){return t instanceof Date?+t:+new Date(+t)}function rq(t,e,r,n,o,i,a,u,c,l){var s=tO(),f=s.invert,p=s.domain,d=l(".%L"),h=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),g=l("%b %d"),b=l("%B"),x=l("%Y");function w(t){return(c(t)<t?d:u(t)<t?h:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:g:r(t)<t?b:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rH)):p().map(r$)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tB(r,t)):s},s.copy=function(){return tx(s,rq(t,e,r,n,o,i,a,u,c,l))},s}function rV(){return tj.C.apply(rq(eT,eC,eP,eA,el,ei,en,ee,et,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rX(){return tj.C.apply(rq(eM,e_,eE,eS,em,ea,eo,er,et,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rG(){var t,e,r,n,o,i=0,a=1,u=tv,c=!1;function l(e){return null==e||isNaN(e*=1)?o:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(tp),l.rangeRound=s(td),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function rK(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rY(){var t=tJ(rG());return t.copy=function(){return rK(t,rY()).exponent(t.exponent())},tj.K.apply(t,arguments)}function rZ(){return rY.apply(null,arguments).exponent(.5)}function rJ(){var t,e,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=tv,p=!1;function d(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function h(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u*=1),e=i(c*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,d):[u,c,l]},d.clamp=function(t){return arguments.length?(p=!!t,d):p},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=h(tp),d.rangeRound=h(td),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return i=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,d}}function rQ(){var t=tJ(rJ());return t.copy=function(){return rK(t,rQ()).exponent(t.exponent())},tj.K.apply(t,arguments)}function r0(){return rQ.apply(null,arguments).exponent(.5)}function r1(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}c=(u=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=eW(o),s=e$(o),f=eW(i),p=e$(i),d=eW(a),h=e$(a),y=eW(u),v=e$(u),m=eW(c),g=e$(c),b={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:rt,e:rt,f:ri,g:rv,G:rg,H:re,I:rr,j:rn,L:ro,m:ra,M:ru,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rz,s:rW,S:rc,u:rl,U:rs,V:rp,w:rd,W:rh,x:null,X:null,y:ry,Y:rm,Z:rb,"%":rF},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:rx,e:rx,f:rS,g:rR,G:rL,H:rw,I:rO,j:rj,L:rA,m:rP,M:rE,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rz,s:rW,S:rk,u:rM,U:r_,V:rC,w:rI,W:rD,x:null,X:null,y:rN,Y:rB,Z:rU,"%":rF},w={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return A(t,e,r,n)},d:e0,e:e0,f:e8,g:eY,G:eK,H:e2,I:e2,j:e1,L:e6,m:eQ,M:e5,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eJ,Q:e9,s:e7,S:e3,u:eq,U:eV,V:eX,w:eH,W:eG,x:function(t,e,n){return A(t,r,e,n)},X:function(t,e,r){return A(t,n,e,r)},y:eY,Y:eK,Z:eZ,"%":e4};function O(t,e){return function(r){var n,o,i,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=eR[n=t.charAt(++u)])?n=t.charAt(++u):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(r){var n,o,i=eN(1900,void 0,1);if(A(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=eD(eN(i.y,0,1))).getUTCDay())>4||0===o?eg.ceil(n):eg(n),n=ea.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=eI(eN(i.y,0,1))).getDay())>4||0===o?es.ceil(n):es(n),n=ei.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?eD(eN(i.y,0,1)).getUTCDay():eI(eN(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,eD(i)):eI(i)}}function A(t,e,r,n){for(var o,i,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=w[(o=e.charAt(a++))in eR?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(e,b),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var r2=r(9819),r5=r(85654);function r3(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function r6(t,e){return t[e]}function r8(t){let e=[];return e.key=t,e}var r4=r(22315),r9=r.n(r4),r7=r(89053),nt=r.n(r7),ne=r(59882),nr=r.n(ne),nn=r(40139),no=r.n(nn),ni=r(15438),na=r.n(ni),nu=r(48973),nc=r.n(nu),nl=r(3698),ns=r.n(nl),nf=r(13908),np=r.n(nf),nd=r(23633),nh=r.n(nd),ny=r(60245),nv=r.n(ny),nm=r(67206),ng=r.n(nm),nb=r(8870),nx=r.n(nb);function nw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nO=function(t){return t},nj={},nA=function(t){return t===nj},nS=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nA(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nP=function(t){return function t(e,r){return 1===e?r:nS(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==nj}).length;return a>=e?r.apply(void 0,o):t(e-a,nS(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return nA(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nw(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return nw(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nw(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nE=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nk=nP(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),nM=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nO;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},n_=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},nT=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};nP(function(t,e,r){var n=+t;return n+r*(+e-n)}),nP(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),nP(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let nC={rangeStep:function(t,e,r){for(var n=new(nx())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new(nx())(t).abs().log(10).toNumber())+1}};function nI(t){return function(t){if(Array.isArray(t))return nR(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nD(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(t,e)||nN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){if(t){if("string"==typeof t)return nR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nR(t,e)}}function nR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nB(t){var e=nD(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function nL(t,e,r){if(t.lte(0))return new(nx())(0);var n=nC.getDigitCount(t.toNumber()),o=new(nx())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,u=new(nx())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?u:new(nx())(Math.ceil(u))}function nU(t,e,r){var n=1,o=new(nx())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(nx())(10).pow(nC.getDigitCount(t)-1),o=new(nx())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(nx())(Math.floor(t)))}else 0===t?o=new(nx())(Math.floor((e-1)/2)):r||(o=new(nx())(Math.floor(t)));var a=Math.floor((e-1)/2);return nM(nk(function(t){return o.add(new(nx())(t-a).mul(n)).toNumber()}),nE)(0,e)}var nF=nT(function(t){var e=nD(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nD(nB([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(nI(nE(0,o-1).map(function(){return 1/0}))):[].concat(nI(nE(0,o-1).map(function(){return-1/0})),[l]);return r>n?n_(s):s}if(c===l)return nU(c,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new(nx())(0),tickMin:new(nx())(0),tickMax:new(nx())(0)};var u=nL(new(nx())(r).sub(e).div(n-1),o,a),c=Math.ceil((i=e<=0&&r>=0?new(nx())(0):(i=new(nx())(e).add(r).div(2)).sub(new(nx())(i).mod(u))).sub(e).div(u).toNumber()),l=Math.ceil(new(nx())(r).sub(i).div(u).toNumber()),s=c+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,c=r>0?c:c+(n-s)),{step:u,tickMin:i.sub(new(nx())(c).mul(u)),tickMax:i.add(new(nx())(l).mul(u))})}(c,l,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=nC.rangeStep(d,h.add(new(nx())(.1).mul(p)),p);return r>n?n_(y):y});nT(function(t){var e=nD(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nD(nB([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return nU(c,o,i);var s=nL(new(nx())(l).sub(c).div(a-1),i,0),f=nM(nk(function(t){return new(nx())(c).add(new(nx())(t).mul(s)).toNumber()}),nE)(0,a).filter(function(t){return t>=c&&t<=l});return r>n?n_(f):f});var nz=nT(function(t,e){var r=nD(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nD(nB([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=Math.max(e,2),s=nL(new(nx())(c).sub(u).div(l-1),i,0),f=[].concat(nI(nC.rangeStep(new(nx())(u),new(nx())(c).sub(new(nx())(.99).mul(s)),s)),[c]);return n>o?n_(f):f}),nW=r(12115),n$=r(93179),nH=r(2348),nq=r(70788),nV=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function nX(t){return(nX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nG(){return(nG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nY(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(nY=function(){return!!t})()}function nZ(t){return(nZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function nJ(t,e){return(nJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nQ(t,e,r){return(e=n0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function n0(t){var e=function(t,e){if("object"!=nX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nX(e)?e:e+""}var n1=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=nZ(t),function(t,e){if(e&&("object"===nX(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,nY()?Reflect.construct(t,e||[],nZ(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nJ(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,a=t.dataPointFormatter,u=t.xAxis,c=t.yAxis,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,nV),s=(0,nq.J9)(l,!1);"x"===this.props.direction&&"number"!==u.type&&(0,n$.A)(!1);var f=i.map(function(t){var i,l,f=a(t,o),p=f.x,d=f.y,h=f.value,y=f.errorVal;if(!y)return null;var v=[];if(Array.isArray(y)){var m=function(t){if(Array.isArray(t))return t}(y)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(y,2)||function(t,e){if(t){if("string"==typeof t)return nK(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nK(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=m[0],l=m[1]}else i=l=y;if("vertical"===r){var g=u.scale,b=d+e,x=b+n,w=b-n,O=g(h-i),j=g(h+l);v.push({x1:j,y1:x,x2:j,y2:w}),v.push({x1:O,y1:b,x2:j,y2:b}),v.push({x1:O,y1:x,x2:O,y2:w})}else if("horizontal"===r){var A=c.scale,S=p+e,P=S-n,E=S+n,k=A(h-i),M=A(h+l);v.push({x1:P,y1:M,x2:E,y2:M}),v.push({x1:S,y1:k,x2:S,y2:M}),v.push({x1:P,y1:k,x2:E,y2:k})}return nW.createElement(nH.W,nG({className:"recharts-errorBar",key:"bar-".concat(v.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),v.map(function(t){return nW.createElement("line",nG({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return nW.createElement(nH.W,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n0(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(nW.Component);nQ(n1,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),nQ(n1,"displayName","ErrorBar");var n2=r(16377),n5=r(83197);function n3(t){return(n3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n6(t){return function(t){if(Array.isArray(t))return n8(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return n8(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n8(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function n9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n4(Object(r),!0).forEach(function(e){n7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function n7(t,e,r){var n;return(n=function(t,e){if("object"!=n3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=n3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==n3(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ot(t,e,r){return nr()(t)||nr()(e)?r:(0,n2.vh)(e)?nc()(t,e,r):no()(e)?e(t):r}function oe(t,e,r,n){var o=ns()(t,function(t){return ot(t,e)});if("number"===r){var i=o.filter(function(t){return(0,n2.Et)(t)||parseFloat(t)});return i.length?[nt()(i),r9()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!nr()(t)}):o).map(function(t){return(0,n2.vh)(t)||t instanceof Date?t:""})}var or=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,n2.sA)(s-l)!==(0,n2.sA)(f-s)){var d=[];if((0,n2.sA)(f-s)===(0,n2.sA)(u[1]-u[0])){p=f;var h=s+u[1]-u[0];d[0]=Math.min(h,(h+l)/2),d[1]=Math.max(h,(h+l)/2)}else{p=l;var y=f+u[1]-u[0];d[0]=Math.min(s,(y+s)/2),d[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){i=n[c].index;break}}else{var m=Math.min(l,f),g=Math.max(l,f);if(t>(m+s)/2&&t<=(g+s)/2){i=n[c].index;break}}}else for(var b=0;b<a;b++)if(0===b&&t<=(r[b].coordinate+r[b+1].coordinate)/2||b>0&&b<a-1&&t>(r[b].coordinate+r[b-1].coordinate)/2&&t<=(r[b].coordinate+r[b+1].coordinate)/2||b===a-1&&t>(r[b].coordinate+r[b-1].coordinate)/2){i=r[b].index;break}return i},on=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?n9(n9({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},oo=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var d=l[s[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(t){return(0,nq.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,g=void 0!==m?n9(n9({},m),v[0].props):v[0].props,b=g.barSize,x=g[y];i[x]||(i[x]=[]);var w=nr()(b)?e:b;i[x].push({item:v[0],stackList:v.slice(1),barSize:nr()(w)?void 0:(0,n2.F4)(w,r,0)})}}return i},oi=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,u=t.maxBarSize,c=a.length;if(c<1)return null;var l=(0,n2.F4)(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,d=a.reduce(function(t,e){return t+e.barSize||0},0);(d+=(c-1)*l)>=o&&(d-=(c-1)*l,l=0),d>=o&&p>0&&(f=!0,p*=.9,d=c*p);var h={offset:((o-d)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:h.offset+h.size+l,size:f?p:e.barSize}},n=[].concat(n6(t),[r]);return h=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:h})}),n},s)}else{var y=(0,n2.F4)(n,o,0,!0);o-2*y-(c-1)*l<=0&&(l=0);var v=(o-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;e=a.reduce(function(t,e,r){var n=[].concat(n6(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},oa=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=(0,n5.g)({children:o,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,d=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===d)&&"center"!==p&&(0,n2.Et)(t[p]))return n9(n9({},t),{},n7({},p,t[p]+(s||0)));if(("horizontal"===h||"vertical"===h&&"center"===p)&&"middle"!==d&&(0,n2.Et)(t[d]))return n9(n9({},t),{},n7({},d,t[d]+(f||0)))}return t},ou=function(t,e,r,n,o){var i=e.props.children,a=(0,nq.aS)(i,n1).filter(function(t){var e;return e=t.props.direction,!!nr()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=ot(e,r);if(nr()(n))return t;var o=Array.isArray(n)?[nt()(n),r9()(n)]:[n,n],i=u.reduce(function(t,r){var n=ot(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},oc=function(t,e,r,n,o){var i=e.map(function(e){return ou(t,e,r,o,n)}).filter(function(t){return!nr()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},ol=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&ou(t,e,i,n)||oe(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},os=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},of=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,n2.sA)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+c,value:t,offset:c}}).filter(function(t){return!np()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+c,value:t,offset:c}}):n.domain().map(function(t,e){return{coordinate:n(t)+c,value:o?o[t]:t,index:e,offset:c}})},op=new WeakMap,od=function(t,e){if("function"!=typeof e)return t;op.has(t)||op.set(t,new WeakMap);var r=op.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},oh=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:f.A(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:tR(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:f.z(),realScaleType:"point"}:"category"===o?{scale:f.A(),realScaleType:"band"}:{scale:tR(),realScaleType:"linear"};if(na()(n)){var u="scale".concat(nh()(n));return{scale:(s[u]||f.z)(),realScaleType:s[u]?u:"point"}}return no()(n)?{scale:n}:{scale:f.z(),realScaleType:"point"}},oy=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),u=t(e[r-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[r-1]])}},ov={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var u=np()(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}r1(t,e)}},none:r1,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}r1(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,d=0;d<u;++d){var h=t[e[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,r1(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=np()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},om=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=ov[r];return(function(){var t=(0,r5.A)([]),e=r3,r=r1,n=r6;function o(o){var i,a,u=Array.from(t.apply(this,arguments),r8),c=u.length,l=-1;for(let t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(t,u[i].key,l,o)]).data=t;for(i=0,a=(0,r2.A)(e(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,r5.A)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,r5.A)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?r3:"function"==typeof t?t:(0,r5.A)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?r1:t,o):r},o})().keys(n).value(function(t,e){return+ot(t,e,0)}).order(r3).offset(o)(t)},og=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?n9(n9({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[r],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,n2.vh)(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,n2.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return n9(n9({},t),{},n7({},u,c))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return n9(n9({},e),{},n7({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:om(t,a.items,o)}))},{})),n9(n9({},e),{},n7({},i,u))},{})},ob=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=nF(c,o,a);return t.domain([nt()(l),r9()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:nz(t.domain(),o,a)}:null},ox=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?n9(n9({},t.type.defaultProps),t.props):t.props).stackId;if((0,n2.vh)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},ow=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[nt()(e.concat([t[0]]).filter(n2.Et)),r9()(e.concat([t[1]]).filter(n2.Et))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},oO=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oj=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oA=function(t,e,r){if(no()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,n2.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(oO.test(t[0])){var o=+oO.exec(t[0])[1];n[0]=e[0]-o}else no()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,n2.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(oj.test(t[1])){var i=+oj.exec(t[1])[1];n[1]=e[1]+i}else no()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},oS=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=ng()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},oP=function(t,e,r){return!t||!t.length||nv()(t,nc()(r,"type.defaultProps.domain"))?e:t},oE=function(t,e){var r=t.type.defaultProps?n9(n9({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return n9(n9({},(0,nq.J9)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:on(t),value:ot(e,n),type:u,payload:e,chartType:c,hide:l})}},49840:(t,e,r)=>{t=r.nmd(t);var n=r(7985),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,u=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=u},49872:(t,e,r)=>{var n=r(22471);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,u=Object(r);(e?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},49940:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},50111:(t,e,r)=>{var n=r(73800),o=r(9813),i=r(39608),a=r(99544),u=r(15631),c=r(94356);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=c(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&u(s)&&a(p,s)&&(i(t)||o(t))}},50330:(t,e,r)=>{"use strict";t.exports=r(30294)},50523:(t,e,r)=>{var n=r(24376),o=r(76957),i=r(58817),a=r(38406),u=r(90724),c=r(74166),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var d=u;case"[object Set]":var h=1&n;if(d||(d=c),t.size!=e.size&&!h)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(d(t),d(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},50664:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},50687:t=>{t.exports=function(t){return function(){return t}}},50851:t=>{t.exports=function(t){return t.split("")}},51445:(t,e,r)=>{var n=r(53516);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},51911:(t,e,r)=>{var n=r(69229),o=r(48611);t.exports=function t(e,r,i,a,u){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,u):e!=e&&r!=r)}},52521:t=>{t.exports=function(t,e){return t<e}},53516:(t,e,r)=>{var n=r(20480);t.exports=r(49872)(n)},53696:(t,e,r)=>{var n=r(75899),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},54360:(t,e,r)=>{var n=r(58817);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},54811:(t,e,r)=>{"use strict";r.d(e,{f:()=>n});var n=function(t){return null};n.displayName="Cell"},54906:(t,e,r)=>{var n=r(83711);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},54913:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},55794:(t,e,r)=>{var n=r(57213),o=r(34711),i=r(18028),a=r(6305),u=r(50664),c=r(33332),l=r(9699),s=r(13465),f=r(39608);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,c(i)),u(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},55863:(t,e,r)=>{"use strict";r.d(e,{C1:()=>w,bL:()=>x});var n=r(12115),o=r(46081),i=r(63655),a=r(95155),u="Progress",[c,l]=(0,o.A)(u),[s,f]=c(u),p=n.forwardRef((t,e)=>{var r,n,o,u;let{__scopeProgress:c,value:l=null,max:f,getValueLabel:p=y,...d}=t;(f||0===f)&&!g(f)&&console.error((r="".concat(f),n="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=g(f)?f:100;null===l||b(l,h)||console.error((o="".concat(l),u="Progress","Invalid prop `value` of value `".concat(o,"` supplied to `").concat(u,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let x=b(l,h)?l:null,w=m(x)?p(x,h):void 0;return(0,a.jsx)(s,{scope:c,value:x,max:h,children:(0,a.jsx)(i.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":m(x)?x:void 0,"aria-valuetext":w,role:"progressbar","data-state":v(x,h),"data-value":null!=x?x:void 0,"data-max":h,...d,ref:e})})});p.displayName=u;var d="ProgressIndicator",h=n.forwardRef((t,e)=>{var r;let{__scopeProgress:n,...o}=t,u=f(d,n);return(0,a.jsx)(i.sG.div,{"data-state":v(u.value,u.max),"data-value":null!==(r=u.value)&&void 0!==r?r:void 0,"data-max":u.max,...o,ref:e})});function y(t,e){return"".concat(Math.round(t/e*100),"%")}function v(t,e){return null==t?"indeterminate":t===e?"complete":"loading"}function m(t){return"number"==typeof t}function g(t){return m(t)&&!isNaN(t)&&t>0}function b(t,e){return m(t)&&!isNaN(t)&&t<=e&&t>=0}h.displayName=d;var x=p,w=h},55910:(t,e,r)=>{var n=r(4854),o=r(27569),i=r(92972);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},56917:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},57082:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},57213:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},58096:t=>{t.exports=function(t){return this.__data__.has(t)}},58127:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},58260:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},58817:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},58918:(t,e,r)=>{var n=r(70771);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,l=a}return l}},58995:(t,e,r)=>{"use strict";r.d(e,{r:()=>rC});var n=r(12115),o=r(59882),i=r.n(o),a=r(40139),u=r.n(a),c=r(18940),l=r.n(c),s=r(48973),f=r.n(s),p=r(67206),d=r.n(p),h=r(91959),y=r.n(h),v=r(52596),m=r(93179),g=r(72790),b=r(2348),x=r(94517),w=r(24026),O=r(43597),j=r(70788);function A(){return(A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var S=function(t){var e=t.cx,r=t.cy,o=t.r,i=t.className,a=(0,v.A)("recharts-dot",i);return e===+e&&r===+r&&o===+o?n.createElement("circle",A({},(0,j.J9)(t,!1),(0,O._U)(t),{className:a,cx:e,cy:r,r:o})):null},P=r(44538),E=r(81519),k=r(79095),M=r(49754),_=r(16377);function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function I(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach(function(e){D(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function D(t,e,r){var n;return(n=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==T(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var N=["Webkit","Moz","O","ms"],R=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=N.reduce(function(t,n){return I(I({},t),{},D({},n+r,e))},{});return n[t]=e,n};function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){q(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function z(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,V(n.key),n)}}function W(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(W=function(){return!!t})()}function $(t){return($=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function H(t,e){return(H=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function q(t,e,r){return(e=V(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function V(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}var X=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,E.z)().domain(l()(0,u)).range([o,o+i-a]),s=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}},G=function(t){return t.changedTouches&&!!t.changedTouches.length},K=function(t){var e,r;function o(t){var e,r,n;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),r=o,n=[t],r=$(r),q(e=function(t,e){if(e&&("object"===B(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,W()?Reflect.construct(r,n||[],$(this).constructor):r.apply(this,n)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),q(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),q(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),q(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),q(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),q(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),q(e,"handleSlideDragStart",function(t){var r=G(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&H(t,e)}(o,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=Math.min(e,r),l=Math.max(e,r),s=o.getIndexInRange(n,c),f=o.getIndexInRange(n,l);return{startIndex:s-s%a,endIndex:f===u?u:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,M.kr)(r[t],o,t);return u()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==l||d.endIndex!==s)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=G(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,d=u.data,h={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,g=v.endIndex,b=function(){var t=d.length-1;return"startX"===n&&(o>i?m%p==0:g%p==0)||!!(o<i)&&g===t||"endX"===n&&(o>i?g%p==0:m%p==0)||!!(o>i)&&g===t};this.setState(q(q({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&b()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(-1!==l&&!(l>=o.length)){var s=o[l];("startX"!==e||!(s>=a))&&("endX"!==e||!(s<=i))&&this.setState(q({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,u=t.stroke;return n.createElement("rect",{stroke:u,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.data,u=t.children,c=t.padding,l=n.Children.only(u);return l?n.cloneElement(l,{x:e,y:r,width:o,height:i,margin:c,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,d=u.data,h=u.startIndex,y=u.endIndex,v=Math.max(t,this.props.x),m=F(F({},(0,j.J9)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),g=p||"Min value: ".concat(null===(r=d[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(i=d[y])||void 0===i?void 0:i.name);return n.createElement(b.W,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},o.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,u=r.travellerWidth,c=Math.min(t,e)+u,l=Math.max(Math.abs(e-t)-u,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return n.createElement(b.W,{className:"recharts-brush-texts"},n.createElement(k.E,L({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),n.createElement(k.E,L({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,i=t.x,a=t.y,u=t.width,c=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,d=s.isTextActive,h=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!(0,_.Et)(i)||!(0,_.Et)(a)||!(0,_.Et)(u)||!(0,_.Et)(c)||u<=0||c<=0)return null;var g=(0,v.A)("recharts-brush",r),x=1===n.Children.count(o),w=R("userSelect","none");return n.createElement(b.W,{className:g,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:w},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||m||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,u=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:u,x2:e+o-1,y2:u,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:u+2,x2:e+o-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):o.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return F({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?X({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&z(o.prototype,e),r&&z(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);q(K,"displayName","Brush"),q(K,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Y=r(46605),Z=r(83197),J=r(60379),Q=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},tt=r(74925),te=r.n(tt),tr=r(29794),tn=r.n(tr);function to(t){return(to="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ti(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tl(n.key),n)}}function ta(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ta(Object(r),!0).forEach(function(e){tc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ta(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tc(t,e,r){return(e=tl(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tl(t){var e=function(t,e){if("object"!=to(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=to(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==to(e)?e:e+""}var ts=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},tf=function(){var t,e;function r(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&ti(r.prototype,t),e&&ti(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();tc(tf,"EPS",1e-4);var tp=function(t){var e=Object.keys(t).reduce(function(e,r){return tu(tu({},e),{},tc({},r,tf.create(t[r])))},{});return tu(tu({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return te()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return tn()(t,function(t,r){return e[r].isInRange(t)})}})},td=r(675);function th(){return(th=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ty(t){return(ty="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tv(Object(r),!0).forEach(function(e){tw(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tg(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tg=function(){return!!t})()}function tb(t){return(tb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tx(t,e){return(tx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tw(t,e,r){return(e=tO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tO(t){var e=function(t,e){if("object"!=ty(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ty(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ty(e)?e:e+""}var tj=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=tp({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return Q(t,"discard")&&!i.isInRange(a)?null:a},tA=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=tb(t),function(t,e){if(e&&("object"===ty(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tg()?Reflect.construct(t,e||[],tb(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tx(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,o=t.y,i=t.r,a=t.alwaysShow,u=t.clipPathId,c=(0,_.vh)(e),l=(0,_.vh)(o);if((0,td.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var s=tj(this.props);if(!s)return null;var f=s.x,p=s.y,d=this.props,h=d.shape,y=d.className,m=tm(tm({clipPath:Q(this.props,"hidden")?"url(#".concat(u,")"):void 0},(0,j.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(b.W,{className:(0,v.A)("recharts-reference-dot",y)},r.renderDot(h,m),J.J.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tO(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);tw(tA,"displayName","ReferenceDot"),tw(tA,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tw(tA,"renderDot",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(S,th({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var tS=r(71571),tP=r.n(tS);r(97124);var tE=r(91113),tk=r.n(tE)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),tM=(0,n.createContext)(void 0),t_=(0,n.createContext)(void 0),tT=(0,n.createContext)(void 0),tC=(0,n.createContext)({}),tI=(0,n.createContext)(void 0),tD=(0,n.createContext)(0),tN=(0,n.createContext)(0),tR=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,u=t.children,c=t.width,l=t.height,s=tk(i);return n.createElement(tM.Provider,{value:r},n.createElement(t_.Provider,{value:o},n.createElement(tC.Provider,{value:i},n.createElement(tT.Provider,{value:s},n.createElement(tI.Provider,{value:a},n.createElement(tD.Provider,{value:l},n.createElement(tN.Provider,{value:c},u)))))))},tB=function(t){var e=(0,n.useContext)(tM);null==e&&(0,m.A)(!1);var r=e[t];return null==r&&(0,m.A)(!1),r},tL=function(t){var e=(0,n.useContext)(t_);null==e&&(0,m.A)(!1);var r=e[t];return null==r&&(0,m.A)(!1),r};function tU(t){return(tU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tF(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tF=function(){return!!t})()}function tz(t){return(tz=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tW(t,e){return(tW=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t$(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t$(Object(r),!0).forEach(function(e){tq(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tq(t,e,r){return(e=tV(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tV(t){var e=function(t,e){if("object"!=tU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tU(e)?e:e+""}function tX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tG(){return(tG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tK=function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement("line",tG({},e,{className:"recharts-reference-line-line"}))},tY=function(t,e,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var d=c.y,h=t.y.apply(d,{position:i});if(Q(c,"discard")&&!t.y.isInRange(h))return null;var y=[{x:l+f,y:h},{x:l,y:h}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(Q(c,"discard")&&!t.x.isInRange(m))return null;var g=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?g.reverse():g}if(n){var b=c.segment.map(function(e){return t.apply(e,{position:i})});return Q(c,"discard")&&tP()(b,function(e){return!t.isInRange(e)})?null:b}return null};function tZ(t){var e,r=t.x,o=t.y,i=t.segment,a=t.xAxisId,u=t.yAxisId,c=t.shape,l=t.className,s=t.alwaysShow,f=(0,n.useContext)(tI),p=tB(a),d=tL(u),h=(0,n.useContext)(tT);if(!f||!h)return null;(0,td.R)(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var y=tY(tp({x:p.scale,y:d.scale}),(0,_.vh)(r),(0,_.vh)(o),i&&2===i.length,h,t.position,p.orientation,d.orientation,t);if(!y)return null;var m=function(t){if(Array.isArray(t))return t}(y)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(y,2)||function(t,e){if(t){if("string"==typeof t)return tX(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tX(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=m[0],x=g.x,w=g.y,O=m[1],A=O.x,S=O.y,P=tH(tH({clipPath:Q(t,"hidden")?"url(#".concat(f,")"):void 0},(0,j.J9)(t,!0)),{},{x1:x,y1:w,x2:A,y2:S});return n.createElement(b.W,{className:(0,v.A)("recharts-reference-line",l)},tK(c,P),J.J.renderCallByParent(t,ts({x:(e={x1:x,y1:w,x2:A,y2:S}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var tJ=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=tz(t),function(t,e){if(e&&("object"===tU(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tF()?Reflect.construct(t,e||[],tz(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tW(t,e)}(r,t),e=[{key:"render",value:function(){return n.createElement(tZ,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tV(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tQ(){return(tQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t0(t){return(t0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t1(Object(r),!0).forEach(function(e){t8(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}tq(tJ,"displayName","ReferenceLine"),tq(tJ,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function t5(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t5=function(){return!!t})()}function t3(t){return(t3=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t6(t,e){return(t6=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t8(t,e,r){return(e=t4(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t4(t){var e=function(t,e){if("object"!=t0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t0(e)?e:e+""}var t9=function(t,e,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=tp({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},d={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!Q(o,"discard")||f.isInRange(p)&&f.isInRange(d)?ts(p,d):null},t7=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=t3(t),function(t,e){if(e&&("object"===t0(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t5()?Reflect.construct(t,e||[],t3(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&t6(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,o=t.x2,i=t.y1,a=t.y2,u=t.className,c=t.alwaysShow,l=t.clipPathId;(0,td.R)(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,_.vh)(e),f=(0,_.vh)(o),p=(0,_.vh)(i),d=(0,_.vh)(a),h=this.props.shape;if(!s&&!f&&!p&&!d&&!h)return null;var y=t9(s,f,p,d,this.props);if(!y&&!h)return null;var m=Q(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(b.W,{className:(0,v.A)("recharts-reference-area",u)},r.renderRect(h,t2(t2({clipPath:m},(0,j.J9)(this.props,!0)),y)),J.J.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t4(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function et(t){return function(t){if(Array.isArray(t))return ee(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ee(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ee(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ee(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}t8(t7,"displayName","ReferenceArea"),t8(t7,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),t8(t7,"renderRect",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(P.M,tQ({},e,{className:"recharts-reference-area-rect"}))});var er=function(t,e,r,n,o){var i=(0,j.aS)(t,tJ),a=(0,j.aS)(t,tA),u=[].concat(et(i),et(a)),c=(0,j.aS)(t,t7),l="".concat(n,"Id"),s=n[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===r&&Q(e.props,"extendDomain")&&(0,_.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),c.length){var p="".concat(s,"1"),d="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===r&&Q(e.props,"extendDomain")&&(0,_.Et)(e.props[p])&&(0,_.Et)(e.props[d])){var n=e.props[p],o=e.props[d];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,_.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},en=r(25641),eo=r(15232),ei=r(82661),ea=new(r.n(ei)()),eu="recharts.syncMouseEvents";function ec(t){return(ec="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function el(t,e,r){return(e=es(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function es(t){var e=function(t,e){if("object"!=ec(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ec(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ec(e)?e:e+""}var ef=function(){var t,e;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),el(this,"activeIndex",0),el(this,"coordinateList",[]),el(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,es(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),ep=r(67790),ed=r(70688);function eh(t){return(eh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ey=["x","y","top","left","width","height","className"];function ev(){return(ev=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function em(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var eg=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,a=t.top,u=void 0===a?0:a,c=t.left,l=void 0===c?0:c,s=t.width,f=void 0===s?0:s,p=t.height,d=void 0===p?0:p,h=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?em(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=eh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eh(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):em(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:u,left:l,width:f,height:d},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ey));return(0,_.Et)(r)&&(0,_.Et)(i)&&(0,_.Et)(f)&&(0,_.Et)(d)&&(0,_.Et)(u)&&(0,_.Et)(l)?n.createElement("path",ev({},(0,j.J9)(y,!0),{className:(0,v.A)("recharts-cross",h),d:"M".concat(r,",").concat(u,"v").concat(d,"M").concat(l,",").concat(i,"h").concat(f)})):null};function eb(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,en.IZ)(e,r,n,o),(0,en.IZ)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var ex=r(77283);function ew(t){return(ew="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ej(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eO(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=ew(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ew(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ew(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eA(t){var e,r,o,i,a=t.element,u=t.tooltipEventType,c=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,d=t.tooltipAxisBandSize,h=t.layout,y=t.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(o=a.type.defaultProps)||void 0===o?void 0:o.cursor;if(!a||!m||!c||!l||"ScatterChart"!==y&&"axis"!==u)return null;var g=ed.I;if("ScatterChart"===y)i=l,g=eg;else if("BarChart"===y)e=d/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===h?l.x-e:f.left+.5,y:"horizontal"===h?f.top+.5:l.y-e,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},g=P.M;else if("radial"===h){var b=eb(l),x=b.cx,w=b.cy,O=b.radius;i={cx:x,cy:w,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:O,outerRadius:O},g=ex.h}else i={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return eb(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,en.IZ)(u,c,l,f),d=(0,en.IZ)(u,c,s,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,l,f)},g=ed.I;var A=ej(ej(ej(ej({stroke:"#ccc",pointerEvents:"none"},f),i),(0,j.J9)(m,!1)),{},{payload:s,payloadIndex:p,className:(0,v.A)("recharts-tooltip-cursor",m.className)});return(0,n.isValidElement)(m)?(0,n.cloneElement)(m,A):(0,n.createElement)(g,A)}var eS=["item"],eP=["children","className","width","height","style","compact","title","desc"];function eE(t){return(eE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ek(){return(ek=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eM(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||eN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e_(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function eT(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eT=function(){return!!t})()}function eC(t){return(eC=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eI(t,e){return(eI=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eD(t){return function(t){if(Array.isArray(t))return eR(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||eN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eN(t,e){if(t){if("string"==typeof t)return eR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eR(t,e)}}function eR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function eB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eB(Object(r),!0).forEach(function(e){eU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eU(t,e,r){return(e=eF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eF(t){var e=function(t,e){if("object"!=eE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eE(e)?e:e+""}var ez={xAxis:["bottom","top"],yAxis:["left","right"]},eW={width:"100%",height:"100%"},e$={x:0,y:0};function eH(t){return t}var eq=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return eL(eL(eL({},n),(0,en.IZ)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return eL(eL(eL({},n),(0,en.IZ)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return e$},eV=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(eD(t),eD(r)):t},[]);return i.length>0?i:t&&t.length&&(0,_.Et)(n)&&(0,_.Et)(o)?t.slice(n,o+1):[]};function eX(t){return"number"===t?[0,"auto"]:void 0}var eG=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=eV(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,_.eP)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(eD(o),[(0,M.zb)(u,l)]):o},[])},eK=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,M.gH)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=eG(t,e,l,s),p=eq(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eY=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,a=e.axisIdKey,u=e.stackGroups,c=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,d=t.stackOffset,h=(0,M._L)(f,o);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?eL(eL({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,g=y.allowDataOverflow,b=y.allowDuplicatedCategory,x=y.scale,w=y.ticks,O=y.includeHidden,j=y[a];if(e[j])return e;var A=eV(t.data,{graphicalItems:n.filter(function(t){var e;return(a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a])===j}),dataStartIndex:c,dataEndIndex:s}),S=A.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&(0,_.Et)(n)&&(0,_.Et)(o))return!0}return!1})(y.domain,g,v)&&(k=(0,M.AQ)(y.domain,null,g),h&&("number"===v||"auto"!==x)&&(C=(0,M.Ay)(A,m,"category")));var P=eX(v);if(!k||0===k.length){var E,k,T,C,I,D=null!==(I=y.domain)&&void 0!==I?I:P;if(m){if(k=(0,M.Ay)(A,m,v),"category"===v&&h){var N=(0,_.CG)(k);b&&N?(T=k,k=l()(0,S)):b||(k=(0,M.KC)(D,k,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(eD(t),[e])},[]))}else if("category"===v)k=b?k.filter(function(t){return""!==t&&!i()(t)}):(0,M.KC)(D,k,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||i()(e)?t:[].concat(eD(t),[e])},[]);else if("number"===v){var R=(0,M.A1)(A,n.filter(function(t){var e,r,n=a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===j&&(O||!o)}),m,o,f);R&&(k=R)}h&&("number"===v||"auto"!==x)&&(C=(0,M.Ay)(A,m,"category"))}else k=h?l()(0,S):u&&u[j]&&u[j].hasStack&&"number"===v?"expand"===d?[0,1]:(0,M.Mk)(u[j].stackGroups,c,s):(0,M.vf)(A,n.filter(function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(O||!r)}),v,f,!0);"number"===v?(k=er(p,k,j,o,w),D&&(k=(0,M.AQ)(D,k,g))):"category"===v&&D&&k.every(function(t){return D.indexOf(t)>=0})&&(k=D)}return eL(eL({},e),{},eU({},j,eL(eL({},y),{},{axisType:o,domain:k,categoricalDomain:C,duplicateDomain:T,originalDomain:null!==(E=y.domain)&&void 0!==E?E:P,isCategorical:h,layout:f})))},{})},eZ=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.layout,p=t.children,d=eV(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),h=d.length,y=(0,M._L)(s,o),v=-1;return r.reduce(function(t,e){var m,g=(void 0!==e.type.defaultProps?eL(eL({},e.type.defaultProps),e.props):e.props)[i],b=eX("number");return t[g]?t:(v++,m=y?l()(0,h):a&&a[g]&&a[g].hasStack?er(p,m=(0,M.Mk)(a[g].stackGroups,u,c),g,o):er(p,m=(0,M.AQ)(b,(0,M.vf)(d,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===g&&!o}),"number",s),n.defaultProps.allowDataOverflow),g,o),eL(eL({},t),{},eU({},g,eL(eL({axisType:o},n.defaultProps),{},{hide:!0,orientation:f()(ez,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:b,isCategorical:y,layout:s}))))},{})},eJ=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,j.aS)(l,o),p={};return f&&f.length?p=eY(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=eZ(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},eQ=function(t){var e=(0,_.lX)(t),r=(0,M.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:d()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,M.Hj)(e,r)}},e0=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,j.BU)(e,K),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},e1=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},e2=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,p=r.margin||{},d=(0,j.BU)(s,K),h=(0,j.BU)(s,w.s),y=Object.keys(u).reduce(function(t,e){var r=u[e],n=r.orientation;return r.mirror||r.hide?t:eL(eL({},t),{},eU({},n,t[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:eL(eL({},t),{},eU({},n,f()(t,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=eL(eL({},v),y),g=m.bottom;d&&(m.bottom+=d.props.height||K.defaultProps.height),h&&e&&(m=(0,M.s0)(m,n,r,e));var b=c-m.left-m.right,x=l-m.top-m.bottom;return eL(eL({brushBottom:g},m),{},{width:Math.max(b,0),height:Math.max(x,0)})},e5=["points","className","baseLinePoints","connectNulls"];function e3(){return(e3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e6(t){return function(t){if(Array.isArray(t))return e8(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return e8(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return e8(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var e4=function(t){return t&&t.x===+t.x&&t.y===+t.y},e9=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){e4(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),e4(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},e7=function(t,e){var r=e9(t);e&&(r=[r.reduce(function(t,e){return[].concat(e6(t),e6(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},rt=function(t,e,r){var n=e7(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(e7(e.reverse(),r).slice(1))},re=function(t){var e=t.points,r=t.className,o=t.baseLinePoints,i=t.connectNulls,a=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,e5);if(!e||!e.length)return null;var u=(0,v.A)("recharts-polygon",r);if(o&&o.length){var c=a.stroke&&"none"!==a.stroke,l=rt(e,o,i);return n.createElement("g",{className:u},n.createElement("path",e3({},(0,j.J9)(a,!0),{fill:"Z"===l.slice(-1)?a.fill:"none",stroke:"none",d:l})),c?n.createElement("path",e3({},(0,j.J9)(a,!0),{fill:"none",d:e7(e,i)})):null,c?n.createElement("path",e3({},(0,j.J9)(a,!0),{fill:"none",d:e7(o,i)})):null)}var s=e7(e,i);return n.createElement("path",e3({},(0,j.J9)(a,!0),{fill:"Z"===s.slice(-1)?a.fill:"none",className:u,d:s}))};function rr(t){return(rr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rn(){return(rn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ro(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ri(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ro(Object(r),!0).forEach(function(e){rs(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ro(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ra(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rf(n.key),n)}}function ru(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ru=function(){return!!t})()}function rc(t){return(rc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rl(t,e){return(rl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rs(t,e,r){return(e=rf(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rf(t){var e=function(t,e){if("object"!=rr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rr(e)?e:e+""}var rp=Math.PI/180,rd=function(t){var e,r;function o(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),t=o,e=arguments,t=rc(t),function(t,e){if(e&&("object"===rr(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ru()?Reflect.construct(t,e||[],rc(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rl(t,e)}(o,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,u=(0,en.IZ)(r,n,o,t.coordinate),c=(0,en.IZ)(r,n,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:u.x,y1:u.y,x2:c.x,y2:c.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*rp);return r>1e-5?"outer"===e?"start":"end":r<-1e-5?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,o=t.radius,i=t.axisLine,a=t.axisLineType,u=ri(ri({},(0,j.J9)(this.props,!1)),{},{fill:"none"},(0,j.J9)(i,!1));if("circle"===a)return n.createElement(S,rn({className:"recharts-polar-angle-axis-line"},u,{cx:e,cy:r,r:o}));var c=this.props.ticks.map(function(t){return(0,en.IZ)(e,r,o,t.coordinate)});return n.createElement(re,rn({className:"recharts-polar-angle-axis-line"},u,{points:c}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,i=e.tick,a=e.tickLine,u=e.tickFormatter,c=e.stroke,l=(0,j.J9)(this.props,!1),s=(0,j.J9)(i,!1),f=ri(ri({},l),{},{fill:"none"},(0,j.J9)(a,!1)),p=r.map(function(e,r){var p=t.getTickLineCoord(e),d=ri(ri(ri({textAnchor:t.getTickTextAnchor(e)},l),{},{stroke:"none",fill:c},s),{},{index:r,payload:e,x:p.x2,y:p.y2});return n.createElement(b.W,rn({className:(0,v.A)("recharts-polar-angle-axis-tick",(0,en.Zk)(i)),key:"tick-".concat(e.coordinate)},(0,O.XC)(t.props,e,r)),a&&n.createElement("line",rn({className:"recharts-polar-angle-axis-tick-line"},f,p)),i&&o.renderTickItem(i,d,u?u(e.value,r):e.value))});return n.createElement(b.W,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,o=t.axisLine;return!(r<=0)&&e&&e.length?n.createElement(b.W,{className:(0,v.A)("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var o;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(k.E,rn({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&ra(o.prototype,e),r&&ra(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);rs(rd,"displayName","PolarAngleAxis"),rs(rd,"axisType","angleAxis"),rs(rd,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var rh=r(83134),ry=r.n(rh),rv=r(14268),rm=r.n(rv),rg=["cx","cy","angle","ticks","axisLine"],rb=["ticks","tick","angle","tickFormatter","stroke"];function rx(t){return(rx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rw(){return(rw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function rO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rO(Object(r),!0).forEach(function(e){rM(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rA(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function rS(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,r_(n.key),n)}}function rP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rP=function(){return!!t})()}function rE(t){return(rE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rk(t,e){return(rk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rM(t,e,r){return(e=r_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r_(t){var e=function(t,e){if("object"!=rx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rx(e)?e:e+""}var rT=function(t){var e,r;function o(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),t=o,e=arguments,t=rE(t),function(t,e){if(e&&("object"===rx(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,rP()?Reflect.construct(t,e||[],rE(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rk(t,e)}(o,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return(0,en.IZ)(o,i,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=ry()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:rm()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,o=t.angle,i=t.ticks,a=t.axisLine,u=rA(t,rg),c=i.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=(0,en.IZ)(e,r,c[0],o),s=(0,en.IZ)(e,r,c[1],o),f=rj(rj(rj({},(0,j.J9)(u,!1)),{},{fill:"none"},(0,j.J9)(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return n.createElement("line",rw({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,i=e.tick,a=e.angle,u=e.tickFormatter,c=e.stroke,l=rA(e,rb),s=this.getTickTextAnchor(),f=(0,j.J9)(l,!1),p=(0,j.J9)(i,!1),d=r.map(function(e,r){var l=t.getTickValueCoord(e),d=rj(rj(rj(rj({textAnchor:s,transform:"rotate(".concat(90-a,", ").concat(l.x,", ").concat(l.y,")")},f),{},{stroke:"none",fill:c},p),{},{index:r},l),{},{payload:e});return n.createElement(b.W,rw({className:(0,v.A)("recharts-polar-radius-axis-tick",(0,en.Zk)(i)),key:"tick-".concat(e.coordinate)},(0,O.XC)(t.props,e,r)),o.renderTickItem(i,d,u?u(e.value,r):e.value))});return n.createElement(b.W,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,o=t.tick;return e&&e.length?n.createElement(b.W,{className:(0,v.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),o&&this.renderTicks(),J.J.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var o;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(k.E,rw({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&rS(o.prototype,e),r&&rS(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);rM(rT,"displayName","PolarRadiusAxis"),rM(rT,"axisType","radiusAxis"),rM(rT,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var rC=function(t){var e=t.chartName,r=t.GraphicalChild,o=t.defaultTooltipEventType,a=void 0===o?"axis":o,c=t.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=t.axisComponents,p=t.legendContent,d=t.formatAxisMap,h=t.defaultProps,w=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,a=e.updateId,u=e.dataStartIndex,c=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,d=t.barCategoryGap,h=t.maxBarSize,y=e1(f),v=y.numericAxisName,g=y.cateAxisName,b=!!r&&!!r.length&&r.some(function(t){var e=(0,j.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var w=eV(t.data,{graphicalItems:[r],dataStartIndex:u,dataEndIndex:c}),O=void 0!==r.type.defaultProps?eL(eL({},r.type.defaultProps),r.props):r.props,A=O.dataKey,S=O.maxBarSize,P=O["".concat(v,"Id")],E=O["".concat(g,"Id")],k=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=O["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,m.A)(!1);var i=n[o];return eL(eL({},t),{},eU(eU({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,M.Rh)(i)))},{}),_=k[g],T=k["".concat(g,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,M.kA)(r,n[P].stackGroups),I=(0,j.Mn)(r.type).indexOf("Bar")>=0,D=(0,M.Hj)(_,T),N=[],R=b&&(0,M.tA)({barSize:l,stackGroups:n,totalSize:"xAxis"===g?k[g].width:"yAxis"===g?k[g].height:void 0});if(I){var B,L,U=i()(S)?h:S,F=null!==(B=null!==(L=(0,M.Hj)(_,T,!0))&&void 0!==L?L:U)&&void 0!==B?B:0;N=(0,M.BX)({barGap:p,barCategoryGap:d,bandSize:F!==D?F:D,sizeList:R[E],maxBarSize:U}),F!==D&&(N=N.map(function(t){return eL(eL({},t),{},{position:eL(eL({},t.position),{},{offset:t.position.offset-F/2})})}))}var z=r&&r.type&&r.type.getComposedData;z&&x.push({props:eL(eL({},z(eL(eL({},k),{},{displayedData:w,props:t,dataKey:A,item:r,bandSize:D,barPosition:N,offset:o,stackedData:C,layout:f,dataStartIndex:u,dataEndIndex:c}))),{},eU(eU(eU({key:r.key||"item-".concat(y)},v,k[v]),g,k[g]),"animationId",a)),childIndex:(0,j.AW)(r,t.children),item:r})}),x},A=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!(0,j.Me)({props:o}))return null;var c=o.children,l=o.layout,f=o.stackOffset,p=o.data,h=o.reverseStackOrder,y=e1(l),v=y.numericAxisName,m=y.cateAxisName,g=(0,j.aS)(c,r),b=(0,M.Mn)(p,g,"".concat(v,"Id"),"".concat(m,"Id"),f,h),x=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return eL(eL({},t),{},eU({},r,eJ(o,eL(eL({},e),{},{graphicalItems:g,stackGroups:e.axisType===v&&b,dataStartIndex:i,dataEndIndex:a}))))},{}),O=e2(eL(eL({},x),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(t){x[t]=d(o,x[t],O,t.replace("Map",""),e)});var A=eQ(x["".concat(m,"Map")]),S=w(o,eL(eL({},x),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:g,stackGroups:b,offset:O}));return eL(eL({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:b},A),x)},E=function(t){var r;function o(t){var r,a,c,l,s;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o),l=o,s=[t],l=eC(l),eU(c=function(t,e){if(e&&("object"===eE(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eT()?Reflect.construct(l,s||[],eC(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),eU(c,"accessibilityManager",new ef),eU(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(eL({legendBBox:t},A({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},eL(eL({},c.state),{},{legendBBox:t}))))}}),eU(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),eU(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return eL({dataStartIndex:e,dataEndIndex:r},A({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),eU(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=eL(eL({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;u()(n)&&n(r,t)}}),eU(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?eL(eL({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;u()(n)&&n(r,t)}),eU(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),eU(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),eU(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),eU(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;u()(r)&&r(e,t)}),eU(c,"handleOuterEvent",function(t){var e,r,n=(0,j.X_)(t),o=f()(c.props,"".concat(n));n&&u()(o)&&o(null!==(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)}),eU(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=eL(eL({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;u()(n)&&n(r,t)}}),eU(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;u()(e)&&e(c.getMouseInfo(t),t)}),eU(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;u()(e)&&e(c.getMouseInfo(t),t)}),eU(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),eU(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),eU(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),eU(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;u()(e)&&e(c.getMouseInfo(t),t)}),eU(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;u()(e)&&e(c.getMouseInfo(t),t)}),eU(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&ea.emit(eu,c.props.syncId,t,c.eventEmitterSymbol)}),eU(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(eL({dataStartIndex:i,dataEndIndex:a},A({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(d,t);else if("value"===n){s=-1;for(var h=0;h<d.length;h++)if(d[h].value===t.activeLabel){s=h;break}}var y=eL(eL({},p),{},{x:p.left,y:p.top}),v=Math.min(u,y.x+y.width),m=Math.min(l,y.y+y.height),g=d[s]&&d[s].value,b=eG(c.state,c.props.data,s),x=d[s]?{x:"horizontal"===r?d[s].coordinate:v,y:"horizontal"===r?m:d[s].coordinate}:e$;c.setState(eL(eL({},t),{},{activeLabel:g,activeCoordinate:x,activePayload:b,activeTooltipIndex:s}))}else c.setState(t)}),eU(c,"renderCursor",function(t){var r,o=c.state,i=o.isTooltipActive,a=o.activeCoordinate,u=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=c.getTooltipEventType(),d=null!==(r=t.props.active)&&void 0!==r?r:i,h=c.props.layout,y=t.key||"_recharts-cursor";return n.createElement(eA,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:d,layout:h,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),eU(c,"renderPolarAxis",function(t,e,r){var o=f()(t,"type.axisType"),i=f()(c.state,"".concat(o,"Map")),a=t.type.defaultProps,u=void 0!==a?eL(eL({},a),t.props):t.props,l=i&&i[u["".concat(o,"Id")]];return(0,n.cloneElement)(t,eL(eL({},l),{},{className:(0,v.A)(o,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,M.Rh)(l,!0)}))}),eU(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=(0,_.lX)(u),f=(0,_.lX)(l),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,M.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,M.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),eU(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,o=e.width,i=e.height,a=c.props.margin||{},u=o-(a.left||0)-(a.right||0),l=(0,Z.g)({children:r,formattedGraphicalItems:t,legendWidth:u,legendContent:p});if(!l)return null;var s=l.item,f=e_(l,eS);return(0,n.cloneElement)(s,eL(eL({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),eU(c,"renderTooltip",function(){var t,e=c.props,r=e.children,o=e.accessibilityLayer,i=(0,j.BU)(r,x.m);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,d=null!==(t=i.props.active)&&void 0!==t?t:u;return(0,n.cloneElement)(i,{viewBox:eL(eL({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?s:[],coordinate:l,accessibilityLayer:o})}),eU(c,"renderBrush",function(t){var e=c.props,r=e.margin,o=e.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,M.HQ)(c.handleBrushChange,t.props.onChange),data:o,x:(0,_.Et)(t.props.x)?t.props.x:a.left,y:(0,_.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,_.Et)(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),eU(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var o=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,d=void 0===p?s.xAxisId:p,h=f.yAxisId,y=void 0===h?s.yAxisId:h;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[d],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),eU(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,i=t.childIndex,a=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?eL(eL({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=eL(eL({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,M.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,j.J9)(s,!1)),(0,O._U)(s));return u.push(o.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(i))),n?u.push(o.renderActiveDot(s,eL(eL({},f),{},{cx:n.x,cy:n.y}),"".concat(c,"-basePoint-").concat(i))):a&&u.push(null),u}),eU(c,"renderGraphicChild",function(t,e,r){var o=c.filterFormatItem(t,e,r);if(!o)return null;var a=c.getTooltipEventType(),u=c.state,l=u.isTooltipActive,s=u.tooltipAxis,f=u.activeTooltipIndex,p=u.activeLabel,d=c.props.children,h=(0,j.BU)(d,x.m),y=o.props,v=y.points,m=y.isRange,g=y.baseLine,b=void 0!==o.item.type.defaultProps?eL(eL({},o.item.type.defaultProps),o.item.props):o.item.props,w=b.activeDot,O=b.hide,A=b.activeBar,S=b.activeShape,P=!!(!O&&l&&h&&(w||A||S)),E={};"axis"!==a&&h&&"click"===h.props.trigger?E={onClick:(0,M.HQ)(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(E={onMouseLeave:(0,M.HQ)(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,M.HQ)(c.handleItemMouseEnter,t.props.onMouseEnter)});var k=(0,n.cloneElement)(t,eL(eL({},o.props),E));if(P){if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var T="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());I=(0,_.eP)(v,T,p),D=m&&g&&(0,_.eP)(g,T,p)}else I=null==v?void 0:v[f],D=m&&g&&g[f];if(S||A){var C=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,eL(eL(eL({},o.props),E),{},{activeIndex:C})),null,null]}if(!i()(I))return[k].concat(eD(c.renderActivePoints({item:o,activePoint:I,basePoint:D,childIndex:f,isRange:m})))}else{var I,D,N,R=(null!==(N=c.getItemByXY(c.state.activeCoordinate))&&void 0!==N?N:{graphicalItem:k}).graphicalItem,B=R.item,L=void 0===B?t:B,U=R.childIndex,F=eL(eL(eL({},o.props),E),{},{activeIndex:U});return[(0,n.cloneElement)(L,F),null,null]}}return m?[k,null,null]:[k,null]}),eU(c,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,eL(eL({key:"recharts-customized-".concat(r)},c.props),c.state))}),eU(c,"renderMap",{CartesianGrid:{handler:eH,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:eH},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:eH},YAxis:{handler:eH},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:(0,_.NF)("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=y()(c.triggeredAfterMouseMove,null!==(a=t.throttleDelay)&&void 0!==a?a:1e3/60),c.state={},c}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eI(t,e)}(o,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,j.BU)(e,x.m);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=eG(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=eL(eL({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,j.OV)([(0,j.BU)(t.children,x.m)],[(0,j.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,j.BU)(this.props.children,x.m);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,Y.A3)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap;if("axis"!==this.getTooltipEventType()&&c&&l){var s=(0,_.lX)(c).scale,f=(0,_.lX)(l).scale,p=s&&s.invert?s.invert(o.chartX):null,d=f&&f.invert?f.invert(o.chartY):null;return eL(eL({},o),{},{xValue:p,yValue:d})}var h=eK(this.state,this.props.data,this.props.layout,a);return h?eL(eL({},o),h):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,_.lX)(c);return(0,en.yy)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,j.BU)(t,x.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),eL(eL({},(0,O._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){ea.on(eu,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){ea.removeListener(eu,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,j.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=eM(e,2),n=r[0],o=r[1];return eL(eL({},t),{},eU({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=eM(e,2),n=r[0],o=r[1];return eL(eL({},t),{},eU({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?eL(eL({},c.type.defaultProps),c.props):c.props,s=(0,j.Mn)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(e){return(0,P.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(e){return(0,en.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,ep.NE)(a,n)||(0,ep.nZ)(a,n)||(0,ep.xQ)(a,n)){var d=(0,ep.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),h=void 0===l.activeIndex?d:l.activeIndex;return{graphicalItem:eL(eL({},a),{},{childIndex:h}),payload:(0,ep.xQ)(a,n)?l.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,j.Me)(this))return null;var o=this.props,i=o.children,a=o.className,u=o.width,c=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,d=e_(o,eP),h=(0,j.J9)(d,!1);if(s)return n.createElement(tR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(g.u,ek({},h,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,j.ee)(i,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,h.role=null!==(e=this.props.role)&&void 0!==e?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(tR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",ek({className:(0,v.A)("recharts-wrapper",a),style:eL({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(t){r.container=t}}),n.createElement(g.u,ek({},h,{width:u,height:c,title:f,desc:p,style:eW}),this.renderClipPath(),(0,j.ee)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eF(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);eU(E,"displayName",e),eU(E,"defaultProps",eL({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h)),eU(E,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,a=t.width,u=t.height,c=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var d=e0(t);return eL(eL(eL({},d),{},{updateId:0},A(eL(eL({props:t},d),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||u!==e.prevHeight||c!==e.prevLayout||l!==e.prevStackOffset||!(0,eo.b)(s,e.prevMargin)){var h=e0(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=eL(eL({},eK(e,n,c)),{},{updateId:e.updateId+1}),m=eL(eL(eL({},h),y),v);return eL(eL(eL({},m),A(eL({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,j.OV)(o,e.prevChildren)){var g,b,x,w,O=(0,j.BU)(o,K),S=O&&null!==(g=null===(b=O.props)||void 0===b?void 0:b.startIndex)&&void 0!==g?g:f,P=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:p,E=i()(n)||S!==f||P!==p?e.updateId+1:e.updateId;return eL(eL({updateId:E},A(eL(eL({props:t},e),{},{updateId:E,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:o,dataStartIndex:S,dataEndIndex:P})}return null}),eU(E,"renderActiveDot",function(t,e,r){var o;return o=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):u()(t)?t(e):n.createElement(S,e),n.createElement(b.W,{className:"recharts-active-dot",key:r},o)});var k=(0,n.forwardRef)(function(t,e){return n.createElement(E,ek({},t,{ref:e}))});return k.displayName=E.displayName,k}({chartName:"PieChart",GraphicalChild:r(79133).F,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:rd},{axisType:"radiusAxis",AxisComp:rT}],formatAxisMap:en.pr,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},59882:t=>{t.exports=function(t){return null==t}},60245:(t,e,r)=>{var n=r(51911);t.exports=function(t,e){return n(t,e)}},60379:(t,e,r)=>{"use strict";r.d(e,{J:()=>S});var n=r(12115),o=r(59882),i=r.n(o),a=r(40139),u=r.n(a),c=r(67460),l=r.n(c),s=r(52596),f=r(79095),p=r(70788),d=r(16377),h=r(25641);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var w=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return u()(r)?r(n):n},O=function(t,e,r){var o,a,u=t.position,c=t.viewBox,l=t.offset,f=t.className,p=c.cx,y=c.cy,v=c.innerRadius,m=c.outerRadius,g=c.startAngle,b=c.endAngle,w=c.clockWise,O=(v+m)/2,j=(0,d.sA)(b-g)*Math.min(Math.abs(b-g),360),A=j>=0?1:-1;"insideStart"===u?(o=g+A*l,a=w):"insideEnd"===u?(o=b-A*l,a=!w):"end"===u&&(o=b+A*l,a=w),a=j<=0?a:!a;var S=(0,h.IZ)(p,y,O,o),P=(0,h.IZ)(p,y,O,o+(a?1:-1)*359),E="M".concat(S.x,",").concat(S.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!a,",\n    ").concat(P.x,",").concat(P.y),k=i()(t.id)?(0,d.NF)("recharts-radial-line-"):t.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:k,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(k)},e))},j=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,u=e.outerRadius,c=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,h.IZ)(o,i,u+r,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,h.IZ)(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},A=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,u=e.width,c=e.height,s=c>=0?1:-1,f=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=u>=0?1:-1,v=y*n,m=y>0?"end":"start",g=y>0?"start":"end";if("top"===o)return b(b({},{x:i+u/2,y:a-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return b(b({},{x:i+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var x={x:i-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return b(b({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===o){var w={x:i+u+v,y:a+c/2,textAnchor:g,verticalAnchor:"middle"};return b(b({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var O=r?{width:u,height:c}:{};return"insideLeft"===o?b({x:i+v,y:a+c/2,textAnchor:g,verticalAnchor:"middle"},O):"insideRight"===o?b({x:i+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},O):"insideTop"===o?b({x:i+u/2,y:a+f,textAnchor:"middle",verticalAnchor:h},O):"insideBottom"===o?b({x:i+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},O):"insideTopLeft"===o?b({x:i+v,y:a+f,textAnchor:g,verticalAnchor:h},O):"insideTopRight"===o?b({x:i+u-v,y:a+f,textAnchor:m,verticalAnchor:h},O):"insideBottomLeft"===o?b({x:i+v,y:a+c-f,textAnchor:g,verticalAnchor:p},O):"insideBottomRight"===o?b({x:i+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},O):l()(o)&&((0,d.Et)(o.x)||(0,d._3)(o.x))&&((0,d.Et)(o.y)||(0,d._3)(o.y))?b({x:i+(0,d.F4)(o.x,u),y:a+(0,d.F4)(o.y,c),textAnchor:"end",verticalAnchor:"end"},O):b({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},O)};function S(t){var e,r=t.offset,o=b({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,v)),a=o.viewBox,c=o.position,l=o.value,h=o.children,y=o.content,m=o.className,g=o.textBreakAll;if(!a||i()(l)&&i()(h)&&!(0,n.isValidElement)(y)&&!u()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,o);if(u()(y)){if(e=(0,n.createElement)(y,o),(0,n.isValidElement)(e))return e}else e=w(o);var S="cx"in a&&(0,d.Et)(a.cx),P=(0,p.J9)(o,!0);if(S&&("insideStart"===c||"insideEnd"===c||"end"===c))return O(o,e,P);var E=S?j(o):A(o);return n.createElement(f.E,x({className:(0,s.A)("recharts-label",void 0===m?"":m)},P,E,{breakAll:g}),e)}S.displayName="Label";var P=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,v=t.height,m=t.clockWise,g=t.labelViewBox;if(g)return g;if((0,d.Et)(y)&&(0,d.Et)(v)){if((0,d.Et)(s)&&(0,d.Et)(f))return{x:s,y:f,width:y,height:v};if((0,d.Et)(p)&&(0,d.Et)(h))return{x:p,y:h,width:y,height:v}}return(0,d.Et)(s)&&(0,d.Et)(f)?{x:s,y:f,width:0,height:0}:(0,d.Et)(e)&&(0,d.Et)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};S.parseViewBox=P,S.renderCallByParent=function(t,e){var r,o,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var a=t.children,c=P(t),s=(0,p.aS)(a,S).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||c,key:"label-".concat(r)})});if(!i)return s;return[(r=t.label,o=e||c,r?!0===r?n.createElement(S,{key:"label-implicit",viewBox:o}):(0,d.vh)(r)?n.createElement(S,{key:"label-implicit",viewBox:o,value:r}):(0,n.isValidElement)(r)?r.type===S?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:o}):n.createElement(S,{key:"label-implicit",content:r,viewBox:o}):u()(r)?n.createElement(S,{key:"label-implicit",content:r,viewBox:o}):l()(r)?n.createElement(S,x({viewBox:o},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return m(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return m(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(t,e)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},60760:(t,e,r)=>{"use strict";r.d(e,{N:()=>m});var n=r(95155),o=r(12115),i=r(90869),a=r(82885),u=r(80845),c=r(51508);class l extends o.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function s(t){let{children:e,isPresent:r}=t,i=(0,o.useId)(),a=(0,o.useRef)(null),u=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:s}=(0,o.useContext)(c.Q);return(0,o.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:o}=u.current;if(r||!a.current||!t||!e)return;a.current.dataset.motionPopId=i;let c=document.createElement("style");return s&&(c.nonce=s),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            top: ").concat(n,"px !important;\n            left: ").concat(o,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}},[r]),(0,n.jsx)(l,{isPresent:r,childRef:a,sizeRef:u,children:o.cloneElement(e,{ref:a})})}let f=t=>{let{children:e,initial:r,isPresent:i,onExitComplete:c,custom:l,presenceAffectsLayout:f,mode:d}=t,h=(0,a.M)(p),y=(0,o.useId)(),v=(0,o.useCallback)(t=>{for(let e of(h.set(t,!0),h.values()))if(!e)return;c&&c()},[h,c]),m=(0,o.useMemo)(()=>({id:y,initial:r,isPresent:i,custom:l,onExitComplete:v,register:t=>(h.set(t,!1),()=>h.delete(t))}),f?[Math.random(),v]:[i,v]);return(0,o.useMemo)(()=>{h.forEach((t,e)=>h.set(e,!1))},[i]),o.useEffect(()=>{i||h.size||!c||c()},[i]),"popLayout"===d&&(e=(0,n.jsx)(s,{isPresent:i,children:e})),(0,n.jsx)(u.t.Provider,{value:m,children:e})};function p(){return new Map}var d=r(32082);let h=t=>t.key||"";function y(t){let e=[];return o.Children.forEach(t,t=>{(0,o.isValidElement)(t)&&e.push(t)}),e}var v=r(97494);let m=t=>{let{children:e,custom:r,initial:u=!0,onExitComplete:c,presenceAffectsLayout:l=!0,mode:s="sync",propagate:p=!1}=t,[m,g]=(0,d.xQ)(p),b=(0,o.useMemo)(()=>y(e),[e]),x=p&&!m?[]:b.map(h),w=(0,o.useRef)(!0),O=(0,o.useRef)(b),j=(0,a.M)(()=>new Map),[A,S]=(0,o.useState)(b),[P,E]=(0,o.useState)(b);(0,v.E)(()=>{w.current=!1,O.current=b;for(let t=0;t<P.length;t++){let e=h(P[t]);x.includes(e)?j.delete(e):!0!==j.get(e)&&j.set(e,!1)}},[P,x.length,x.join("-")]);let k=[];if(b!==A){let t=[...b];for(let e=0;e<P.length;e++){let r=P[e],n=h(r);x.includes(n)||(t.splice(e,0,r),k.push(r))}"wait"===s&&k.length&&(t=k),E(y(t)),S(b);return}let{forceRender:M}=(0,o.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:P.map(t=>{let e=h(t),o=(!p||!!m)&&(b===P||x.includes(e));return(0,n.jsx)(f,{isPresent:o,initial:(!w.current||!!u)&&void 0,custom:o?void 0:r,presenceAffectsLayout:l,mode:s,onExitComplete:o?void 0:()=>{if(!j.has(e))return;j.set(e,!0);let t=!0;j.forEach(e=>{e||(t=!1)}),t&&(null==M||M(),E(O.current),p&&(null==g||g()),c&&c())},children:t},e)})})}},61021:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("FolderPlus",[["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"M9 13h6",key:"1uhe8q"}],["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]])},61632:(t,e,r)=>{var n=r(16571);t.exports=r(36730)(n)},61830:(t,e,r)=>{var n=r(54360);t.exports=function(t){return n(this.__data__,t)>-1}},62464:(t,e,r)=>{var n=r(3562),o=r(18028),i=r(23360),a=Math.max;t.exports=function(t,e,r){var u=null==t?0:t.length;if(!u)return -1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(t,o(e,3),c)}},62962:(t,e,r)=>{var n=r(48659),o=r(65531),i=r(75145),a=r(85855);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,u=r?r[0]:e.charAt(0),c=r?n(r,1).join(""):e.slice(1);return u[t]()+c}}},64189:(t,e,r)=>{var n=r(74366),o=r(39641),i=r(42233);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},64439:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},64509:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("WalletCards",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2",key:"4125el"}],["path",{d:"M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21",key:"1dpki6"}]])},64588:(t,e,r)=>{var n=r(84760),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(c),n(t,this,l)}}},65531:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},65646:(t,e,r)=>{var n=r(91569),o=r(39608);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},65796:t=>{t.exports=function(t){return this.__data__.get(t)}},65836:(t,e,r)=>{var n=r(85090),o=r(7548),i=r(39984),a=r(82954),u=r(82596),c=r(74166);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,d=[],h=d;if(r)p=!1,s=i;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,s=a,h=new n}else h=e?[]:d;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var g=h.length;g--;)if(h[g]===m)continue e;e&&h.push(m),d.push(v)}else s(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},66373:(t,e,r)=>{t.exports=r(83711)(r(82500),"Promise")},67206:(t,e,r)=>{var n=r(77969),o=r(55794),i=r(21087),a=r(39641);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},67460:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},67472:(t,e,r)=>{var n=r(31598),o=r(90453),i=r(47995),a=r(65796),u=r(58096),c=r(7771);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,t.exports=l},67790:(t,e,r)=>{"use strict";r.d(e,{yp:()=>C,GG:()=>U,NE:()=>I,nZ:()=>D,xQ:()=>N});var n=r(12115),o=r(40139),i=r.n(o),a=r(44482),u=r.n(a),c=r(56917),l=r.n(c),s=r(60245),f=r.n(s),p=r(44538),d=r(52596),h=r(9557),y=r(70788);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var w=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},O={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=function(t){var e,r=x(x({},O),t),o=(0,n.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return g(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],u=i[1];(0,n.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}},[]);var c=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,b=r.animationEasing,j=r.animationDuration,A=r.animationBegin,S=r.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var P=(0,d.A)("recharts-trapezoid",v);return S?n.createElement(h.Ay,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:j,animationEasing:b,isActive:S},function(t){var e=t.upperWidth,i=t.lowerWidth,u=t.height,c=t.x,l=t.y;return n.createElement(h.Ay,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:A,duration:j,easing:b},n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:w(c,l,e,i,u),ref:o})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:w(c,l,s,f,p)})))},A=r(77283),S=r(2348),P=r(9795),E=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(j,r);case"sector":return n.createElement(A.h,r);case"symbols":if("symbols"===e)return n.createElement(P.i,r);break;default:return null}}function C(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,c=t.activeClassName,s=t.isActive,f=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,E);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,_(_({},f),(0,n.isValidElement)(r)?r.props:r));else if(i()(r))e=r(f);else if(u()(r)&&!l()(r)){var p=(void 0===a?function(t,e){return _(_({},e),t)}:a)(r,f);e=n.createElement(T,{shapeType:o,elementProps:p})}else e=n.createElement(T,{shapeType:o,elementProps:f});return s?n.createElement(S.W,{className:void 0===c?"recharts-active-shape":c},e):e}function I(t,e){return null!=e&&"trapezoids"in t.props}function D(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function R(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function B(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function L(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function U(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,u=(I(i,o)?e="trapezoids":D(i,o)?e="sectors":N(i,o)&&(e="points"),e),c=I(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:D(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:N(i,o)?o.payload:{},l=a.filter(function(t,e){var r=f()(c,t),n=i.props[u].filter(function(t){var e;return(I(i,o)?e=R:D(i,o)?e=B:N(i,o)&&(e=L),e)(t,o)}),a=i.props[u].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},68098:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},69229:(t,e,r)=>{var n=r(67472),o=r(38406),i=r(50523),a=r(84464),u=r(94380),c=r(39608),l=r(33497),s=r(35190),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var g=c(t),b=c(e),x=g?p:u(t),w=b?p:u(e);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,A=x==w;if(A&&l(t)){if(!l(e))return!1;g=!0,O=!1}if(A&&!O)return m||(m=new n),g||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var S=O&&h.call(t,"__wrapped__"),P=j&&h.call(e,"__wrapped__");if(S||P){var E=S?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!A&&(m||(m=new n),a(t,e,r,y,v,m))}},69363:(t,e,r)=>{var n=r(57213),o=r(18028),i=r(6305),a=r(39608);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},69806:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},70688:(t,e,r)=>{"use strict";r.d(e,{I:()=>X});var n=r(12115);function o(){}function i(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function d(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function h(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-n)/3;t._context.bezierCurveTo(n+u,o+u*e,i-u,a-u*r,i,a)}function v(t){this._context=t}function m(t){this._context=new g(t)}function g(t){this._context=t}function b(t){this._context=t}function x(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function w(t,e){this._context=t,this._t=e}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,h(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,h(this,r=d(this,t,e)),r);break;default:y(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},g.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),o=x(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var O=r(9819),j=r(85654),A=r(31847);function S(t){return t[0]}function P(t){return t[1]}function E(t,e){var r=(0,j.A)(!0),n=null,o=p,i=null,a=(0,A.i)(u);function u(u){var c,l,s,f=(u=(0,O.A)(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?S:(0,j.A)(t),e="function"==typeof e?e:void 0===e?P:(0,j.A)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,j.A)(!!t),u):r},u.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),u):o},u.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),u):n},u}function k(t,e,r){var n=null,o=(0,j.A)(!0),i=null,a=p,u=null,c=(0,A.i)(l);function l(l){var s,f,p,d,h,y=(l=(0,O.A)(l)).length,v=!1,m=Array(y),g=Array(y);for(null==i&&(u=a(h=c())),s=0;s<=y;++s){if(!(s<y&&o(d=l[s],s,l))===v){if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],g[p]);u.lineEnd(),u.areaEnd()}}v&&(m[s]=+t(d,s,l),g[s]=+e(d,s,l),u.point(n?+n(d,s,l):m[s],r?+r(d,s,l):g[s]))}if(h)return u=null,h+""||null}function s(){return E().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?S:(0,j.A)(+t),e="function"==typeof e?e:void 0===e?(0,j.A)(0):(0,j.A)(+e),r="function"==typeof r?r:void 0===r?P:(0,j.A)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,j.A)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}var M=r(23633),_=r.n(M),T=r(40139),C=r.n(T),I=r(52596),D=r(43597),N=r(70788),R=r(16377);function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var z={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new b(t)},curveStep:function(t){return new w(t,.5)},curveStepAfter:function(t){return new w(t,1)},curveStepBefore:function(t){return new w(t,0)}},W=function(t){return t.x===+t.x&&t.y===+t.y},$=function(t){return t.x},H=function(t){return t.y},q=function(t,e){if(C()(t))return t;var r="curve".concat(_()(t));return("curveMonotone"===r||"curveBump"===r)&&e?z["".concat(r).concat("vertical"===e?"Y":"X")]:z[r]||p},V=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,u=t.connectNulls,c=void 0!==u&&u,l=q(void 0===r?"linear":r,a),s=c?o.filter(function(t){return W(t)}):o;if(Array.isArray(i)){var f=c?i.filter(function(t){return W(t)}):i,p=s.map(function(t,e){return F(F({},t),{},{base:f[e]})});return(e="vertical"===a?k().y(H).x1($).x0(function(t){return t.base.x}):k().x($).y1(H).y0(function(t){return t.base.y})).defined(W).curve(l),e(p)}return(e="vertical"===a&&(0,R.Et)(i)?k().y(H).x1($).x0(i):(0,R.Et)(i)?k().x($).y1(H).y0(i):E().x($).y(H)).defined(W).curve(l),e(s)},X=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?V(t):o;return n.createElement("path",L({},(0,N.J9)(t,!1),(0,D._U)(t),{className:(0,I.A)("recharts-curve",e),d:a,ref:i}))}},70771:(t,e,r)=>{var n=r(98233),o=r(48611);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},70788:(t,e,r)=>{"use strict";r.d(e,{AW:()=>R,BU:()=>E,J9:()=>T,Me:()=>k,Mn:()=>O,OV:()=>C,X_:()=>N,aS:()=>P,ee:()=>D});var n=r(48973),o=r.n(n),i=r(59882),a=r.n(i),u=r(15438),c=r.n(u),l=r(40139),s=r.n(l),f=r(67460),p=r.n(f),d=r(12115),h=r(50330),y=r(16377),v=r(15232),m=r(43597),g=["children"],b=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},O=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},j=null,A=null,S=function t(e){if(e===j&&Array.isArray(A))return A;var r=[];return d.Children.forEach(e,function(e){a()(e)||((0,h.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),A=r,j=e,r};function P(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return O(t)}):[O(e)],S(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function E(t,e){var r=P(t,e);return r&&r[0]}var k=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.Et)(r)&&!(r<=0)&&!!(0,y.Et)(n)&&!(n<=0)},M=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_=function(t,e,r,n){var o,i=null!==(o=null===m.VU||void 0===m.VU?void 0:m.VU[n])&&void 0!==o?o:[];return!s()(t)&&(n&&i.includes(e)||m.QQ.includes(e))||r&&m.j2.includes(e)},T=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,d.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;_(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},C=function t(e,r){if(e===r)return!0;var n=d.Children.count(e);if(n!==d.Children.count(r))return!1;if(0===n)return!0;if(1===n)return I(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!I(i,a))return!1}return!0},I=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,g),i=e.props||{},u=i.children,c=x(i,b);if(n&&u)return(0,v.b)(o,c)&&C(n,u);if(!n&&!u)return(0,v.b)(o,c)}return!1},D=function(t,e){var r=[],n={};return S(t).forEach(function(t,o){var i;if((i=t)&&i.type&&c()(i.type)&&M.indexOf(i.type)>=0)r.push(t);else if(t){var a=O(t.type),u=e[a]||{},l=u.handler,s=u.once;if(l&&(!s||!n[a])){var f=l(t,a,o);r.push(f),n[a]=!0}}}),r},N=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},R=function(t,e){return S(e).indexOf(t)}},70966:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},71571:(t,e,r)=>{var n=r(92313),o=r(18028),i=r(51445),a=r(39608),u=r(39641);t.exports=function(t,e,r){var c=a(t)?n:i;return r&&u(t,e,r)&&(e=void 0),c(t,o(e,3))}},72043:(t,e,r)=>{t.exports=r(70966)(Object.keys,Object)},72790:(t,e,r)=>{"use strict";r.d(e,{u:()=>c});var n=r(12115),o=r(52596),i=r(70788),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function c(t){var e=t.children,r=t.width,c=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,d=t.desc,h=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),y=l||{width:r,height:c,x:0,y:0},v=(0,o.A)("recharts-surface",s);return n.createElement("svg",u({},(0,i.J9)(h,!0,"svg"),{className:v,width:r,height:c,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),n.createElement("title",null,p),n.createElement("desc",null,d),e)}},72948:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},73726:(t,e,r)=>{t.exports=r(70966)(Object.getPrototypeOf,Object)},73800:(t,e,r)=>{var n=r(39608),o=r(79595),i=r(37835),a=r(85855);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},73956:(t,e,r)=>{var n=r(5658);t.exports=function(t){return n(this,t).has(t)}},74166:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},74366:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,u=r(e((n-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},74544:(t,e,r)=>{var n=r(5658);t.exports=function(t){return n(this,t).get(t)}},74601:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},74888:(t,e,r)=>{var n=r(53516);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},74925:(t,e,r)=>{var n=r(28897),o=r(20480),i=r(18028);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},75031:(t,e,r)=>{var n=r(18028),o=r(22471),i=r(35095);t.exports=function(t){return function(e,r,a){var u=Object(e);if(!o(e)){var c=n(r,3);e=i(e),r=function(t){return c(u[t],t,u)}}var l=t(e,r,a);return l>-1?u[c?e[l]:l]:void 0}}},75145:(t,e,r)=>{var n=r(50851),o=r(65531),i=r(17855);t.exports=function(t){return o(t)?i(t):n(t)}},75899:(t,e,r)=>{t.exports=r(83711)(Object,"create")},76685:(t,e,r)=>{var n=r(82500);t.exports=function(){return n.Date.now()}},76957:(t,e,r)=>{t.exports=r(82500).Uint8Array},77223:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},77283:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(12115),o=r(52596),i=r(70788),a=r(25641),u=r(16377);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(u?1:-1)+n,f=Math.asin(c/s)/a.Kg,p=l?o:o+i*f;return{center:(0,a.IZ)(e,r,s,p),circleTangency:(0,a.IZ)(e,r,n,p),lineTangency:(0,a.IZ)(e,r,s*Math.cos(f*a.Kg),l?o-i*f:o),theta:f}},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,c=t.endAngle,l=(0,u.sA)(c-i)*Math.min(Math.abs(c-i),359.999),s=i+l,f=(0,a.IZ)(e,r,o,i),p=(0,a.IZ)(e,r,o,s),d="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(n>0){var h=(0,a.IZ)(e,r,n,i),y=(0,a.IZ)(e,r,n,s);d+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=s),",\n            ").concat(h.x,",").concat(h.y," Z")}else d+="L ".concat(e,",").concat(r," Z");return d},h=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,u.sA)(s-l),h=p({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:c}),y=h.circleTangency,v=h.lineTangency,m=h.theta,g=p({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:c}),b=g.circleTangency,x=g.lineTangency,w=g.theta,O=c?Math.abs(l-s):Math.abs(l-s)-m-w;if(O<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):d({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(O>180),",").concat(+(f<0),",").concat(b.x,",").concat(b.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var A=p({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),S=A.circleTangency,P=A.lineTangency,E=A.theta,k=p({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),M=k.circleTangency,_=k.lineTangency,T=k.theta,C=c?Math.abs(l-s):Math.abs(l-s)-E-T;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(_.x,",").concat(_.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(S.x,",").concat(S.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,r=f(f({},y),t),a=r.cx,c=r.cy,s=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,g=r.cornerIsExternal,b=r.startAngle,x=r.endAngle,w=r.className;if(p<s||b===x)return null;var O=(0,o.A)("recharts-sector",w),j=p-s,A=(0,u.F4)(v,j,0,!0);return e=A>0&&360>Math.abs(b-x)?h({cx:a,cy:c,innerRadius:s,outerRadius:p,cornerRadius:Math.min(A,j/2),forceCornerRadius:m,cornerIsExternal:g,startAngle:b,endAngle:x}):d({cx:a,cy:c,innerRadius:s,outerRadius:p,startAngle:b,endAngle:x}),n.createElement("path",l({},(0,i.J9)(r,!0),{className:O,d:e,role:"img"}))}},77381:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},77969:(t,e,r)=>{var n=r(91569),o=r(36314);t.exports=function t(e,r,i,a,u){var c=-1,l=e.length;for(i||(i=o),u||(u=[]);++c<l;){var s=e[c];r>0&&i(s)?r>1?t(s,r-1,i,a,u):n(u,s):a||(u[u.length]=s)}return u}},79095:(t,e,r)=>{"use strict";r.d(e,{E:()=>B});var n=r(12115),o=r(59882),i=r.n(o),a=r(52596),u=r(16377),c=r(41643),l=r(70788),s=r(46605);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,b={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(b),w=function(){var t,e;function r(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*b[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=p(null!==(e=g.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&h(r.prototype,t),e&&h(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function O(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=y.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=w.parse(null!=o?o:""),c=w.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";e=e.replace(y,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=v.exec(e))&&void 0!==s?s:[],4),d=f[1],h=f[2],m=f[3],g=w.parse(null!=d?d:""),b=w.parse(null!=m?m:""),x="+"===h?g.add(b):g.subtract(b);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var j=/\(([^()]*)\)/;function A(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=p(j.exec(e),2)[1];e=e.replace(j,O(r))}return e}(e),e=O(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var S=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],P=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var T=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];i()(e)||(o=r?e.toString().split(""):e.toString().split(T));var a=o.map(function(t){return{word:t,width:(0,s.Pu)(t,n).width}}),u=r?0:(0,s.Pu)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(t){return null}},I=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,l=t.style,s=t.breakAll,f=(0,u.Et)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];return u&&(null==n||o||u.width+a+r<Number(n))?(u.words.push(i),u.width+=a+r):t.push({words:[i],width:a}),t},[])},d=p(e);if(!f)return d;for(var h=function(t){var e=p(C({breakAll:s,style:l,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var g=Math.floor((y+v)/2),b=M(h(g-1),2),x=b[0],w=b[1],O=M(h(g),1)[0];if(x||O||(y=g+1),x&&O&&(v=g-1),!x&&O){i=w;break}m++}return i||d},D=function(t){return[{words:i()(t)?[]:t.toString().split(T)}]},N=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!c.m.isSsr){var u=C({breakAll:i,children:n,style:o});if(!u)return D(n);var l=u.wordsWithComputedWidth,s=u.spaceWidth;return I({breakAll:i,children:n,maxLines:a,style:o},l,s,e,r)}return D(n)},R="#808080",B=function(t){var e,r=t.x,o=void 0===r?0:r,i=t.y,c=void 0===i?0:i,s=t.lineHeight,f=void 0===s?"1em":s,p=t.capHeight,d=void 0===p?"0.71em":p,h=t.scaleToFit,y=void 0!==h&&h,v=t.textAnchor,m=t.verticalAnchor,g=t.fill,b=void 0===g?R:g,x=k(t,S),w=(0,n.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),O=x.dx,j=x.dy,M=x.angle,_=x.className,T=x.breakAll,C=k(x,P);if(!(0,u.vh)(o)||!(0,u.vh)(c))return null;var I=o+((0,u.Et)(O)?O:0),D=c+((0,u.Et)(j)?j:0);switch(void 0===m?"end":m){case"start":e=A("calc(".concat(d,")"));break;case"middle":e=A("calc(".concat((w.length-1)/2," * -").concat(f," + (").concat(d," / 2))"));break;default:e=A("calc(".concat(w.length-1," * -").concat(f,")"))}var B=[];if(y){var L=w[0].width,U=x.width;B.push("scale(".concat(((0,u.Et)(U)?U/L:1)/L,")"))}return M&&B.push("rotate(".concat(M,", ").concat(I,", ").concat(D,")")),B.length&&(C.transform=B.join(" ")),n.createElement("text",E({},(0,l.J9)(C,!0),{x:I,y:D,className:(0,a.A)("recharts-text",_),textAnchor:void 0===v?"start":v,fill:b.includes("url")?R:b}),w.map(function(t,r){var o=t.words.join(T?"":" ");return n.createElement("tspan",{x:I,dy:0===r?e:f,key:"".concat(o,"-").concat(r)},o)}))}},79133:(t,e,r)=>{"use strict";r.d(e,{F:()=>Z});var n=r(12115),o=r(9557),i=r(48973),a=r.n(i),u=r(60245),c=r.n(u),l=r(59882),s=r.n(l),f=r(40139),p=r.n(f),d=r(52596),h=r(2348),y=r(70688),v=r(79095),m=r(60379),g=r(67460),b=r.n(g),x=r(83979),w=r.n(x),O=r(70788),j=r(49754);function A(t){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var S=["valueAccessor"],P=["data","dataKey","clockWise","id","textBreakAll"];function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function T(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var C=function(t){return Array.isArray(t.value)?w()(t.value):t.value};function I(t){var e=t.valueAccessor,r=void 0===e?C:e,o=T(t,S),i=o.data,a=o.dataKey,u=o.clockWise,c=o.id,l=o.textBreakAll,f=T(o,P);return i&&i.length?n.createElement(h.W,{className:"recharts-label-list"},i.map(function(t,e){var o=s()(a)?r(t,e):(0,j.kr)(t&&t.payload,a),i=s()(c)?{}:{id:"".concat(c,"-").concat(e)};return n.createElement(m.J,k({},(0,O.J9)(t,!0),f,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:l,viewBox:m.J.parseViewBox(s()(u)?t:_(_({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}I.displayName="LabelList",I.renderCallByParent=function(t,e){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=(0,O.aS)(i,I).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return o?[(r=t.label)?!0===r?n.createElement(I,{key:"labelList-implicit",data:e}):n.isValidElement(r)||p()(r)?n.createElement(I,{key:"labelList-implicit",data:e,content:r}):b()(r)?n.createElement(I,k({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return E(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return E(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return E(t,e)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a};var D=r(54811),N=r(41643),R=r(25641),B=r(16377),L=r(675),U=r(43597),F=r(67790);function z(t){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function W(){return(W=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function $(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function H(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$(Object(r),!0).forEach(function(e){K(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function q(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Y(n.key),n)}}function V(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(V=function(){return!!t})()}function X(t){return(X=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function G(t,e){return(G=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function K(t,e,r){return(e=Y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Y(t){var e=function(t,e){if("object"!=z(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=z(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==z(e)?e:e+""}var Z=function(t){var e,r;function i(t){var e,r,n;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),r=i,n=[t],r=X(r),K(e=function(t,e){if(e&&("object"===z(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,V()?Reflect.construct(r,n||[],X(this).constructor):r.apply(this,n)),"pieRef",null),K(e,"sectorRefs",[]),K(e,"id",(0,B.NF)("recharts-pie-")),K(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),p()(t)&&t()}),K(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),p()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&G(t,e)}(i,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,r=e.label,o=e.labelLine,a=e.dataKey,u=e.valueKey,c=(0,O.J9)(this.props,!1),l=(0,O.J9)(r,!1),f=(0,O.J9)(o,!1),p=r&&r.offsetRadius||20,d=t.map(function(t,e){var d=(t.startAngle+t.endAngle)/2,y=(0,R.IZ)(t.cx,t.cy,t.outerRadius+p,d),v=H(H(H(H({},c),t),{},{stroke:"none"},l),{},{index:e,textAnchor:i.getTextAnchor(y.x,t.cx)},y),m=H(H(H(H({},c),t),{},{fill:"none",stroke:t.fill},f),{},{index:e,points:[(0,R.IZ)(t.cx,t.cy,t.outerRadius,d),y]}),g=a;return s()(a)&&s()(u)?g="value":s()(a)&&(g=u),n.createElement(h.W,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&i.renderLabelLineItem(o,m,"line"),i.renderLabelItem(r,v,(0,j.kr)(t,g)))});return n.createElement(h.W,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,o=r.activeShape,i=r.blendStroke,a=r.inactiveShape;return t.map(function(r,u){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==t.length)return null;var c=e.isActiveIndex(u),l=a&&e.hasActiveIndex()?a:null,s=H(H({},r),{},{stroke:i?r.fill:r.stroke,tabIndex:-1});return n.createElement(h.W,W({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},(0,U.XC)(e.props,r,u),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(u)}),n.createElement(F.yp,W({option:c?o:l,isActive:c,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,i=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,l=e.animationEasing,s=e.animationId,f=this.state,p=f.prevSectors,d=f.prevIsAnimationActive;return n.createElement(o.Ay,{begin:u,duration:c,isActive:i,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var o=e.t,i=[],u=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=p&&p[e],n=e>0?a()(t,"paddingAngle",0):0;if(r){var c=(0,B.Dj)(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=H(H({},t),{},{startAngle:u+n,endAngle:u+c(o)+n});i.push(l),u=l.endAngle}else{var s=t.endAngle,f=t.startAngle,d=(0,B.Dj)(0,s-f)(o),h=H(H({},t),{},{startAngle:u+n,endAngle:u+d+n});i.push(h),u=h.endAngle}}),n.createElement(h.W,null,t.renderSectorsStatically(i))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return r&&e&&e.length&&(!n||!c()(n,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,o=e.sectors,i=e.className,a=e.label,u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!o||!o.length||!(0,B.Et)(u)||!(0,B.Et)(c)||!(0,B.Et)(l)||!(0,B.Et)(s))return null;var y=(0,d.A)("recharts-pie",i);return n.createElement(h.W,{tabIndex:this.props.rootTabIndex,className:y,ref:function(e){t.pieRef=e}},this.renderSectors(),a&&this.renderLabels(o),m.J.renderCallByParent(this.props,null,!1),(!f||p)&&I.renderCallByParent(this.props,o,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(n.isValidElement(t))return n.cloneElement(t,e);if(p()(t))return t(e);var o=(0,d.A)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return n.createElement(y.I,W({},e,{key:r,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(t,e,r){if(n.isValidElement(t))return n.cloneElement(t,e);var o=r;if(p()(t)&&(o=t(e),n.isValidElement(o)))return o;var i=(0,d.A)("recharts-pie-label-text","boolean"==typeof t||p()(t)?"":t.className);return n.createElement(v.E,W({},e,{alignmentBaseline:"middle",className:i}),o)}}],e&&q(i.prototype,e),r&&q(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);K(Z,"displayName","Pie"),K(Z,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!N.m.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),K(Z,"parseDeltaAngle",function(t,e){return(0,B.sA)(e-t)*Math.min(Math.abs(e-t),360)}),K(Z,"getRealPieData",function(t){var e=t.data,r=t.children,n=(0,O.J9)(t,!1),o=(0,O.aS)(r,D.f);return e&&e.length?e.map(function(t,e){return H(H(H({payload:t},n),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return H(H({},n),t.props)}):[]}),K(Z,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=(0,R.lY)(o,i);return{cx:n+(0,B.F4)(t.cx,o,o/2),cy:r+(0,B.F4)(t.cy,i,i/2),innerRadius:(0,B.F4)(t.innerRadius,a,0),outerRadius:(0,B.F4)(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),K(Z,"getComposedData",function(t){var e,r,n=t.item,o=t.offset,i=void 0!==n.type.defaultProps?H(H({},n.type.defaultProps),n.props):n.props,a=Z.getRealPieData(i);if(!a||!a.length)return null;var u=i.cornerRadius,c=i.startAngle,l=i.endAngle,f=i.paddingAngle,p=i.dataKey,d=i.nameKey,h=i.valueKey,y=i.tooltipType,v=Math.abs(i.minAngle),m=Z.parseCoordinateOfPie(i,o),g=Z.parseDeltaAngle(c,l),b=Math.abs(g),x=p;s()(p)&&s()(h)?((0,L.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x="value"):s()(p)&&((0,L.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x=h);var w=a.filter(function(t){return 0!==(0,j.kr)(t,x,0)}).length,O=b-w*v-(b>=360?w:w-1)*f,A=a.reduce(function(t,e){var r=(0,j.kr)(e,x,0);return t+((0,B.Et)(r)?r:0)},0);return A>0&&(e=a.map(function(t,e){var n,o=(0,j.kr)(t,x,0),i=(0,j.kr)(t,d,e),a=((0,B.Et)(o)?o:0)/A,l=(n=e?r.endAngle+(0,B.sA)(g)*f*+(0!==o):c)+(0,B.sA)(g)*((0!==o?v:0)+a*O),s=(n+l)/2,p=(m.innerRadius+m.outerRadius)/2,h=[{name:i,value:o,payload:t,dataKey:x,type:y}],b=(0,R.IZ)(m.cx,m.cy,p,s);return r=H(H(H({percent:a,cornerRadius:u,name:i,tooltipPayload:h,midAngle:s,middleRadius:p,tooltipPosition:b},t),m),{},{value:(0,j.kr)(t,x),startAngle:n,endAngle:l,payload:t,paddingAngle:(0,B.sA)(g)*f})})),H(H({},m),{},{sectors:e,data:a})})},79399:(t,e,r)=>{"use strict";var n=r(72948);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},79401:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},79556:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79595:(t,e,r)=>{var n=r(39608),o=r(70771),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},80659:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},81203:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},81519:(t,e,r)=>{"use strict";r.d(e,{A:()=>i,z:()=>a});var n=r(28749),o=r(95442);function i(){var t,e,r=(0,o.A)().unknown(void 0),a=r.domain,u=r.range,c=0,l=1,s=!1,f=0,p=0,d=.5;function h(){var r=a().length,n=l<c,o=n?l:c,i=n?c:l;t=(i-o)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*d,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var h=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return o+t*e});return u(n?h.reverse():h)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),h()):a()},r.range=function(t){return arguments.length?([c,l]=t,c*=1,l*=1,h()):[c,l]},r.rangeRound=function(t){return[c,l]=t,c*=1,l*=1,s=!0,h()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,h()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),h()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),h()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,h()):p},r.align=function(t){return arguments.length?(d=Math.max(0,Math.min(1,t)),h()):d},r.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(d)},n.C.apply(h(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(i.apply(null,arguments).paddingInner(1))}},82500:(t,e,r)=>{var n=r(7985),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},82596:(t,e,r)=>{var n=r(38008),o=r(31431),i=r(74166);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},82661:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,d=s.length;for(l=0;l<d;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},82954:t=>{t.exports=function(t,e){return t.has(e)}},83134:(t,e,r)=>{var n=r(58918),o=r(21582),i=r(18028);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},83197:(t,e,r)=>{"use strict";r.d(e,{g:()=>l});var n=r(24026),o=r(49754),i=r(70788);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,l=t.legendContent,s=(0,i.BU)(r,n.s);if(!s)return null;var f=n.s.defaultProps,p=void 0!==f?c(c({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?c(c({},r),e.props):{},i=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.Ps)(e),value:a||i,payload:n}}),c(c(c({},p),n.s.getWithHeight(s,u)),{},{payload:e,item:s})}},83540:(t,e,r)=>{"use strict";r.d(e,{u:()=>h});var n=r(52596),o=r(12115),i=r(91959),a=r.n(i),u=r(16377),c=r(675),l=r(70788);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var h=(0,o.forwardRef)(function(t,e){var r,i=t.aspect,s=t.initialDimension,f=void 0===s?{width:-1,height:-1}:s,h=t.width,y=void 0===h?"100%":h,v=t.height,m=void 0===v?"100%":v,g=t.minWidth,b=void 0===g?0:g,x=t.minHeight,w=t.maxHeight,O=t.children,j=t.debounce,A=void 0===j?0:j,S=t.id,P=t.className,E=t.onResize,k=t.style,M=(0,o.useRef)(null),_=(0,o.useRef)();_.current=E,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var T=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:f.width,containerHeight:f.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(r,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=T[0],I=T[1],D=(0,o.useCallback)(function(t,e){I(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;D(n,o),null===(e=_.current)||void 0===e||e.call(_,n,o)};A>0&&(t=a()(t,A,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=M.current.getBoundingClientRect();return D(r.width,r.height),e.observe(M.current),function(){e.disconnect()}},[D,A]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,c.R)((0,u._3)(y)||(0,u._3)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,c.R)(!i||i>0,"The aspect(%s) must be greater than zero.",i);var r=(0,u._3)(y)?t:y,n=(0,u._3)(m)?e:m;i&&i>0&&(r?n=r/i:n&&(r=n*i),w&&n>w&&(n=w)),(0,c.R)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,y,m,b,x,i);var a=!Array.isArray(O)&&(0,l.Mn)(O.type).endsWith("Chart");return o.Children.map(O,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:r,height:n},a?{style:p({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[i,O,m,w,x,b,C,y]);return o.createElement("div",{id:S?"".concat(S):void 0,className:(0,n.A)("recharts-responsive-container",P),style:p(p({},void 0===k?{}:k),{},{width:y,height:m,minWidth:b,minHeight:x,maxHeight:w}),ref:M},N)})},83711:(t,e,r)=>{var n=r(13122),o=r(15473);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},83979:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},84464:(t,e,r)=>{var n=r(20963),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,u){var c=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in e:o.call(e,p)))return!1}var d=u.get(t),h=u.get(e);if(d&&h)return d==e&&h==t;var y=!0;u.set(t,e),u.set(e,t);for(var v=c;++f<s;){var m=t[p=l[f]],g=e[p];if(i)var b=c?i(g,m,p,e,t,u):i(m,g,p,t,e,u);if(!(void 0===b?m===g||a(m,g,r,i,u):b)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return u.delete(t),u.delete(e),y}},84760:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85090:(t,e,r)=>{var n=r(88748),o=r(6997),i=r(34210);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},85268:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},85654:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},85855:(t,e,r)=>{var n=r(16613);t.exports=function(t){return null==t?"":n(t)}},86216:(t,e,r)=>{t.exports=r(30716)()},86452:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},88390:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},88748:(t,e,r)=>{var n=r(10537),o=r(94999),i=r(74544),a=r(73956),u=r(5516);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},89053:(t,e,r)=>{var n=r(58918),o=r(52521),i=r(13465);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},89316:(t,e,r)=>{var n=r(98233),o=r(15631),i=r(48611),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},89829:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},90453:(t,e,r)=>{var n=r(31598);t.exports=function(){this.__data__=new n,this.size=0}},90724:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},90929:(t,e,r)=>{var n=r(54360),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},91113:(t,e,r)=>{var n=r(88748);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},91467:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(40157).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},91569:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},91959:(t,e,r)=>{var n=r(45964),o=r(67460);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},92313:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},92972:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},93081:(t,e,r)=>{"use strict";r.d(e,{UC:()=>tI,YJ:()=>tN,In:()=>tT,q7:()=>tB,VF:()=>tU,p4:()=>tL,JU:()=>tR,ZL:()=>tC,bL:()=>tk,wn:()=>tz,PP:()=>tF,wv:()=>tW,l9:()=>tM,WT:()=>t_,LM:()=>tD});var n=r(12115),o=r(47650);function i(t,[e,r]){return Math.min(r,Math.max(e,t))}var a=r(85185),u=r(82284),c=r(6101),l=r(46081),s=r(94315),f=r(19178),p=r(92293),d=r(25519),h=r(61285),y=r(38795),v=r(34378),m=r(63655),g=r(99708),b=r(39033),x=r(5845),w=r(52712),O=r(2564),j=r(38168),A=r(31114),S=r(95155),P=[" ","Enter","ArrowUp","ArrowDown"],E=[" ","Enter"],k="Select",[M,_,T]=(0,u.N)(k),[C,I]=(0,l.A)(k,[T,y.Bk]),D=(0,y.Bk)(),[N,R]=C(k),[B,L]=C(k),U=t=>{let{__scopeSelect:e,children:r,open:o,defaultOpen:i,onOpenChange:a,value:u,defaultValue:c,onValueChange:l,dir:f,name:p,autoComplete:d,disabled:v,required:m,form:g}=t,b=D(e),[w,O]=n.useState(null),[j,A]=n.useState(null),[P,E]=n.useState(!1),k=(0,s.jH)(f),[_=!1,T]=(0,x.i)({prop:o,defaultProp:i,onChange:a}),[C,I]=(0,x.i)({prop:u,defaultProp:c,onChange:l}),R=n.useRef(null),L=!w||g||!!w.closest("form"),[U,F]=n.useState(new Set),z=Array.from(U).map(t=>t.props.value).join(";");return(0,S.jsx)(y.bL,{...b,children:(0,S.jsxs)(N,{required:m,scope:e,trigger:w,onTriggerChange:O,valueNode:j,onValueNodeChange:A,valueNodeHasChildren:P,onValueNodeHasChildrenChange:E,contentId:(0,h.B)(),value:C,onValueChange:I,open:_,onOpenChange:T,dir:k,triggerPointerDownPosRef:R,disabled:v,children:[(0,S.jsx)(M.Provider,{scope:e,children:(0,S.jsx)(B,{scope:t.__scopeSelect,onNativeOptionAdd:n.useCallback(t=>{F(e=>new Set(e).add(t))},[]),onNativeOptionRemove:n.useCallback(t=>{F(e=>{let r=new Set(e);return r.delete(t),r})},[]),children:r})}),L?(0,S.jsxs)(tS,{"aria-hidden":!0,required:m,tabIndex:-1,name:p,autoComplete:d,value:C,onChange:t=>I(t.target.value),disabled:v,form:g,children:[void 0===C?(0,S.jsx)("option",{value:""}):null,Array.from(U)]},z):null]})})};U.displayName=k;var F="SelectTrigger",z=n.forwardRef((t,e)=>{let{__scopeSelect:r,disabled:o=!1,...i}=t,u=D(r),l=R(F,r),s=l.disabled||o,f=(0,c.s)(e,l.onTriggerChange),p=_(r),d=n.useRef("touch"),[h,v,g]=tP(t=>{let e=p().filter(t=>!t.disabled),r=e.find(t=>t.value===l.value),n=tE(e,t,r);void 0!==n&&l.onValueChange(n.value)}),b=t=>{s||(l.onOpenChange(!0),g()),t&&(l.triggerPointerDownPosRef.current={x:Math.round(t.pageX),y:Math.round(t.pageY)})};return(0,S.jsx)(y.Mz,{asChild:!0,...u,children:(0,S.jsx)(m.sG.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":tA(l.value)?"":void 0,...i,ref:f,onClick:(0,a.m)(i.onClick,t=>{t.currentTarget.focus(),"mouse"!==d.current&&b(t)}),onPointerDown:(0,a.m)(i.onPointerDown,t=>{d.current=t.pointerType;let e=t.target;e.hasPointerCapture(t.pointerId)&&e.releasePointerCapture(t.pointerId),0===t.button&&!1===t.ctrlKey&&"mouse"===t.pointerType&&(b(t),t.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,t=>{let e=""!==h.current;t.ctrlKey||t.altKey||t.metaKey||1!==t.key.length||v(t.key),(!e||" "!==t.key)&&P.includes(t.key)&&(b(),t.preventDefault())})})})});z.displayName=F;var W="SelectValue",$=n.forwardRef((t,e)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:a="",...u}=t,l=R(W,r),{onValueNodeHasChildrenChange:s}=l,f=void 0!==i,p=(0,c.s)(e,l.onValueNodeChange);return(0,w.N)(()=>{s(f)},[s,f]),(0,S.jsx)(m.sG.span,{...u,ref:p,style:{pointerEvents:"none"},children:tA(l.value)?(0,S.jsx)(S.Fragment,{children:a}):i})});$.displayName=W;var H=n.forwardRef((t,e)=>{let{__scopeSelect:r,children:n,...o}=t;return(0,S.jsx)(m.sG.span,{"aria-hidden":!0,...o,ref:e,children:n||"▼"})});H.displayName="SelectIcon";var q=t=>(0,S.jsx)(v.Z,{asChild:!0,...t});q.displayName="SelectPortal";var V="SelectContent",X=n.forwardRef((t,e)=>{let r=R(V,t.__scopeSelect),[i,a]=n.useState();return((0,w.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,S.jsx)(Y,{...t,ref:e}):i?o.createPortal((0,S.jsx)(G,{scope:t.__scopeSelect,children:(0,S.jsx)(M.Slot,{scope:t.__scopeSelect,children:(0,S.jsx)("div",{children:t.children})})}),i):null});X.displayName=V;var[G,K]=C(V),Y=n.forwardRef((t,e)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:u,onPointerDownOutside:l,side:s,sideOffset:h,align:y,alignOffset:v,arrowPadding:m,collisionBoundary:b,collisionPadding:x,sticky:w,hideWhenDetached:O,avoidCollisions:P,...E}=t,k=R(V,r),[M,T]=n.useState(null),[C,I]=n.useState(null),D=(0,c.s)(e,t=>T(t)),[N,B]=n.useState(null),[L,U]=n.useState(null),F=_(r),[z,W]=n.useState(!1),$=n.useRef(!1);n.useEffect(()=>{if(M)return(0,j.Eq)(M)},[M]),(0,p.Oh)();let H=n.useCallback(t=>{let[e,...r]=F().map(t=>t.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of t)if(r===o||(null==r||r.scrollIntoView({block:"nearest"}),r===e&&C&&(C.scrollTop=0),r===n&&C&&(C.scrollTop=C.scrollHeight),null==r||r.focus(),document.activeElement!==o))return},[F,C]),q=n.useCallback(()=>H([N,M]),[H,N,M]);n.useEffect(()=>{z&&q()},[z,q]);let{onOpenChange:X,triggerPointerDownPosRef:K}=k;n.useEffect(()=>{if(M){let t={x:0,y:0},e=e=>{var r,n,o,i;t={x:Math.abs(Math.round(e.pageX)-(null!==(o=null===(r=K.current)||void 0===r?void 0:r.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(e.pageY)-(null!==(i=null===(n=K.current)||void 0===n?void 0:n.y)&&void 0!==i?i:0))}},r=r=>{t.x<=10&&t.y<=10?r.preventDefault():M.contains(r.target)||X(!1),document.removeEventListener("pointermove",e),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",e),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",e),document.removeEventListener("pointerup",r,{capture:!0})}}},[M,X,K]),n.useEffect(()=>{let t=()=>X(!1);return window.addEventListener("blur",t),window.addEventListener("resize",t),()=>{window.removeEventListener("blur",t),window.removeEventListener("resize",t)}},[X]);let[Y,Q]=tP(t=>{let e=F().filter(t=>!t.disabled),r=e.find(t=>t.ref.current===document.activeElement),n=tE(e,t,r);n&&setTimeout(()=>n.ref.current.focus())}),tt=n.useCallback((t,e,r)=>{let n=!$.current&&!r;(void 0!==k.value&&k.value===e||n)&&(B(t),n&&($.current=!0))},[k.value]),te=n.useCallback(()=>null==M?void 0:M.focus(),[M]),tr=n.useCallback((t,e,r)=>{let n=!$.current&&!r;(void 0!==k.value&&k.value===e||n)&&U(t)},[k.value]),tn="popper"===o?J:Z,to=tn===J?{side:s,sideOffset:h,align:y,alignOffset:v,arrowPadding:m,collisionBoundary:b,collisionPadding:x,sticky:w,hideWhenDetached:O,avoidCollisions:P}:{};return(0,S.jsx)(G,{scope:r,content:M,viewport:C,onViewportChange:I,itemRefCallback:tt,selectedItem:N,onItemLeave:te,itemTextRefCallback:tr,focusSelectedItem:q,selectedItemText:L,position:o,isPositioned:z,searchRef:Y,children:(0,S.jsx)(A.A,{as:g.DX,allowPinchZoom:!0,children:(0,S.jsx)(d.n,{asChild:!0,trapped:k.open,onMountAutoFocus:t=>{t.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,t=>{var e;null===(e=k.trigger)||void 0===e||e.focus({preventScroll:!0}),t.preventDefault()}),children:(0,S.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:u,onPointerDownOutside:l,onFocusOutside:t=>t.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,S.jsx)(tn,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:t=>t.preventDefault(),...E,...to,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,a.m)(E.onKeyDown,t=>{let e=t.ctrlKey||t.altKey||t.metaKey;if("Tab"===t.key&&t.preventDefault(),e||1!==t.key.length||Q(t.key),["ArrowUp","ArrowDown","Home","End"].includes(t.key)){let e=F().filter(t=>!t.disabled).map(t=>t.ref.current);if(["ArrowUp","End"].includes(t.key)&&(e=e.slice().reverse()),["ArrowUp","ArrowDown"].includes(t.key)){let r=t.target,n=e.indexOf(r);e=e.slice(n+1)}setTimeout(()=>H(e)),t.preventDefault()}})})})})})})});Y.displayName="SelectContentImpl";var Z=n.forwardRef((t,e)=>{let{__scopeSelect:r,onPlaced:o,...a}=t,u=R(V,r),l=K(V,r),[s,f]=n.useState(null),[p,d]=n.useState(null),h=(0,c.s)(e,t=>d(t)),y=_(r),v=n.useRef(!1),g=n.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:O,focusSelectedItem:j}=l,A=n.useCallback(()=>{if(u.trigger&&u.valueNode&&s&&p&&b&&x&&O){let t=u.trigger.getBoundingClientRect(),e=p.getBoundingClientRect(),r=u.valueNode.getBoundingClientRect(),n=O.getBoundingClientRect();if("rtl"!==u.dir){let o=n.left-e.left,a=r.left-o,u=t.left-a,c=t.width+u,l=Math.max(c,e.width),f=i(a,[10,Math.max(10,window.innerWidth-10-l)]);s.style.minWidth=c+"px",s.style.left=f+"px"}else{let o=e.right-n.right,a=window.innerWidth-r.right-o,u=window.innerWidth-t.right-a,c=t.width+u,l=Math.max(c,e.width),f=i(a,[10,Math.max(10,window.innerWidth-10-l)]);s.style.minWidth=c+"px",s.style.right=f+"px"}let a=y(),c=window.innerHeight-20,l=b.scrollHeight,f=window.getComputedStyle(p),d=parseInt(f.borderTopWidth,10),h=parseInt(f.paddingTop,10),m=parseInt(f.borderBottomWidth,10),g=d+h+l+parseInt(f.paddingBottom,10)+m,w=Math.min(5*x.offsetHeight,g),j=window.getComputedStyle(b),A=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),P=t.top+t.height/2-10,E=x.offsetHeight/2,k=d+h+(x.offsetTop+E);if(k<=P){let t=a.length>0&&x===a[a.length-1].ref.current;s.style.bottom="0px";let e=Math.max(c-P,E+(t?S:0)+(p.clientHeight-b.offsetTop-b.offsetHeight)+m);s.style.height=k+e+"px"}else{let t=a.length>0&&x===a[0].ref.current;s.style.top="0px";let e=Math.max(P,d+b.offsetTop+(t?A:0)+E);s.style.height=e+(g-k)+"px",b.scrollTop=k-P+b.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=w+"px",s.style.maxHeight=c+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[y,u.trigger,u.valueNode,s,p,b,x,O,u.dir,o]);(0,w.N)(()=>A(),[A]);let[P,E]=n.useState();(0,w.N)(()=>{p&&E(window.getComputedStyle(p).zIndex)},[p]);let k=n.useCallback(t=>{t&&!0===g.current&&(A(),null==j||j(),g.current=!1)},[A,j]);return(0,S.jsx)(Q,{scope:r,contentWrapper:s,shouldExpandOnScrollRef:v,onScrollButtonChange:k,children:(0,S.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:(0,S.jsx)(m.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Z.displayName="SelectItemAlignedPosition";var J=n.forwardRef((t,e)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=t,a=D(r);return(0,S.jsx)(y.UC,{...a,...i,ref:e,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="SelectPopperPosition";var[Q,tt]=C(V,{}),te="SelectViewport",tr=n.forwardRef((t,e)=>{let{__scopeSelect:r,nonce:o,...i}=t,u=K(te,r),l=tt(te,r),s=(0,c.s)(e,u.onViewportChange),f=n.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(M.Slot,{scope:r,children:(0,S.jsx)(m.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,t=>{let e=t.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=l;if((null==n?void 0:n.current)&&r){let t=Math.abs(f.current-e.scrollTop);if(t>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+t,a=Math.min(n,i),u=i-a;r.style.height=a+"px","0px"===r.style.bottom&&(e.scrollTop=u>0?u:0,r.style.justifyContent="flex-end")}}}f.current=e.scrollTop})})})]})});tr.displayName=te;var tn="SelectGroup",[to,ti]=C(tn),ta=n.forwardRef((t,e)=>{let{__scopeSelect:r,...n}=t,o=(0,h.B)();return(0,S.jsx)(to,{scope:r,id:o,children:(0,S.jsx)(m.sG.div,{role:"group","aria-labelledby":o,...n,ref:e})})});ta.displayName=tn;var tu="SelectLabel",tc=n.forwardRef((t,e)=>{let{__scopeSelect:r,...n}=t,o=ti(tu,r);return(0,S.jsx)(m.sG.div,{id:o.id,...n,ref:e})});tc.displayName=tu;var tl="SelectItem",[ts,tf]=C(tl),tp=n.forwardRef((t,e)=>{let{__scopeSelect:r,value:o,disabled:i=!1,textValue:u,...l}=t,s=R(tl,r),f=K(tl,r),p=s.value===o,[d,y]=n.useState(null!=u?u:""),[v,g]=n.useState(!1),b=(0,c.s)(e,t=>{var e;return null===(e=f.itemRefCallback)||void 0===e?void 0:e.call(f,t,o,i)}),x=(0,h.B)(),w=n.useRef("touch"),O=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ts,{scope:r,value:o,disabled:i,textId:x,isSelected:p,onItemTextChange:n.useCallback(t=>{y(e=>{var r;return e||(null!==(r=null==t?void 0:t.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,S.jsx)(M.ItemSlot,{scope:r,value:o,disabled:i,textValue:d,children:(0,S.jsx)(m.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":p&&v,"data-state":p?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...l,ref:b,onFocus:(0,a.m)(l.onFocus,()=>g(!0)),onBlur:(0,a.m)(l.onBlur,()=>g(!1)),onClick:(0,a.m)(l.onClick,()=>{"mouse"!==w.current&&O()}),onPointerUp:(0,a.m)(l.onPointerUp,()=>{"mouse"===w.current&&O()}),onPointerDown:(0,a.m)(l.onPointerDown,t=>{w.current=t.pointerType}),onPointerMove:(0,a.m)(l.onPointerMove,t=>{if(w.current=t.pointerType,i){var e;null===(e=f.onItemLeave)||void 0===e||e.call(f)}else"mouse"===w.current&&t.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(l.onPointerLeave,t=>{if(t.currentTarget===document.activeElement){var e;null===(e=f.onItemLeave)||void 0===e||e.call(f)}}),onKeyDown:(0,a.m)(l.onKeyDown,t=>{var e;((null===(e=f.searchRef)||void 0===e?void 0:e.current)===""||" "!==t.key)&&(E.includes(t.key)&&O()," "===t.key&&t.preventDefault())})})})})});tp.displayName=tl;var td="SelectItemText",th=n.forwardRef((t,e)=>{let{__scopeSelect:r,className:i,style:a,...u}=t,l=R(td,r),s=K(td,r),f=tf(td,r),p=L(td,r),[d,h]=n.useState(null),y=(0,c.s)(e,t=>h(t),f.onItemTextChange,t=>{var e;return null===(e=s.itemTextRefCallback)||void 0===e?void 0:e.call(s,t,f.value,f.disabled)}),v=null==d?void 0:d.textContent,g=n.useMemo(()=>(0,S.jsx)("option",{value:f.value,disabled:f.disabled,children:v},f.value),[f.disabled,f.value,v]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=p;return(0,w.N)(()=>(b(g),()=>x(g)),[b,x,g]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(m.sG.span,{id:f.textId,...u,ref:y}),f.isSelected&&l.valueNode&&!l.valueNodeHasChildren?o.createPortal(u.children,l.valueNode):null]})});th.displayName=td;var ty="SelectItemIndicator",tv=n.forwardRef((t,e)=>{let{__scopeSelect:r,...n}=t;return tf(ty,r).isSelected?(0,S.jsx)(m.sG.span,{"aria-hidden":!0,...n,ref:e}):null});tv.displayName=ty;var tm="SelectScrollUpButton",tg=n.forwardRef((t,e)=>{let r=K(tm,t.__scopeSelect),o=tt(tm,t.__scopeSelect),[i,a]=n.useState(!1),u=(0,c.s)(e,o.onScrollButtonChange);return(0,w.N)(()=>{if(r.viewport&&r.isPositioned){let t=function(){a(e.scrollTop>0)},e=r.viewport;return t(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[r.viewport,r.isPositioned]),i?(0,S.jsx)(tw,{...t,ref:u,onAutoScroll:()=>{let{viewport:t,selectedItem:e}=r;t&&e&&(t.scrollTop=t.scrollTop-e.offsetHeight)}}):null});tg.displayName=tm;var tb="SelectScrollDownButton",tx=n.forwardRef((t,e)=>{let r=K(tb,t.__scopeSelect),o=tt(tb,t.__scopeSelect),[i,a]=n.useState(!1),u=(0,c.s)(e,o.onScrollButtonChange);return(0,w.N)(()=>{if(r.viewport&&r.isPositioned){let t=function(){let t=e.scrollHeight-e.clientHeight;a(Math.ceil(e.scrollTop)<t)},e=r.viewport;return t(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[r.viewport,r.isPositioned]),i?(0,S.jsx)(tw,{...t,ref:u,onAutoScroll:()=>{let{viewport:t,selectedItem:e}=r;t&&e&&(t.scrollTop=t.scrollTop+e.offsetHeight)}}):null});tx.displayName=tb;var tw=n.forwardRef((t,e)=>{let{__scopeSelect:r,onAutoScroll:o,...i}=t,u=K("SelectScrollButton",r),c=n.useRef(null),l=_(r),s=n.useCallback(()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)},[]);return n.useEffect(()=>()=>s(),[s]),(0,w.N)(()=>{var t;let e=l().find(t=>t.ref.current===document.activeElement);null==e||null===(t=e.ref.current)||void 0===t||t.scrollIntoView({block:"nearest"})},[l]),(0,S.jsx)(m.sG.div,{"aria-hidden":!0,...i,ref:e,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===c.current&&(c.current=window.setInterval(o,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{var t;null===(t=u.onItemLeave)||void 0===t||t.call(u),null===c.current&&(c.current=window.setInterval(o,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{s()})})}),tO=n.forwardRef((t,e)=>{let{__scopeSelect:r,...n}=t;return(0,S.jsx)(m.sG.div,{"aria-hidden":!0,...n,ref:e})});tO.displayName="SelectSeparator";var tj="SelectArrow";function tA(t){return""===t||void 0===t}n.forwardRef((t,e)=>{let{__scopeSelect:r,...n}=t,o=D(r),i=R(tj,r),a=K(tj,r);return i.open&&"popper"===a.position?(0,S.jsx)(y.i3,{...o,...n,ref:e}):null}).displayName=tj;var tS=n.forwardRef((t,e)=>{let{value:r,...o}=t,i=n.useRef(null),a=(0,c.s)(e,i),u=function(t){let e=n.useRef({value:t,previous:t});return n.useMemo(()=>(e.current.value!==t&&(e.current.previous=e.current.value,e.current.value=t),e.current.previous),[t])}(r);return n.useEffect(()=>{let t=i.current,e=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==r&&e){let n=new Event("change",{bubbles:!0});e.call(t,r),t.dispatchEvent(n)}},[u,r]),(0,S.jsx)(O.s,{asChild:!0,children:(0,S.jsx)("select",{...o,ref:a,defaultValue:r})})});function tP(t){let e=(0,b.c)(t),r=n.useRef(""),o=n.useRef(0),i=n.useCallback(t=>{let n=r.current+t;e(n),function t(e){r.current=e,window.clearTimeout(o.current),""!==e&&(o.current=window.setTimeout(()=>t(""),1e3))}(n)},[e]),a=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,a]}function tE(t,e,r){var n,o;let i=e.length>1&&Array.from(e).every(t=>t===e[0])?e[0]:e,a=r?t.indexOf(r):-1,u=(n=t,o=Math.max(a,0),n.map((t,e)=>n[(o+e)%n.length]));1===i.length&&(u=u.filter(t=>t!==r));let c=u.find(t=>t.textValue.toLowerCase().startsWith(i.toLowerCase()));return c!==r?c:void 0}tS.displayName="BubbleSelect";var tk=U,tM=z,t_=$,tT=H,tC=q,tI=X,tD=tr,tN=ta,tR=tc,tB=tp,tL=th,tU=tv,tF=tg,tz=tx,tW=tO},93179:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});function n(t,e){if(!t)throw Error("Invariant failed")}},93294:(t,e,r)=>{var n=r(34711);t.exports=function(t){return function(e){return n(e,t)}}},94356:(t,e,r)=>{var n=r(70771),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},94380:(t,e,r)=>{var n=r(44101),o=r(18686),i=r(66373),a=r(38008),u=r(40382),c=r(98233),l=r(7512),s="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=l(n),v=l(o),m=l(i),g=l(a),b=l(u),x=c;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||u&&x(new u)!=d)&&(x=function(t){var e=c(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return h;case v:return s;case m:return f;case g:return p;case b:return d}return e}),t.exports=x},94517:(t,e,r)=>{"use strict";r.d(e,{m:()=>W});var n=r(12115),o=r(67206),i=r.n(o),a=r(59882),u=r.n(a),c=r(52596),l=r(16377);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t){return Array.isArray(t)&&(0,l.vh)(t[0])&&(0,l.vh)(t[1])?t.join(" ~ "):t}var v=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=t.itemStyle,s=void 0===a?{}:a,d=t.labelStyle,v=t.payload,m=t.formatter,g=t.itemSorter,b=t.wrapperClassName,x=t.labelClassName,w=t.label,O=t.labelFormatter,j=t.accessibilityLayer,A=h({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===o?{}:o),S=h({margin:0},void 0===d?{}:d),P=!u()(w),E=P?w:"",k=(0,c.A)("recharts-default-tooltip",b),M=(0,c.A)("recharts-tooltip-label",x);return P&&O&&null!=v&&(E=O(w,v)),n.createElement("div",f({className:k,style:A},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:S},n.isValidElement(E)?E:"".concat(E)),function(){if(v&&v.length){var t=(g?i()(v,g):v).map(function(t,e){if("none"===t.type)return null;var o=h({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},s),i=t.formatter||m||y,a=t.value,u=t.name,c=a,f=u;if(i&&null!=c&&null!=f){var d=i(a,u,t,e,v);if(Array.isArray(d)){var g=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(d,2)||function(t,e){if(t){if("string"==typeof t)return p(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=g[0],f=g[1]}else c=d}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function g(t,e,r){var n;return(n=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var b="recharts-tooltip-wrapper",x={visibility:"hidden"};function w(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,s=t.viewBoxDimension;if(i&&(0,l.Et)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+s?Math.max(f,c[n]):Math.max(p,c[n])}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(S=function(){return!!t})()}function P(t){return(P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function E(t,e){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function k(t,e,r){return(e=M(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function M(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var _=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=P(e),k(t=function(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,S()?Reflect.construct(e,n||[],P(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),k(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&E(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,o,i,a,u,s,f,p,d,h,y,v,m,O,j,S,P,E=this,k=this.props,M=k.active,_=k.allowEscapeViewBox,T=k.animationDuration,C=k.animationEasing,I=k.children,D=k.coordinate,N=k.hasPayload,R=k.isAnimationActive,B=k.offset,L=k.position,U=k.reverseDirection,F=k.useTranslate3d,z=k.viewBox,W=k.wrapperStyle,$=(h=(t={allowEscapeViewBox:_,coordinate:D,offsetTopLeft:B,position:L,reverseDirection:U,tooltipBox:this.state.lastBoundingBox,useTranslate3d:F,viewBox:z}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,O=t.reverseDirection,j=t.tooltipBox,S=t.useTranslate3d,P=t.viewBox,j.height>0&&j.width>0&&y?(r=(e={translateX:p=w({allowEscapeViewBox:h,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:O,tooltipDimension:j.width,viewBox:P,viewBoxDimension:P.width}),translateY:d=w({allowEscapeViewBox:h,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:O,tooltipDimension:j.height,viewBox:P,viewBoxDimension:P.height}),useTranslate3d:S}).translateX,o=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(o,"px, 0)"):"translate(".concat(r,"px, ").concat(o,"px)")}):f=x,{cssProperties:f,cssClasses:(a=(i={translateX:p,translateY:d,coordinate:y}).coordinate,u=i.translateX,s=i.translateY,(0,c.A)(b,g(g(g(g({},"".concat(b,"-right"),(0,l.Et)(u)&&a&&(0,l.Et)(a.x)&&u>=a.x),"".concat(b,"-left"),(0,l.Et)(u)&&a&&(0,l.Et)(a.x)&&u<a.x),"".concat(b,"-bottom"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s>=a.y),"".concat(b,"-top"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s<a.y)))}),H=$.cssClasses,q=$.cssProperties,V=A(A({transition:R&&M?"transform ".concat(T,"ms ").concat(C):void 0},q),{},{pointerEvents:"none",visibility:!this.state.dismissed&&M&&N?"visible":"hidden",position:"absolute",top:0,left:0},W);return n.createElement("div",{tabIndex:-1,className:H,style:V,ref:function(t){E.wrapperNode=t}},I)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,M(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),T=r(41643),C=r(2494);function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function D(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function N(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?D(Object(r),!0).forEach(function(e){U(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(R=function(){return!!t})()}function B(t){return(B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function L(t,e){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function U(t,e,r){return(e=F(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function F(t){var e=function(t,e){if("object"!=I(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=I(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==I(e)?e:e+""}function z(t){return t.dataKey}var W=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=B(t),function(t,e){if(e&&("object"===I(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,R()?Reflect.construct(t,e||[],B(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&L(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,o=r.active,i=r.allowEscapeViewBox,a=r.animationDuration,u=r.animationEasing,c=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,m=r.reverseDirection,g=r.useTranslate3d,b=r.viewBox,x=r.wrapperStyle,w=null!=d?d:[];s&&w.length&&(w=(0,C.s)(d.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),h,z));var O=w.length>0;return n.createElement(_,{allowEscapeViewBox:i,animationDuration:a,animationEasing:u,isAnimationActive:f,active:o,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:m,useTranslate3d:g,viewBox:b,wrapperStyle:x},(t=N(N({},this.props),{},{payload:w}),n.isValidElement(c)?n.cloneElement(c,t):"function"==typeof c?n.createElement(c,t):n.createElement(v,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,F(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);U(W,"displayName","Tooltip"),U(W,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!T.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},94999:(t,e,r)=>{var n=r(5658);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=+!!e,e}},95442:(t,e,r)=>{"use strict";r.d(e,{A:()=>function t(){var e=new n,r=[],o=[],i=u;function c(t){let n=e.get(t);if(void 0===n){if(i!==u)return i;e.set(t,n=r.push(t)-1)}return o[n%o.length]}return c.domain=function(t){if(!arguments.length)return r.slice();for(let o of(r=[],e=new n,t))e.has(o)||e.set(o,r.push(o)-1);return c},c.range=function(t){return arguments.length?(o=Array.from(t),c):o.slice()},c.unknown=function(t){return arguments.length?(i=t,c):i},c.copy=function(){return t(r,o).unknown(i)},a.C.apply(c,arguments),c},h:()=>u});class n extends Map{constructor(t,e=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function o({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function i(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(28749);let u=Symbol("implicit")},96294:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},96540:(t,e,r)=>{var n=r(31545),o=r(26151),i=r(53696),a=r(13364),u=r(20988);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},96548:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},96699:(t,e,r)=>{var n=r(51911),o=r(48973),i=r(20134),a=r(79595),u=r(32197),c=r(92972),l=r(94356);t.exports=function(t,e){return a(t)&&u(e)?c(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},97124:(t,e,r)=>{t.exports=r(75031)(r(62464))},98233:(t,e,r)=>{var n=r(24376),o=r(20570),i=r(64439),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},99544:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}}}]);