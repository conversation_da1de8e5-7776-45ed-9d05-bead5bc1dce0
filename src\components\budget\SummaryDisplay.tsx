
'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { TrendingUp, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON><PERSON>, DollarSign, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { EnhancedProgress } from '@/components/ui/enhanced-progress';
import { AnimatedNumber } from '@/components/ui/animated-wrapper';
import { motion } from 'framer-motion';

interface SummaryDisplayProps {
  totalIncome: number;
  overallTotalAllocated: number;
  balancesVisible: boolean;
}

export default function SummaryDisplay({ totalIncome, overallTotalAllocated, balancesVisible }: SummaryDisplayProps) {
  const overallRemaining = totalIncome - overallTotalAllocated;
  const allocationPercentage = totalIncome > 0 ? (overallTotalAllocated / totalIncome) * 100 : 0;
  const isOverBudget = overallRemaining < 0;

  return (
    <Card className="card-enhanced glow-primary">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center gap-2 font-headline">
          <motion.div
            animate={{ rotate: [0, 5, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          >
            <TrendingUp className="h-5 w-5 text-primary" />
          </motion.div>
          Budget Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <DollarSign className="h-3 w-3" />
              Total Income
            </div>
            <div className="font-semibold text-lg">
              <AnimatedNumber
                value={totalIncome}
                prefix="R "
                className={balancesVisible ? "" : "blur-sm"}
              />
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <PieChart className="h-3 w-3" />
              Allocated
            </div>
            <div className="font-semibold text-lg">
              <AnimatedNumber
                value={overallTotalAllocated}
                prefix="R "
                className={balancesVisible ? "" : "blur-sm"}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span className="text-muted-foreground">Allocation Progress</span>
            <span className="font-medium">{Math.round(allocationPercentage)}%</span>
          </div>

          <EnhancedProgress
            value={allocationPercentage}
            max={100}
            variant={isOverBudget ? "destructive" : allocationPercentage >= 90 ? "warning" : "default"}
            size="md"
            animated={true}
            gradient={true}
            glow={isOverBudget}
          />
        </div>

        <motion.div
          className={`flex justify-between items-center text-sm font-semibold p-3 rounded-lg ${
            isOverBudget
              ? 'text-destructive bg-destructive/10 border border-destructive/20'
              : 'text-success bg-success/10 border border-success/20'
          }`}
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <span className="flex items-center gap-2">
            {isOverBudget ? (
              <AlertTriangle className="h-4 w-4" />
            ) : (
              <BadgeCheck className="h-4 w-4" />
            )}
            {isOverBudget ? "Over Allocated:" : "Remaining:"}
          </span>
          <AnimatedNumber
            value={Math.abs(overallRemaining)}
            prefix="R "
            className={balancesVisible ? "" : "blur-sm"}
          />
        </motion.div>

        {isOverBudget && (
          <motion.div
            className="flex items-center gap-2 text-destructive text-xs p-3 bg-destructive/5 border border-destructive/20 rounded-lg"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <AlertTriangle className="h-4 w-4 animate-pulse" />
            <span>Warning: Your allocations exceed your income!</span>
          </motion.div>
        )}
         {!isOverBudget && totalIncome > 0 && overallTotalAllocated <= totalIncome && (
          <div className="flex items-center gap-2 text-green-600 text-xs p-2 bg-green-600/10 rounded-md">
            <BadgeCheck className="h-4 w-4" />
            <span>Your budget is balanced or has funds remaining.</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
