(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root of the server]__860be4f0._.js", {

"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
function middleware(request) {
    // Client-side checks in AuthProvider and page.tsx handle route protection.
    // This middleware passes the request through.
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
} // The original file contained comments about not using middleware in Firebase Studio
 // and an example of what standard middleware might look like.
 // Those comments have been preserved below for context, but the active code
 // is just the minimal middleware function above.
 // Original comments for context:
 // Firebase Studio currently does not support middleware.ts directly.
 // Route protection is handled client-side in the page components (e.g., src/app/page.tsx)
 // using the AuthContext and useRouter from next/navigation.
 //
 // If you were to implement middleware for route protection in a standard Next.js setup,
 // it might look something like this (DO NOT USE THIS IN FIREBASE STUDIO):
 /*
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// This is the actual middleware function signature Next.js expects
export function exampleMiddleware(request: NextRequest) {
  const currentUserCookie = request.cookies.get('firebaseAuthToken'); // Example cookie name

  const protectedRoutes = ['/']; // Add any other routes that need protection
  const authRoutes = ['/login', '/register'];

  const { pathname } = request.nextUrl;

  if (protectedRoutes.includes(pathname) && !currentUserCookie) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  if (authRoutes.includes(pathname) && currentUserCookie) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/', '/login', '/register'], // Apply middleware to these paths
};
*/  // For Firebase Studio, client-side checks are the way to go for now.
 // The AuthProvider and useAuth hook along with useRouter in page.tsx handle this.
 // The original file ended with `export {};` which caused the error.
 // It's now replaced by the default export above.
}}),
}]);

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__860be4f0._.js.map