'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Minus, LucideIcon } from 'lucide-react';
import { AnimatedNumber } from '@/components/ui/animated-wrapper';

interface ModernMetricCardProps {
  title: string;
  value: number;
  previousValue?: number;
  prefix?: string;
  suffix?: string;
  icon: LucideIcon;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: number;
  description?: string;
  className?: string;
  balancesVisible?: boolean;
  children?: ReactNode;
}

export default function ModernMetricCard({
  title,
  value,
  previousValue,
  prefix = 'R ',
  suffix = '',
  icon: Icon,
  trend,
  trendValue,
  description,
  className = '',
  balancesVisible = true,
  children,
}: ModernMetricCardProps) {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return TrendingUp;
      case 'down':
        return TrendingDown;
      default:
        return Minus;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-success';
      case 'down':
        return 'text-destructive';
      default:
        return 'text-muted-foreground';
    }
  };

  const TrendIcon = getTrendIcon();

  return (
    <motion.div
      className={`metric-card glow-primary-modern ${className}`}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <motion.div
            className="w-12 h-12 bg-gradient-to-r from-primary/20 to-accent/20 rounded-xl flex items-center justify-center"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ type: 'spring', stiffness: 400, damping: 17 }}
          >
            <Icon className="w-6 h-6 text-primary" />
          </motion.div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
            {description && (
              <p className="text-xs text-muted-foreground/70">{description}</p>
            )}
          </div>
        </div>

        {/* Trend Indicator */}
        {trend && trendValue !== undefined && (
          <motion.div
            className={`flex items-center gap-1 px-2 py-1 rounded-lg bg-white/5 ${getTrendColor()}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <TrendIcon className="w-3 h-3" />
            <span className="text-xs font-medium">
              {Math.abs(trendValue).toFixed(1)}%
            </span>
          </motion.div>
        )}
      </div>

      {/* Value */}
      <div className="mb-4">
        <div className={`text-3xl font-bold ${balancesVisible ? '' : 'blur-sm'}`}>
          <AnimatedNumber
            value={value}
            prefix={prefix}
            suffix={suffix}
            duration={1}
          />
        </div>
        
        {previousValue !== undefined && balancesVisible && (
          <motion.div
            className="flex items-center gap-2 mt-2"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <span className="text-sm text-muted-foreground">
              vs {prefix}{previousValue.toFixed(2)}{suffix} last month
            </span>
          </motion.div>
        )}
      </div>

      {/* Additional Content */}
      {children && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          {children}
        </motion.div>
      )}

      {/* Decorative Elements */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-full" />
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-accent/10 to-transparent rounded-tr-full" />
    </motion.div>
  );
}

interface QuickStatsGridProps {
  totalIncome: number;
  totalAllocated: number;
  remaining: number;
  savingsGoal?: number;
  balancesVisible?: boolean;
}

export function QuickStatsGrid({
  totalIncome,
  totalAllocated,
  remaining,
  savingsGoal,
  balancesVisible = true,
}: QuickStatsGridProps) {
  const allocationPercentage = totalIncome > 0 ? (totalAllocated / totalIncome) * 100 : 0;
  const savingsPercentage = savingsGoal && totalIncome > 0 ? (remaining / savingsGoal) * 100 : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <ModernMetricCard
        title="Total Income"
        value={totalIncome}
        icon={TrendingUp}
        trend="up"
        trendValue={12.5}
        description="Monthly income"
        balancesVisible={balancesVisible}
      >
        <div className="progress-modern h-2">
          <motion.div
            className="h-full"
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 1, delay: 0.5 }}
          />
        </div>
      </ModernMetricCard>

      <ModernMetricCard
        title="Allocated"
        value={totalAllocated}
        icon={TrendingDown}
        trend={allocationPercentage > 100 ? 'down' : 'up'}
        trendValue={allocationPercentage}
        description="Budget allocation"
        balancesVisible={balancesVisible}
      >
        <div className="progress-modern h-2">
          <motion.div
            className="h-full"
            initial={{ width: 0 }}
            animate={{ width: `${Math.min(allocationPercentage, 100)}%` }}
            transition={{ duration: 1, delay: 0.5 }}
          />
        </div>
      </ModernMetricCard>

      <ModernMetricCard
        title="Remaining"
        value={remaining}
        icon={remaining >= 0 ? TrendingUp : TrendingDown}
        trend={remaining >= 0 ? 'up' : 'down'}
        trendValue={Math.abs((remaining / totalIncome) * 100)}
        description={remaining >= 0 ? 'Available to save' : 'Over budget'}
        balancesVisible={balancesVisible}
        className={remaining < 0 ? 'border-destructive/20' : ''}
      />

      {savingsGoal && (
        <ModernMetricCard
          title="Savings Goal"
          value={savingsGoal}
          icon={TrendingUp}
          trend={savingsPercentage >= 100 ? 'up' : 'neutral'}
          trendValue={savingsPercentage}
          description="Monthly target"
          balancesVisible={balancesVisible}
        >
          <div className="progress-modern h-2">
            <motion.div
              className="h-full bg-gradient-to-r from-success via-success/90 to-success/80"
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(savingsPercentage || 0, 100)}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </ModernMetricCard>
      )}
    </div>
  );
}
