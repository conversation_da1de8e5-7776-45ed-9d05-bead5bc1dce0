'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface EnhancedProgressProps {
  value: number;
  max?: number;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  animated?: boolean;
  gradient?: boolean;
  glow?: boolean;
  children?: ReactNode;
}

const variantStyles = {
  default: 'from-primary to-primary/80',
  success: 'from-success to-success/80',
  warning: 'from-warning to-warning/80',
  destructive: 'from-destructive to-destructive/80',
};

const sizeStyles = {
  sm: 'h-1',
  md: 'h-2',
  lg: 'h-3',
};

const glowStyles = {
  default: 'shadow-lg shadow-primary/30',
  success: 'shadow-lg shadow-success/30',
  warning: 'shadow-lg shadow-warning/30',
  destructive: 'shadow-lg shadow-destructive/30',
};

export function EnhancedProgress({
  value,
  max = 100,
  className,
  variant = 'default',
  size = 'md',
  showValue = false,
  animated = true,
  gradient = true,
  glow = false,
  children,
}: EnhancedProgressProps) {
  const percentage = Math.min((value / max) * 100, 100);
  const isComplete = percentage >= 100;

  return (
    <div className={cn('w-full space-y-1', className)}>
      {(showValue || children) && (
        <div className="flex justify-between items-center text-sm">
          {children}
          {showValue && (
            <motion.span
              key={value}
              initial={animated ? { scale: 1.2, opacity: 0 } : {}}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className="font-medium"
            >
              {value} / {max}
            </motion.span>
          )}
        </div>
      )}
      
      <div
        className={cn(
          'w-full bg-muted rounded-full overflow-hidden',
          sizeStyles[size],
          glow && glowStyles[variant]
        )}
      >
        <motion.div
          className={cn(
            'h-full rounded-full',
            gradient
              ? `bg-gradient-to-r ${variantStyles[variant]}`
              : variant === 'default'
              ? 'bg-primary'
              : variant === 'success'
              ? 'bg-success'
              : variant === 'warning'
              ? 'bg-warning'
              : 'bg-destructive'
          )}
          initial={animated ? { width: 0 } : { width: `${percentage}%` }}
          animate={{ width: `${percentage}%` }}
          transition={
            animated
              ? {
                  duration: 1,
                  ease: 'easeOut',
                  type: 'spring',
                  stiffness: 100,
                  damping: 15,
                }
              : {}
          }
        />
        
        {/* Shimmer effect for completed progress */}
        {isComplete && animated && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            initial={{ x: '-100%' }}
            animate={{ x: '100%' }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              repeatDelay: 2,
              ease: 'easeInOut',
            }}
          />
        )}
      </div>
    </div>
  );
}

interface CircularProgressProps {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  showValue?: boolean;
  animated?: boolean;
  children?: ReactNode;
}

export function CircularProgress({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  className,
  variant = 'default',
  showValue = true,
  animated = true,
  children,
}: CircularProgressProps) {
  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const colorMap = {
    default: 'stroke-primary',
    success: 'stroke-success',
    warning: 'stroke-warning',
    destructive: 'stroke-destructive',
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-muted opacity-20"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          className={colorMap[variant]}
          initial={animated ? { strokeDashoffset: circumference } : { strokeDashoffset }}
          animate={{ strokeDashoffset }}
          transition={
            animated
              ? {
                  duration: 1.5,
                  ease: 'easeOut',
                  type: 'spring',
                  stiffness: 50,
                  damping: 15,
                }
              : {}
          }
          style={{
            strokeDasharray,
          }}
        />
      </svg>
      
      {/* Center content */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (
          showValue && (
            <motion.div
              key={value}
              initial={animated ? { scale: 0.8, opacity: 0 } : {}}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className="text-center"
            >
              <div className="text-2xl font-bold">{Math.round(percentage)}%</div>
              <div className="text-xs text-muted-foreground">
                {value} / {max}
              </div>
            </motion.div>
          )
        )}
      </div>
    </div>
  );
}

interface ProgressRingProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  className?: string;
  children?: ReactNode;
}

export function ProgressRing({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  className,
  children,
}: ProgressRingProps) {
  const sizeMap = {
    sm: { size: 60, strokeWidth: 4 },
    md: { size: 80, strokeWidth: 6 },
    lg: { size: 120, strokeWidth: 8 },
  };

  const { size: ringSize, strokeWidth } = sizeMap[size];

  return (
    <CircularProgress
      value={value}
      max={max}
      size={ringSize}
      strokeWidth={strokeWidth}
      variant={variant}
      className={className}
      showValue={false}
    >
      {children}
    </CircularProgress>
  );
}

interface SteppedProgressProps {
  currentStep: number;
  totalSteps: number;
  className?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}

export function SteppedProgress({
  currentStep,
  totalSteps,
  className,
  variant = 'default',
  size = 'md',
  animated = true,
}: SteppedProgressProps) {
  const stepSizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  const colorMap = {
    default: 'bg-primary',
    success: 'bg-success',
    warning: 'bg-warning',
    destructive: 'bg-destructive',
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {Array.from({ length: totalSteps }, (_, index) => {
        const stepNumber = index + 1;
        const isCompleted = stepNumber <= currentStep;
        const isCurrent = stepNumber === currentStep;

        return (
          <motion.div
            key={index}
            className={cn(
              'rounded-full transition-all duration-300',
              stepSizes[size],
              isCompleted
                ? colorMap[variant]
                : 'bg-muted',
              isCurrent && 'ring-2 ring-offset-2 ring-primary/50'
            )}
            initial={animated ? { scale: 0 } : {}}
            animate={{ scale: 1 }}
            transition={
              animated
                ? {
                    duration: 0.3,
                    delay: index * 0.1,
                    ease: 'easeOut',
                  }
                : {}
            }
            whileHover={{ scale: 1.2 }}
          />
        );
      })}
    </div>
  );
}
