'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  PieChart,
  Target,
  TrendingUp,
  Settings,
  HelpCircle,
  Menu,
  X,
  Wallet,
  BarChart3,
  Trophy,
  Bell,
  User,
  LogOut,
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';

interface SidebarItem {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  href: string;
  badge?: number;
}

const sidebarItems: SidebarItem[] = [
  { icon: Home, label: 'Dashboard', href: '/' },
  { icon: Wallet, label: 'Income', href: '/income' },
  { icon: PieChart, label: 'Categories', href: '/categories' },
  { icon: BarChart3, label: 'Analytics', href: '/analytics' },
  { icon: Target, label: 'Goals', href: '/goals' },
  { icon: Trophy, label: 'Achievements', href: '/achievements', badge: 3 },
  { icon: TrendingUp, label: 'Reports', href: '/reports' },
];

const bottomItems: SidebarItem[] = [
  { icon: Bell, label: 'Notifications', href: '/notifications', badge: 2 },
  { icon: Settings, label: 'Settings', href: '/settings' },
  { icon: HelpCircle, label: 'Help', href: '/help' },
];

interface ModernSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

export default function ModernSidebar({ isOpen, onToggle }: ModernSidebarProps) {
  const pathname = usePathname();
  const { currentUser, signOut } = useAuth();

  const getInitials = (email?: string | null, name?: string | null) => {
    if (name) {
      const nameParts = name.split(' ').filter(Boolean);
      if (nameParts.length > 1) {
        return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
      }
      return name.substring(0, 2).toUpperCase();
    }
    if (email) return email.substring(0, 2).toUpperCase();
    return 'U';
  };

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center gap-3">
          <motion.div
            className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-xl flex items-center justify-center"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Wallet className="w-5 h-5 text-white" />
          </motion.div>
          <div>
            <h1 className="text-xl font-bold text-foreground">BudgetWise</h1>
            <p className="text-xs text-muted-foreground">Financial Freedom</p>
          </div>
        </div>
      </div>

      {/* User Profile */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center gap-3 p-3 rounded-xl bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
          <Avatar className="w-10 h-10">
            <AvatarImage src={currentUser?.photoURL || undefined} />
            <AvatarFallback className="bg-primary/20 text-primary">
              {getInitials(currentUser?.email, currentUser?.displayName)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-foreground truncate">
              {currentUser?.displayName || currentUser?.email?.split('@')[0] || 'User'}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {currentUser?.email}
            </p>
          </div>
          <User className="w-4 h-4 text-muted-foreground" />
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4 space-y-2">
        {sidebarItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <motion.div
              key={item.href}
              whileHover={{ x: 4 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href={item.href}
                className={`${
                  isActive ? 'sidebar-item-active' : 'sidebar-item'
                } relative`}
              >
                <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-foreground'} transition-colors`} />
                <span className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>
                  {item.label}
                </span>
                {item.badge && (
                  <motion.div
                    className="ml-auto w-5 h-5 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    {item.badge}
                  </motion.div>
                )}
                {isActive && (
                  <motion.div
                    className="absolute left-0 top-0 bottom-0 w-1 bg-primary rounded-r-full"
                    layoutId="activeIndicator"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
              </Link>
            </motion.div>
          );
        })}
      </nav>

      {/* Bottom Items */}
      <div className="p-4 border-t border-white/10 space-y-2">
        {bottomItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <motion.div
              key={item.href}
              whileHover={{ x: 4 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                href={item.href}
                className={`${
                  isActive ? 'sidebar-item-active' : 'sidebar-item'
                } relative`}
              >
                <Icon className={`w-5 h-5 ${isActive ? 'text-primary' : 'text-muted-foreground group-hover:text-foreground'} transition-colors`} />
                <span className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>
                  {item.label}
                </span>
                {item.badge && (
                  <motion.div
                    className="ml-auto w-5 h-5 bg-accent text-accent-foreground rounded-full flex items-center justify-center text-xs font-bold"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    {item.badge}
                  </motion.div>
                )}
              </Link>
            </motion.div>
          );
        })}

        {/* Sign Out */}
        <motion.div
          whileHover={{ x: 4 }}
          whileTap={{ scale: 0.98 }}
        >
          <button
            onClick={signOut}
            className="sidebar-item w-full text-left text-destructive hover:bg-destructive/10"
          >
            <LogOut className="w-5 h-5" />
            <span className="font-medium">Sign Out</span>
          </button>
        </motion.div>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={onToggle}
        className="fixed top-4 left-4 z-50 lg:hidden glass-card w-10 h-10"
      >
        {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
      </Button>

      {/* Desktop Sidebar */}
      <motion.aside
        className="hidden lg:flex fixed left-0 top-0 h-full w-80 sidebar-modern z-40"
        initial={{ x: -320 }}
        animate={{ x: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        <SidebarContent />
      </motion.aside>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={onToggle}
            />

            {/* Sidebar */}
            <motion.aside
              className="fixed left-0 top-0 h-full w-80 sidebar-modern z-50 lg:hidden"
              initial={{ x: -320 }}
              animate={{ x: 0 }}
              exit={{ x: -320 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
              <SidebarContent />
            </motion.aside>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
