(()=>{var e={};e.id=974,e.ids=[974],e.modules={22:(e,t,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),l=r(45058);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):l(e)}},658:(e,t,r)=>{e.exports=r(41547)(r(85718),"Map")},1566:(e,t,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),l=r(17830),s=r(29395),c=r(12290),u="[object Map]",f="[object Promise]",d="[object Set]",p="[object WeakMap]",h="[object DataView]",y=c(n),m=c(o),v=c(i),g=c(a),b=c(l),x=s;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=u||i&&x(i.resolve())!=f||a&&x(new a)!=d||l&&x(new l)!=p)&&(x=function(e){var t=s(e),r="[object Object]"==t?e.constructor:void 0,n=r?c(r):"";if(n)switch(n){case y:return h;case m:return u;case v:return f;case g:return d;case b:return p}return t}),e.exports=x},1707:(e,t,r)=>{var n=r(35142),o=r(46436);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},1944:e=>{e.exports=function(){return!1}},2408:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},2896:(e,t,r)=>{var n=r(81488),o=r(59467);e.exports=function(e,t){return null!=e&&o(e,t,n)}},2984:(e,t,r)=>{var n=r(49227);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],l=t(a);if(null!=l&&(void 0===s?l==l&&!n(l):r(l,s)))var s=l,c=a}return c}},3105:e=>{e.exports=function(e){return e.split("")}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(e,t,r)=>{e.exports=r(85718).Uint8Array},5231:(e,t,r)=>{var n=r(29395),o=r(55048);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},5359:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},5566:(e,t,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,l=r?r[0]:t.charAt(0),s=r?n(r,1).join(""):t.slice(1);return l[e]()+s}}},6053:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},6330:e=>{e.exports=function(){return[]}},7383:(e,t,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);e.exports=function(e,t,r){if(!a(r))return!1;var l=typeof t;return("number"==l?!!(o(r)&&i(t,r.length)):"string"==l&&t in r)&&n(r[t],e)}},7651:(e,t,r)=>{var n=r(82038),o=r(52931),i=r(32269);e.exports=function(e){return i(e)?n(e):o(e)}},8336:(e,t,r)=>{var n=r(45803);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},8852:(e,t,r)=>{var n=r(1707);e.exports=function(e){return function(t){return n(t,e)}}},10034:(e,t,r)=>{var n=r(2984),o=r(22),i=r(46063);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),i):void 0}},10090:(e,t,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;e.exports=a?o(a):n},10188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>gP});var n={};r.r(n),r.d(n,{scaleBand:()=>aM,scaleDiverging:()=>function e(){var t=sy(uC()(l8));return t.copy=function(){return uN(t,e())},aS.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=sS(uC()).domain([.1,1,10]);return t.copy=function(){return uN(t,e()).base(t.base())},aS.apply(t,arguments)},scaleDivergingPow:()=>uT,scaleDivergingSqrt:()=>u_,scaleDivergingSymlog:()=>function e(){var t=sk(uC());return t.copy=function(){return uN(t,e()).constant(t.constant())},aS.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,l3),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,l3):[0,1],sy(n)},scaleImplicit:()=>aN,scaleLinear:()=>sm,scaleLog:()=>function e(){let t=sS(sr()).domain([1,10]);return t.copy=()=>st(t,e()).base(t.base()),aO.apply(t,arguments),t},scaleOrdinal:()=>aE,scalePoint:()=>aC,scalePow:()=>sT,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=lh){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(+r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e*=1)?t:n[lm(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(lu),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},aO.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function l(e){return null!=e&&e<=e?a[lm(i,e,0,o)]:t}function s(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,s()):[r,n]},l.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,s()):a.slice()},l.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return i.slice()},l.copy=function(){return e().domain([r,n]).range(a).unknown(t)},aO.apply(sy(l),arguments)},scaleRadial:()=>function e(){var t,r=sn(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(sI(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,l3)).map(sI)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},aO.apply(i,arguments),sy(i)},scaleSequential:()=>function e(){var t=sy(uk()(l8));return t.copy=function(){return uN(t,e())},aS.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=sS(uk()).domain([1,10]);return t.copy=function(){return uN(t,e()).base(t.base())},aS.apply(t,arguments)},scaleSequentialPow:()=>uE,scaleSequentialQuantile:()=>function e(){var t=[],r=l8;function n(e){if(null!=e&&!isNaN(e*=1))return r((lm(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(lu),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return sR(e);if(t>=1)return sD(e);var n,o=(n-1)*t,i=Math.floor(o),a=sD((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?sB:function(e=lu){if(e===lu)return sB;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,l=r-n+1,s=Math.log(a),c=.5*Math.exp(2*s/3),u=.5*Math.sqrt(s*c*(a-c)/a)*(l-a/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/a+u)),d=Math.min(o,Math.floor(r+(a-l)*c/a+u));e(t,r,f,d,i)}let a=t[r],l=n,s=o;for(sL(t,n,r),i(t[o],a)>0&&sL(t,n,o);l<s;){for(sL(t,l,s),++l,--s;0>i(t[l],a);)++l;for(;i(t[s],a)>0;)--s}0===i(t[n],a)?sL(t,n,s):sL(t,++s,o),s<=r&&(n=s+1),r<=s&&(o=s-1)}return t})(e,i).subarray(0,i+1));return a+(sR(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},aS.apply(n,arguments)},scaleSequentialSqrt:()=>uM,scaleSequentialSymlog:()=>function e(){var t=sk(uk());return t.copy=function(){return uN(t,e()).constant(t.constant())},aS.apply(t,arguments)},scaleSqrt:()=>s_,scaleSymlog:()=>function e(){var t=sk(sr());return t.copy=function(){return st(t,e()).constant(t.constant())},aO.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[lm(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},aO.apply(i,arguments)},scaleTime:()=>uA,scaleUtc:()=>uP,tickFormat:()=>sh});var o=r(60687),i=r(43210),a=r.n(i),l=r(16189),s=r(12157),c=r(72789),u=r(21279),f=r(32582);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:e,isPresent:t}){let r=(0,i.useId)(),n=(0,i.useRef)(null),a=(0,i.useRef)({width:0,height:0,top:0,left:0}),{nonce:l}=(0,i.useContext)(f.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:o,top:i,left:s}=a.current;if(t||!n.current||!e||!o)return;n.current.dataset.motionPopId=r;let c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${o}px !important;
            top: ${i}px !important;
            left: ${s}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),(0,o.jsx)(d,{isPresent:t,childRef:n,sizeRef:a,children:i.cloneElement(e,{ref:n})})}let h=({children:e,initial:t,isPresent:r,onExitComplete:n,custom:a,presenceAffectsLayout:l,mode:s})=>{let f=(0,c.M)(y),d=(0,i.useId)(),h=(0,i.useCallback)(e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;n&&n()},[f,n]),m=(0,i.useMemo)(()=>({id:d,initial:t,isPresent:r,custom:a,onExitComplete:h,register:e=>(f.set(e,!1),()=>f.delete(e))}),l?[Math.random(),h]:[r,h]);return(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[r]),i.useEffect(()=>{r||f.size||!n||n()},[r]),"popLayout"===s&&(e=(0,o.jsx)(p,{isPresent:r,children:e})),(0,o.jsx)(u.t.Provider,{value:m,children:e})};function y(){return new Map}var m=r(86044);let v=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}var b=r(15124);let x=({children:e,custom:t,initial:r=!0,onExitComplete:n,presenceAffectsLayout:a=!0,mode:l="sync",propagate:u=!1})=>{let[f,d]=(0,m.xQ)(u),p=(0,i.useMemo)(()=>g(e),[e]),y=u&&!f?[]:p.map(v),x=(0,i.useRef)(!0),w=(0,i.useRef)(p),j=(0,c.M)(()=>new Map),[O,S]=(0,i.useState)(p),[A,P]=(0,i.useState)(p);(0,b.E)(()=>{x.current=!1,w.current=p;for(let e=0;e<A.length;e++){let t=v(A[e]);y.includes(t)?j.delete(t):!0!==j.get(t)&&j.set(t,!1)}},[A,y.length,y.join("-")]);let k=[];if(p!==O){let e=[...p];for(let t=0;t<A.length;t++){let r=A[t],n=v(r);y.includes(n)||(e.splice(t,0,r),k.push(r))}"wait"===l&&k.length&&(e=k),P(g(e)),S(p);return}let{forceRender:N}=(0,i.useContext)(s.L);return(0,o.jsx)(o.Fragment,{children:A.map(e=>{let i=v(e),s=(!u||!!f)&&(p===A||y.includes(i));return(0,o.jsx)(h,{isPresent:s,initial:(!x.current||!!r)&&void 0,custom:s?void 0:t,presenceAffectsLayout:a,mode:l,onExitComplete:s?void 0:()=>{if(!j.has(i))return;j.set(i,!0);let e=!0;j.forEach(t=>{t||(e=!1)}),e&&(null==N||N(),P(w.current),u&&(null==d||d()),n&&n())},children:e},i)})})};var w=r(97905),j=r(85706),O=r(89667),S=r(80013),A=r(44493),P=r(82614);let k=(0,P.A)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]]),N=(0,P.A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),E=(0,P.A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);function M({totalIncome:e,onIncomeChange:t,balancesVisible:r}){let[n,a]=(0,i.useState)("");return(0,o.jsx)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:(0,o.jsxs)(A.Zp,{className:"card-modern hover-glow group",children:[(0,o.jsx)(A.aR,{className:"pb-4",children:(0,o.jsxs)(w.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,type:"spring"},className:"flex items-center justify-between",children:[(0,o.jsxs)(A.ZB,{className:"text-xl flex items-center gap-3 font-headline text-gradient-primary",children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:"p-2 rounded-xl bg-gradient-to-r from-primary/20 to-accent/20 group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300",children:(0,o.jsx)(k,{className:"h-6 w-6 text-primary"})}),"Total Monthly Income"]}),(0,o.jsx)(w.P.div,{animate:{scale:[1,1.1,1]},transition:{duration:2,repeat:1/0,repeatDelay:4},className:"p-2 rounded-full bg-success/20",children:(0,o.jsx)(N,{className:"h-5 w-5 text-success"})})]})}),(0,o.jsxs)(A.Wu,{className:"space-y-4",children:[(0,o.jsxs)(w.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.4},className:"relative",children:[(0,o.jsx)(S.J,{htmlFor:"totalIncome",className:"sr-only",children:"Total Income"}),(0,o.jsxs)("div",{className:"relative flex items-center",children:[(0,o.jsx)(w.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.6,type:"spring"},className:"absolute left-4 text-2xl font-bold text-gradient-primary z-10",children:"R"}),(0,o.jsx)(O.p,{id:"totalIncome",type:r?"number":"text",placeholder:"Enter your monthly income...",value:n,onChange:e=>{let n=e.target.value;r&&a(n);let o=""===n?0:parseFloat(n);!isNaN(o)&&o>=0?t(o):""===n&&t(0)},className:"input-modern text-2xl h-16 pl-12 pr-6 font-semibold text-center bg-gradient-to-r from-card/50 to-card/80 border-2 focus:border-primary/50 focus:shadow-lg focus:shadow-primary/20",min:"0",step:"any",readOnly:!r&&"••••"===n}),e>0&&r&&(0,o.jsx)(w.P.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.8},className:"absolute right-4 top-1/2 -translate-y-1/2",children:(0,o.jsxs)("div",{className:"flex items-center gap-2 px-3 py-1 rounded-full bg-success/20 border border-success/30",children:[(0,o.jsx)("div",{className:"w-2 h-2 rounded-full bg-success animate-pulse"}),(0,o.jsx)("span",{className:"text-xs font-medium text-success",children:"Active"})]})})]})]}),e>0&&r&&(0,o.jsx)(w.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1},className:"glass-premium p-4 rounded-xl",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-success/20 to-primary/20",children:(0,o.jsx)(E,{className:"h-4 w-4 text-success"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-foreground",children:"Monthly Budget"}),(0,o.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for allocation"})]})]}),(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsxs)("p",{className:"text-lg font-bold text-gradient-primary",children:["R ",e.toLocaleString("en-ZA",{minimumFractionDigits:2})]}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["~R ",(e/30).toFixed(0)," per day"]})]})]})}),!e&&(0,o.jsxs)(w.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"text-center py-6",children:[(0,o.jsx)(w.P.div,{animate:{y:[0,-5,0]},transition:{duration:2,repeat:1/0},className:"inline-block p-4 rounded-full bg-gradient-to-r from-muted/20 to-muted/10 mb-3",children:(0,o.jsx)(k,{className:"h-8 w-8 text-muted-foreground"})}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enter your monthly income to start budgeting"})]})]})]})})}var C=r(4780);let T={default:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500",destructive:"bg-red-500",primary:"bg-primary"},_={sm:"h-1",md:"h-2",lg:"h-3"};function I({value:e=0,max:t=100,className:r,indicatorClassName:n,showValue:i=!1,showPercentage:a=!1,animated:l=!0,variant:s="default",size:c="md",label:u,formatValue:f,...d}){let p=Math.min(Math.max(e/t*100,0),100),h=f?f(e,t):a?`${Math.round(p)}%`:`${e}/${t}`;return(0,o.jsxs)("div",{className:(0,C.cn)("w-full",r),...d,children:[(u||i||a)&&(0,o.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[u&&(0,o.jsx)("span",{className:"text-sm font-medium text-foreground",children:u}),(i||a)&&(0,o.jsx)("span",{className:"text-sm text-muted-foreground",children:h})]}),(0,o.jsxs)("div",{className:(0,C.cn)("relative w-full overflow-hidden rounded-full bg-muted",_[c]),children:[l?(0,o.jsx)(w.P.div,{className:(0,C.cn)("h-full rounded-full transition-colors",T[s],n),initial:{width:0},animate:{width:`${p}%`},transition:{duration:1,ease:"easeOut"}}):(0,o.jsx)("div",{className:(0,C.cn)("h-full rounded-full transition-all duration-500 ease-out",T[s],n),style:{width:`${p}%`}}),l&&p>0&&p<100&&(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer",style:{backgroundSize:"200% 100%"}})]})]})}function D({value:e=0,max:t=100,size:r=120,strokeWidth:n=8,className:i,variant:a="default",showValue:l=!1,showPercentage:s=!0,animated:c=!0,children:u}){let f=Math.min(Math.max(e/t*100,0),100),d=(r-n)/2,p=2*d*Math.PI,h=p-f/100*p,y={default:"stroke-primary",success:"stroke-green-500",warning:"stroke-yellow-500",destructive:"stroke-red-500",primary:"stroke-primary"};return(0,o.jsxs)("div",{className:(0,C.cn)("relative inline-flex items-center justify-center",i),children:[(0,o.jsxs)("svg",{width:r,height:r,className:"transform -rotate-90",children:[(0,o.jsx)("circle",{cx:r/2,cy:r/2,r:d,stroke:"currentColor",strokeWidth:n,fill:"none",className:"text-muted opacity-20"}),c?(0,o.jsx)(w.P.circle,{cx:r/2,cy:r/2,r:d,strokeWidth:n,fill:"none",strokeDasharray:p,strokeLinecap:"round",className:(0,C.cn)("transition-colors",y[a]),initial:{strokeDashoffset:p},animate:{strokeDashoffset:h},transition:{duration:1.5,ease:"easeOut"}}):(0,o.jsx)("circle",{cx:r/2,cy:r/2,r:d,strokeWidth:n,fill:"none",strokeDasharray:p,strokeDashoffset:h,strokeLinecap:"round",className:(0,C.cn)("transition-all duration-500 ease-out",y[a])})]}),(0,o.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:u||(0,o.jsxs)("div",{className:"text-center",children:[s&&(0,o.jsxs)("div",{className:"text-2xl font-bold text-foreground",children:[Math.round(f),"%"]}),l&&(0,o.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e,"/",t]})]})})]})}let R=(0,P.A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var B=r(14975);let L=(0,P.A)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),$=(0,P.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);function z({totalIncome:e,overallTotalAllocated:t,balancesVisible:r}){let n=e-t,i=e>0?t/e*100:0,a=n<0,l=e=>r?`R ${e.toFixed(2)}`:"R ••••";return(0,o.jsx)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},children:(0,o.jsxs)(A.Zp,{className:"card-modern hover-glow group",children:[(0,o.jsx)(A.aR,{className:"pb-4",children:(0,o.jsxs)(w.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.3,type:"spring"},className:"flex items-center justify-between",children:[(0,o.jsxs)(A.ZB,{className:"text-xl flex items-center gap-3 font-headline text-gradient-accent",children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,360]},transition:{duration:3,repeat:1/0,ease:"linear"},className:"p-2 rounded-xl bg-gradient-to-r from-accent/20 to-primary/20 group-hover:from-accent/30 group-hover:to-primary/30 transition-all duration-300",children:(0,o.jsx)(R,{className:"h-6 w-6 text-accent"})}),"Budget Overview"]}),(0,o.jsx)(w.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,repeatDelay:3},className:`p-2 rounded-full ${a?"bg-destructive/20":"bg-success/20"}`,children:a?(0,o.jsx)(B.A,{className:"h-5 w-5 text-destructive"}):(0,o.jsx)(L,{className:"h-5 w-5 text-success"})})]})}),(0,o.jsxs)(A.Wu,{className:"space-y-6",children:[(0,o.jsx)(w.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.5,type:"spring"},className:"flex justify-center",children:(0,o.jsx)(D,{value:t,max:e,size:120,strokeWidth:8,variant:a?"destructive":"success",showPercentage:!0,animated:!0,children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsxs)("div",{className:"text-lg font-bold text-foreground",children:[Math.round(i),"%"]}),(0,o.jsx)("div",{className:"text-xs text-muted-foreground",children:"Allocated"})]})})}),(0,o.jsxs)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"grid grid-cols-1 gap-4",children:[(0,o.jsx)("div",{className:"glass-premium p-4 rounded-xl",children:(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-primary/20 to-accent/20",children:(0,o.jsx)(E,{className:"h-4 w-4 text-primary"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Income"}),(0,o.jsx)("p",{className:"text-lg font-bold text-gradient-primary",children:l(e)})]})]})})}),(0,o.jsx)("div",{className:"glass-premium p-4 rounded-xl",children:(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:"p-2 rounded-lg bg-gradient-to-r from-accent/20 to-primary/20",children:(0,o.jsx)(N,{className:"h-4 w-4 text-accent"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Allocated"}),(0,o.jsx)("p",{className:"text-lg font-bold text-gradient-accent",children:l(t)})]})]})})}),(0,o.jsx)("div",{className:`glass-premium p-4 rounded-xl border-2 ${a?"border-destructive/30":"border-success/30"}`,children:(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:`p-2 rounded-lg ${a?"bg-destructive/20":"bg-success/20"}`,children:(0,o.jsx)($,{className:`h-4 w-4 ${a?"text-destructive":"text-success"}`})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:a?"Over Budget":"Remaining"}),(0,o.jsx)("p",{className:`text-lg font-bold ${a?"text-destructive":"text-success"}`,children:l(Math.abs(n))})]})]})})})]}),(0,o.jsx)(w.P.div,{initial:{opacity:0,scaleX:0},animate:{opacity:1,scaleX:1},transition:{delay:.9,duration:.8},children:(0,o.jsx)(I,{value:t,max:e,variant:a?"destructive":"success",size:"lg",animated:!0,showPercentage:!0,label:"Budget Allocation Progress"})}),a&&(0,o.jsxs)(w.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:1.1},className:"status-error flex items-center gap-3 p-4 rounded-xl",children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.5,repeat:1/0,repeatDelay:2},children:(0,o.jsx)(B.A,{className:"h-5 w-5"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"font-semibold",children:"Budget Exceeded!"}),(0,o.jsxs)("p",{className:"text-sm opacity-90",children:["You've allocated R ",Math.abs(n).toFixed(2)," more than your income."]})]})]}),!a&&e>0&&t<=e&&(0,o.jsxs)(w.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:1.1},className:"status-success flex items-center gap-3 p-4 rounded-xl",children:[(0,o.jsx)(w.P.div,{animate:{scale:[1,1.2,1]},transition:{duration:1,repeat:1/0,repeatDelay:2},children:(0,o.jsx)(L,{className:"h-5 w-5"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"font-semibold",children:"Budget Balanced!"}),(0,o.jsx)("p",{className:"text-sm opacity-90",children:"Your budget is well-managed with funds remaining."})]})]})]})]})})}var F=r(29523),U=r(11273),q=r(9510),H=r(98599),W=r(70569),V=r(65551),G=r(14163),X=r(66156),Y=r(46059),K=r(96963),Z="Collapsible",[J,Q]=(0,U.A)(Z),[ee,et]=J(Z),er=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:n,defaultOpen:a,disabled:l,onOpenChange:s,...c}=e,[u=!1,f]=(0,V.i)({prop:n,defaultProp:a,onChange:s});return(0,o.jsx)(ee,{scope:r,disabled:l,contentId:(0,K.B)(),open:u,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),children:(0,o.jsx)(G.sG.div,{"data-state":es(u),"data-disabled":l?"":void 0,...c,ref:t})})});er.displayName=Z;var en="CollapsibleTrigger",eo=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,i=et(en,r);return(0,o.jsx)(G.sG.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":es(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...n,ref:t,onClick:(0,W.m)(e.onClick,i.onOpenToggle)})});eo.displayName=en;var ei="CollapsibleContent",ea=i.forwardRef((e,t)=>{let{forceMount:r,...n}=e,i=et(ei,e.__scopeCollapsible);return(0,o.jsx)(Y.C,{present:r||i.open,children:({present:e})=>(0,o.jsx)(el,{...n,ref:t,present:e})})});ea.displayName=ei;var el=i.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:n,children:a,...l}=e,s=et(ei,r),[c,u]=i.useState(n),f=i.useRef(null),d=(0,H.s)(t,f),p=i.useRef(0),h=p.current,y=i.useRef(0),m=y.current,v=s.open||c,g=i.useRef(v),b=i.useRef(void 0);return i.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,X.N)(()=>{let e=f.current;if(e){b.current=b.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();p.current=t.height,y.current=t.width,g.current||(e.style.transitionDuration=b.current.transitionDuration,e.style.animationName=b.current.animationName),u(n)}},[s.open,n]),(0,o.jsx)(G.sG.div,{"data-state":es(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!v,...l,ref:d,style:{"--radix-collapsible-content-height":h?`${h}px`:void 0,"--radix-collapsible-content-width":m?`${m}px`:void 0,...e.style},children:v&&a})});function es(e){return e?"open":"closed"}var ec=r(43),eu="Accordion",ef=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[ed,ep,eh]=(0,q.N)(eu),[ey,em]=(0,U.A)(eu,[eh,Q]),ev=Q(),eg=i.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,o.jsx)(ed.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,o.jsx)(eS,{...n,ref:t}):(0,o.jsx)(eO,{...n,ref:t})})});eg.displayName=eu;var[eb,ex]=ey(eu),[ew,ej]=ey(eu,{collapsible:!1}),eO=i.forwardRef((e,t)=>{let{value:r,defaultValue:n,onValueChange:a=()=>{},collapsible:l=!1,...s}=e,[c,u]=(0,V.i)({prop:r,defaultProp:n,onChange:a});return(0,o.jsx)(eb,{scope:e.__scopeAccordion,value:c?[c]:[],onItemOpen:u,onItemClose:i.useCallback(()=>l&&u(""),[l,u]),children:(0,o.jsx)(ew,{scope:e.__scopeAccordion,collapsible:l,children:(0,o.jsx)(ek,{...s,ref:t})})})}),eS=i.forwardRef((e,t)=>{let{value:r,defaultValue:n,onValueChange:a=()=>{},...l}=e,[s=[],c]=(0,V.i)({prop:r,defaultProp:n,onChange:a}),u=i.useCallback(e=>c((t=[])=>[...t,e]),[c]),f=i.useCallback(e=>c((t=[])=>t.filter(t=>t!==e)),[c]);return(0,o.jsx)(eb,{scope:e.__scopeAccordion,value:s,onItemOpen:u,onItemClose:f,children:(0,o.jsx)(ew,{scope:e.__scopeAccordion,collapsible:!0,children:(0,o.jsx)(ek,{...l,ref:t})})})}),[eA,eP]=ey(eu),ek=i.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:n,dir:a,orientation:l="vertical",...s}=e,c=i.useRef(null),u=(0,H.s)(c,t),f=ep(r),d="ltr"===(0,ec.jH)(a),p=(0,W.m)(e.onKeyDown,e=>{if(!ef.includes(e.key))return;let t=e.target,r=f().filter(e=>!e.ref.current?.disabled),n=r.findIndex(e=>e.ref.current===t),o=r.length;if(-1===n)return;e.preventDefault();let i=n,a=o-1,s=()=>{(i=n+1)>a&&(i=0)},c=()=>{(i=n-1)<0&&(i=a)};switch(e.key){case"Home":i=0;break;case"End":i=a;break;case"ArrowRight":"horizontal"===l&&(d?s():c());break;case"ArrowDown":"vertical"===l&&s();break;case"ArrowLeft":"horizontal"===l&&(d?c():s());break;case"ArrowUp":"vertical"===l&&c()}let u=i%o;r[u].ref.current?.focus()});return(0,o.jsx)(eA,{scope:r,disabled:n,direction:a,orientation:l,children:(0,o.jsx)(ed.Slot,{scope:r,children:(0,o.jsx)(G.sG.div,{...s,"data-orientation":l,ref:u,onKeyDown:n?void 0:p})})})}),eN="AccordionItem",[eE,eM]=ey(eN),eC=i.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...i}=e,a=eP(eN,r),l=ex(eN,r),s=ev(r),c=(0,K.B)(),u=n&&l.value.includes(n)||!1,f=a.disabled||e.disabled;return(0,o.jsx)(eE,{scope:r,open:u,disabled:f,triggerId:c,children:(0,o.jsx)(er,{"data-orientation":a.orientation,"data-state":eL(u),...s,...i,ref:t,disabled:f,open:u,onOpenChange:e=>{e?l.onItemOpen(n):l.onItemClose(n)}})})});eC.displayName=eN;var eT="AccordionHeader",e_=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=eP(eu,r),a=eM(eT,r);return(0,o.jsx)(G.sG.h3,{"data-orientation":i.orientation,"data-state":eL(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});e_.displayName=eT;var eI="AccordionTrigger",eD=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=eP(eu,r),a=eM(eI,r),l=ej(eI,r),s=ev(r);return(0,o.jsx)(ed.ItemSlot,{scope:r,children:(0,o.jsx)(eo,{"aria-disabled":a.open&&!l.collapsible||void 0,"data-orientation":i.orientation,id:a.triggerId,...s,...n,ref:t})})});eD.displayName=eI;var eR="AccordionContent",eB=i.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,i=eP(eu,r),a=eM(eR,r),l=ev(r);return(0,o.jsx)(ea,{role:"region","aria-labelledby":a.triggerId,"data-orientation":i.orientation,...l,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function eL(e){return e?"open":"closed"}eB.displayName=eR;let e$=(0,P.A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ez=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(eC,{ref:r,className:(0,C.cn)("border-b",e),...t}));ez.displayName="AccordionItem";let eF=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsx)(e_,{className:"flex",children:(0,o.jsxs)(eD,{ref:n,className:(0,C.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>.accordion-trigger-chevron]:rotate-180",e),...r,children:[t,(0,o.jsx)(e$,{className:"h-4 w-4 shrink-0 transition-transform duration-200 accordion-trigger-chevron"})]})}));eF.displayName=eD.displayName;let eU=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsx)(eB,{ref:n,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...r,children:(0,o.jsx)("div",{className:(0,C.cn)("pb-4 pt-0",e),children:t})}));eU.displayName=eB.displayName;var eq=r(31355),eH=r(32547),eW=r(25028),eV=r(1359),eG=r(11490),eX=r(63376),eY=r(8730),eK="Dialog",[eZ,eJ]=(0,U.A)(eK),[eQ,e0]=eZ(eK),e1=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,c=i.useRef(null),u=i.useRef(null),[f=!1,d]=(0,V.i)({prop:n,defaultProp:a,onChange:l});return(0,o.jsx)(eQ,{scope:t,triggerRef:c,contentRef:u,contentId:(0,K.B)(),titleId:(0,K.B)(),descriptionId:(0,K.B)(),open:f,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(e=>!e),[d]),modal:s,children:r})};e1.displayName=eK;var e2="DialogTrigger",e5=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e0(e2,r),a=(0,H.s)(t,i.triggerRef);return(0,o.jsx)(G.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":td(i.open),...n,ref:a,onClick:(0,W.m)(e.onClick,i.onOpenToggle)})});e5.displayName=e2;var e4="DialogPortal",[e3,e6]=eZ(e4,{forceMount:void 0}),e8=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,l=e0(e4,t);return(0,o.jsx)(e3,{scope:t,forceMount:r,children:i.Children.map(n,e=>(0,o.jsx)(Y.C,{present:r||l.open,children:(0,o.jsx)(eW.Z,{asChild:!0,container:a,children:e})}))})};e8.displayName=e4;var e7="DialogOverlay",e9=i.forwardRef((e,t)=>{let r=e6(e7,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=e0(e7,e.__scopeDialog);return a.modal?(0,o.jsx)(Y.C,{present:n||a.open,children:(0,o.jsx)(te,{...i,ref:t})}):null});e9.displayName=e7;var te=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e0(e7,r);return(0,o.jsx)(eG.A,{as:eY.DX,allowPinchZoom:!0,shards:[i.contentRef],children:(0,o.jsx)(G.sG.div,{"data-state":td(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),tt="DialogContent",tr=i.forwardRef((e,t)=>{let r=e6(tt,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,a=e0(tt,e.__scopeDialog);return(0,o.jsx)(Y.C,{present:n||a.open,children:a.modal?(0,o.jsx)(tn,{...i,ref:t}):(0,o.jsx)(to,{...i,ref:t})})});tr.displayName=tt;var tn=i.forwardRef((e,t)=>{let r=e0(tt,e.__scopeDialog),n=i.useRef(null),a=(0,H.s)(t,r.contentRef,n);return i.useEffect(()=>{let e=n.current;if(e)return(0,eX.Eq)(e)},[]),(0,o.jsx)(ti,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,W.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,W.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,W.m)(e.onFocusOutside,e=>e.preventDefault())})}),to=i.forwardRef((e,t)=>{let r=e0(tt,e.__scopeDialog),n=i.useRef(!1),a=i.useRef(!1);return(0,o.jsx)(ti,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),ti=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,c=e0(tt,r),u=i.useRef(null),f=(0,H.s)(t,u);return(0,eV.Oh)(),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eH.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,o.jsx)(eq.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":td(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(tm,{titleId:c.titleId}),(0,o.jsx)(tv,{contentRef:u,descriptionId:c.descriptionId})]})]})}),ta="DialogTitle",tl=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e0(ta,r);return(0,o.jsx)(G.sG.h2,{id:i.titleId,...n,ref:t})});tl.displayName=ta;var ts="DialogDescription",tc=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e0(ts,r);return(0,o.jsx)(G.sG.p,{id:i.descriptionId,...n,ref:t})});tc.displayName=ts;var tu="DialogClose",tf=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=e0(tu,r);return(0,o.jsx)(G.sG.button,{type:"button",...n,ref:t,onClick:(0,W.m)(e.onClick,()=>i.onOpenChange(!1))})});function td(e){return e?"open":"closed"}tf.displayName=tu;var tp="DialogTitleWarning",[th,ty]=(0,U.q)(tp,{contentName:tt,titleName:ta,docsSlug:"dialog"}),tm=({titleId:e})=>{let t=ty(tp),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},tv=({contentRef:e,descriptionId:t})=>{let r=ty("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return i.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},tg=r(78726);let tb=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(e9,{ref:r,className:(0,C.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));tb.displayName=e9.displayName;let tx=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsxs)(e8,{children:[(0,o.jsx)(tb,{}),(0,o.jsxs)(tr,{ref:n,className:(0,C.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,o.jsxs)(tf,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,o.jsx)(tg.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));tx.displayName=tr.displayName;let tw=({className:e,...t})=>(0,o.jsx)("div",{className:(0,C.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});tw.displayName="DialogHeader";let tj=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(tl,{ref:r,className:(0,C.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));tj.displayName=tl.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(tc,{ref:r,className:(0,C.cn)("text-sm text-muted-foreground",e),...t})).displayName=tc.displayName;let tO=(0,P.A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);var tS=r(57207);function tA({subCategory:e,onEdit:t,onDelete:r,balancesVisible:n}){return(0,o.jsxs)("div",{className:"flex items-center justify-between p-2 border-b border-border/50 last:border-b-0",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("p",{className:"text-sm font-medium",children:e.name}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Allocated: ",n?`R ${e.allocatedAmount.toFixed(2)}`:"R ••••"]})]}),(0,o.jsxs)("div",{className:"flex gap-1",children:[(0,o.jsxs)(F.$,{variant:"ghost",size:"icon",onClick:t,className:"h-7 w-7",children:[(0,o.jsx)(tO,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Edit Subcategory"})]}),(0,o.jsxs)(F.$,{variant:"ghost",size:"icon",onClick:r,className:"h-7 w-7 text-destructive hover:text-destructive",children:[(0,o.jsx)(tS.A,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Delete Subcategory"})]})]})]})}var tP=r(63442),tk=r(27605),tN=r(45880),tE=r(71669);let tM=tN.Ik({name:tN.Yj().min(1,{message:"Subcategory name is required."}).max(50,{message:"Name must be 50 characters or less."}),allocatedAmount:tN.vk(e=>"string"==typeof e?parseFloat(e):e,tN.ai().min(0,{message:"Allocated amount must be a positive number."}))});function tC({onSubmit:e,initialData:t,onClose:r,parentCategoryName:n,balancesVisible:i}){let a=(0,tk.mN)({resolver:(0,tP.u)(tM),defaultValues:{name:t?.name||"",allocatedAmount:t?.allocatedAmount||0}});return(0,o.jsx)(tE.lV,{...a,children:(0,o.jsxs)("form",{onSubmit:a.handleSubmit(t=>{e(t)&&(a.reset(),r())}),className:"space-y-4",children:[(0,o.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Adding to: ",(0,o.jsx)("span",{className:"font-semibold",children:n})]}),(0,o.jsx)(tE.zB,{control:a.control,name:"name",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Subcategory Name"}),(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(O.p,{placeholder:"e.g., Fruits & Vegetables",...e})}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsx)(tE.zB,{control:a.control,name:"allocatedAmount",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Allocated Amount (R)"}),(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(O.p,{type:i?"number":"password",placeholder:i?"e.g., 100":"••••",...e,value:i?e.value:e.value>0?"••••":"0",onChange:t=>{if(i)e.onChange(""===t.target.value?0:parseFloat(t.target.value));else{let r=parseFloat(t.target.value);isNaN(r)?""===t.target.value&&e.onChange(0):e.onChange(r)}},step:"any"})}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)(F.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,o.jsx)(F.$,{type:"submit",children:t?.id?"Save Changes":"Add Subcategory"})]})]})})}let tT=(0,P.A)("ListCollapse",[["path",{d:"m3 10 2.5-2.5L3 5",key:"i6eama"}],["path",{d:"m3 19 2.5-2.5L3 14",key:"w2gmor"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}]]),t_=(0,P.A)("ListTree",[["path",{d:"M21 12h-8",key:"1bmf0i"}],["path",{d:"M21 6H8",key:"1pqkrb"}],["path",{d:"M21 18h-8",key:"1tm79t"}],["path",{d:"M3 6v4c0 1.1.9 2 2 2h3",key:"1ywdgy"}],["path",{d:"M3 10v6c0 1.1.9 2 2 2h3",key:"2wc746"}]]),tI=(0,P.A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var tD="Progress",[tR,tB]=(0,U.A)(tD),[tL,t$]=tR(tD),tz=i.forwardRef((e,t)=>{var r,n;let{__scopeProgress:i,value:a=null,max:l,getValueLabel:s=tq,...c}=e;(l||0===l)&&!tV(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=tV(l)?l:100;null===a||tG(a,u)||console.error((n=`${a}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let f=tG(a,u)?a:null,d=tW(f)?s(f,u):void 0;return(0,o.jsx)(tL,{scope:i,value:f,max:u,children:(0,o.jsx)(G.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":tW(f)?f:void 0,"aria-valuetext":d,role:"progressbar","data-state":tH(f,u),"data-value":f??void 0,"data-max":u,...c,ref:t})})});tz.displayName=tD;var tF="ProgressIndicator",tU=i.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,i=t$(tF,r);return(0,o.jsx)(G.sG.div,{"data-state":tH(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...n,ref:t})});function tq(e,t){return`${Math.round(e/t*100)}%`}function tH(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function tW(e){return"number"==typeof e}function tV(e){return tW(e)&&!isNaN(e)&&e>0}function tG(e,t){return tW(e)&&!isNaN(e)&&e<=t&&e>=0}tU.displayName=tF;let tX=i.forwardRef(({className:e,value:t,...r},n)=>(0,o.jsx)(tz,{ref:n,className:(0,C.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,o.jsx)(tU,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));tX.displayName=tz.displayName;var tY=r(29867);function tK({category:e,onAddSubCategory:t,onUpdateSubCategory:r,onDeleteSubCategory:n,balancesVisible:a,categoryIsVisible:l}){let[s,c]=(0,i.useState)(!1),[u,f]=(0,i.useState)(null),{toast:d}=(0,tY.dj)(),p=a&&l,h=e.subCategories.reduce((e,t)=>e+t.allocatedAmount,0),y=e.budget-h,m=y<0,v=e.budget>0?h/e.budget*100:0,g=e=>p?`R ${e.toFixed(2)}`:"R ••••";return(0,o.jsxs)(A.Zp,{className:"mb-3",children:[(0,o.jsx)(A.aR,{className:"flex flex-row items-start justify-between pb-2 pt-3 px-4",children:(0,o.jsxs)("div",{children:[(0,o.jsx)(A.ZB,{className:"text-base font-headline",children:e.name}),(0,o.jsxs)(A.BT,{className:"text-xs",children:["Budget: ",g(e.budget)]})]})}),(0,o.jsxs)(A.Wu,{className:"px-4 pb-3 space-y-2",children:[(0,o.jsxs)("div",{className:"text-xs space-y-1",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{children:"Subcategories Total:"}),(0,o.jsx)("span",{children:g(h)})]}),(0,o.jsx)(tX,{value:Math.min(v,100),className:m?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary"}),(0,o.jsxs)("div",{className:`flex justify-between font-medium ${m?"text-destructive":"text-green-600"}`,children:[(0,o.jsx)("span",{children:"Remaining in Budget:"}),(0,o.jsx)("span",{children:g(y)})]})]}),m&&p&&(0,o.jsxs)("div",{className:"flex items-center gap-1 text-destructive text-xs p-1.5 bg-destructive/10 rounded-md",children:[(0,o.jsx)(B.A,{className:"h-3 w-3"}),(0,o.jsx)("span",{children:"Subcategory allocations exceed this category's budget!"})]}),e.subCategories.length>0&&(0,o.jsx)(eg,{type:"single",collapsible:!0,className:"w-full text-sm",children:(0,o.jsxs)(ez,{value:`cat-${e.id}-subcategories`,className:"border-t pt-2",children:[(0,o.jsxs)(eF,{className:"py-1 text-xs hover:no-underline justify-start gap-1 group",children:[(0,o.jsx)(tT,{className:"h-3 w-3 hidden group-data-[state=open]:block"}),(0,o.jsx)(t_,{className:"h-3 w-3 block group-data-[state=open]:hidden"}),(0,o.jsxs)("span",{children:["Subcategories (",e.subCategories.length,")"]})]}),(0,o.jsx)(eU,{className:"pt-1 pb-0 pl-2 border-l ml-1.5",children:e.subCategories.map(t=>(0,o.jsx)(tA,{subCategory:t,onEdit:()=>{f(t),c(!0)},onDelete:()=>n(e.id,t.id),balancesVisible:p},t.id))})]})})]}),(0,o.jsx)(A.wL,{className:"px-4 pb-3 pt-0",children:(0,o.jsxs)(e1,{open:s,onOpenChange:e=>{c(e),e||f(null)},children:[(0,o.jsx)(e5,{asChild:!0,children:(0,o.jsxs)(F.$,{variant:"outline",size:"sm",className:"w-full text-xs",onClick:()=>{f(null),c(!0)},children:[(0,o.jsx)(tI,{className:"mr-1 h-3 w-3"})," Add Subcategory"]})}),(0,o.jsxs)(tx,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tw,{children:(0,o.jsxs)(tj,{className:"font-headline",children:[u?"Edit":"Add"," Subcategory"]})}),(0,o.jsx)(tC,{onSubmit:u?t=>{if(!u)return!1;let n=r(e.id,u.id,t.name,t.allocatedAmount);return n?(d({title:"Subcategory Updated",description:`${t.name} updated.`}),f(null),c(!1)):d({title:"Error",description:`Cannot update allocation. Exceeds remaining budget in ${e.name}.`,variant:"destructive"}),n}:r=>{let n=t(e.id,r.name,r.allocatedAmount);return n?(d({title:"Subcategory Added",description:`${r.name} added to ${e.name}.`}),c(!1)):d({title:"Error",description:`Cannot allocate ${p?"R"+r.allocatedAmount.toFixed(2):"amount"}. Exceeds remaining budget in ${e.name}.`,variant:"destructive"}),n},initialData:u||{},onClose:()=>{c(!1),f(null)},parentCategoryName:e.name,balancesVisible:p})]})]})})]})}let tZ=tN.Ik({name:tN.Yj().min(1,{message:"Category name is required."}).max(50,{message:"Name must be 50 characters or less."}),budget:tN.vk(e=>"string"==typeof e?parseFloat(e):e,tN.ai().min(0,{message:"Budget must be a positive number."}))});function tJ({onSubmit:e,initialData:t,onClose:r}){let n=(0,tk.mN)({resolver:(0,tP.u)(tZ),defaultValues:{name:t?.name||"",budget:t?.budget||0}});return(0,o.jsx)(tE.lV,{...n,children:(0,o.jsxs)("form",{onSubmit:n.handleSubmit(t=>{e(t),n.reset(),r()}),className:"space-y-4",children:[(0,o.jsx)(tE.zB,{control:n.control,name:"name",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Category Name"}),(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(O.p,{placeholder:"e.g., Groceries",...e})}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsx)(tE.zB,{control:n.control,name:"budget",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Budget Amount (R)"}),(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(O.p,{type:"number",placeholder:"e.g., 500",...e,step:"any"})}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)(F.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,o.jsx)(F.$,{type:"submit",children:t?.id?"Save Changes":"Add Category"})]})]})})}var tQ="AlertDialog",[t0,t1]=(0,U.A)(tQ,[eJ]),t2=eJ(),t5=e=>{let{__scopeAlertDialog:t,...r}=e,n=t2(t);return(0,o.jsx)(e1,{...n,...r,modal:!0})};t5.displayName=tQ,i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=t2(r);return(0,o.jsx)(e5,{...i,...n,ref:t})}).displayName="AlertDialogTrigger";var t4=e=>{let{__scopeAlertDialog:t,...r}=e,n=t2(t);return(0,o.jsx)(e8,{...n,...r})};t4.displayName="AlertDialogPortal";var t3=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=t2(r);return(0,o.jsx)(e9,{...i,...n,ref:t})});t3.displayName="AlertDialogOverlay";var t6="AlertDialogContent",[t8,t7]=t0(t6),t9=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...a}=e,l=t2(r),s=i.useRef(null),c=(0,H.s)(t,s),u=i.useRef(null);return(0,o.jsx)(th,{contentName:t6,titleName:re,docsSlug:"alert-dialog",children:(0,o.jsx)(t8,{scope:r,cancelRef:u,children:(0,o.jsxs)(tr,{role:"alertdialog",...l,...a,ref:c,onOpenAutoFocus:(0,W.m)(a.onOpenAutoFocus,e=>{e.preventDefault(),u.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,o.jsx)(eY.xV,{children:n}),(0,o.jsx)(rl,{contentRef:s})]})})})});t9.displayName=t6;var re="AlertDialogTitle",rt=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=t2(r);return(0,o.jsx)(tl,{...i,...n,ref:t})});rt.displayName=re;var rr="AlertDialogDescription",rn=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=t2(r);return(0,o.jsx)(tc,{...i,...n,ref:t})});rn.displayName=rr;var ro=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,i=t2(r);return(0,o.jsx)(tf,{...i,...n,ref:t})});ro.displayName="AlertDialogAction";var ri="AlertDialogCancel",ra=i.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:i}=t7(ri,r),a=t2(r),l=(0,H.s)(t,i);return(0,o.jsx)(tf,{...a,...n,ref:l})});ra.displayName=ri;var rl=({contentRef:e})=>{let t=`\`${t6}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${t6}\` by passing a \`${rr}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${t6}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return i.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null};let rs=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(t3,{className:(0,C.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));rs.displayName=t3.displayName;let rc=i.forwardRef(({className:e,...t},r)=>(0,o.jsxs)(t4,{children:[(0,o.jsx)(rs,{}),(0,o.jsx)(t9,{ref:r,className:(0,C.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));rc.displayName=t9.displayName;let ru=({className:e,...t})=>(0,o.jsx)("div",{className:(0,C.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});ru.displayName="AlertDialogHeader";let rf=({className:e,...t})=>(0,o.jsx)("div",{className:(0,C.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});rf.displayName="AlertDialogFooter";let rd=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(rt,{ref:r,className:(0,C.cn)("text-lg font-semibold",e),...t}));rd.displayName=rt.displayName;let rp=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(rn,{ref:r,className:(0,C.cn)("text-sm text-muted-foreground",e),...t}));rp.displayName=rn.displayName;let rh=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(ro,{ref:r,className:(0,C.cn)((0,F.r)(),e),...t}));rh.displayName=ro.displayName;let ry=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(ra,{ref:r,className:(0,C.cn)((0,F.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));ry.displayName=ra.displayName;let rm=(0,P.A)("LayoutList",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}],["path",{d:"M14 4h7",key:"3xa0d5"}],["path",{d:"M14 9h7",key:"1icrd9"}],["path",{d:"M14 15h7",key:"1mj8o2"}],["path",{d:"M14 20h7",key:"11slyb"}]]),rv=(0,P.A)("FolderPlus",[["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"M9 13h6",key:"1uhe8q"}],["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]),rg=(0,P.A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var rb=r(76311),rx=r(11003);function rw({categories:e,onAddCategory:t,onUpdateCategory:r,onDeleteCategory:n,onAddSubCategory:a,onUpdateSubCategory:l,onDeleteSubCategory:s,onToggleCategoryVisibility:c,balancesVisible:u}){let[f,d]=(0,i.useState)(!1),[p,h]=(0,i.useState)(null),[y,m]=(0,i.useState)(null),{toast:v}=(0,tY.dj)();return(0,o.jsx)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},children:(0,o.jsxs)(A.Zp,{className:"card-modern hover-glow group",children:[(0,o.jsxs)(A.aR,{className:"flex flex-row items-center justify-between pb-4",children:[(0,o.jsxs)(w.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.4,type:"spring"},className:"flex items-center gap-3",children:[(0,o.jsxs)(A.ZB,{className:"text-xl flex items-center gap-3 font-headline text-gradient-primary",children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatDelay:4},className:"p-2 rounded-xl bg-gradient-to-r from-primary/20 to-accent/20 group-hover:from-primary/30 group-hover:to-accent/30 transition-all duration-300",children:(0,o.jsx)(rm,{className:"h-6 w-6 text-primary"})}),"Budget Categories"]}),e.length>0&&(0,o.jsx)(w.P.div,{initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:.6},className:"px-3 py-1 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 border border-primary/30",children:(0,o.jsx)("span",{className:"text-xs font-semibold text-primary",children:e.length})})]}),(0,o.jsx)(w.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.5},children:(0,o.jsxs)(e1,{open:f,onOpenChange:e=>{d(e),e||h(null)},children:[(0,o.jsx)(e5,{asChild:!0,children:(0,o.jsx)(w.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,o.jsxs)(F.$,{size:"sm",className:"btn-gradient-primary",onClick:()=>{h(null),d(!0)},children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,180,360]},transition:{duration:2,repeat:1/0,ease:"linear"},className:"mr-2",children:(0,o.jsx)(tI,{className:"h-4 w-4"})}),"Add Category"]})})}),(0,o.jsxs)(tx,{className:"sm:max-w-[425px] glass-premium",children:[(0,o.jsx)(tw,{children:(0,o.jsxs)(tj,{className:"font-headline text-gradient-primary flex items-center gap-2",children:[(0,o.jsx)(rv,{className:"h-5 w-5"}),p?"Edit":"Add"," Category"]})}),(0,o.jsx)(tJ,{onSubmit:p?e=>{p&&(r(p.id,e.name,e.budget),v({title:"Category Updated",description:`${e.name} has been updated.`}),h(null),d(!1))}:e=>{t(e.name,e.budget),v({title:"Category Added",description:`${e.name} has been added.`}),d(!1)},initialData:p||{},onClose:()=>{d(!1),h(null)}})]})]})})]}),(0,o.jsx)(A.Wu,{className:"pt-4",children:0===e.length?(0,o.jsxs)(w.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.7},className:"text-center py-12",children:[(0,o.jsx)(w.P.div,{animate:{y:[0,-10,0]},transition:{duration:2,repeat:1/0},className:"inline-block p-6 rounded-full bg-gradient-to-r from-muted/20 to-muted/10 mb-4",children:(0,o.jsx)(rv,{className:"h-12 w-12 text-muted-foreground"})}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:"No Categories Yet"}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Start organizing your budget by creating your first category"}),(0,o.jsx)(w.P.div,{animate:{scale:[1,1.05,1]},transition:{duration:2,repeat:1/0,repeatDelay:3},children:(0,o.jsxs)(F.$,{className:"btn-gradient-accent",onClick:()=>{h(null),d(!0)},children:[(0,o.jsx)(rg,{className:"mr-2 h-4 w-4"}),"Create Your First Category"]})})]}):(0,o.jsx)(w.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"space-y-4",children:(0,o.jsx)(x,{mode:"popLayout",children:e.map((e,t)=>(0,o.jsx)(w.P.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{duration:.4,delay:.1*t,type:"spring",stiffness:300,damping:30},layout:!0,className:"relative group",children:(0,o.jsxs)("div",{className:"card-modern p-1 hover:shadow-lg transition-all duration-300",children:[(0,o.jsx)(tK,{category:e,onUpdateCategory:r,onDeleteCategory:n,onAddSubCategory:a,onUpdateSubCategory:l,onDeleteSubCategory:s,balancesVisible:u,categoryIsVisible:e.isVisible??!0,onToggleVisibility:()=>c(e.id)}),(0,o.jsxs)(w.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:0,scale:.8},whileHover:{opacity:1,scale:1},transition:{duration:.2},className:"absolute top-3 right-3 flex gap-1 group-hover:opacity-100 group-hover:scale-100",children:[(0,o.jsx)(w.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,o.jsxs)(F.$,{variant:"ghost",size:"icon",className:"h-8 w-8 glass-premium hover:bg-primary/20 border border-border/50",onClick:()=>c(e.id),children:[(0,o.jsx)(w.P.div,{animate:{rotate:e.isVisible??!0?0:180},transition:{duration:.3},children:e.isVisible??!0?(0,o.jsx)(rb.A,{className:"h-4 w-4"}):(0,o.jsx)(rx.A,{className:"h-4 w-4"})}),(0,o.jsx)("span",{className:"sr-only",children:e.isVisible??!0?"Hide Category Balances":"Show Category Balances"})]})}),(0,o.jsx)(w.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,o.jsxs)(F.$,{variant:"ghost",size:"icon",className:"h-8 w-8 glass-premium hover:bg-accent/20 border border-border/50",onClick:()=>{h(e),d(!0)},children:[(0,o.jsx)(tO,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"sr-only",children:"Edit Category"})]})}),(0,o.jsx)(w.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,o.jsxs)(F.$,{variant:"ghost",size:"icon",className:"h-8 w-8 glass-premium hover:bg-destructive/20 border border-border/50 text-destructive hover:text-destructive",onClick:()=>m(e.id),children:[(0,o.jsx)(w.P.div,{whileHover:{rotate:[0,10,-10,0]},transition:{duration:.3},children:(0,o.jsx)(tS.A,{className:"h-4 w-4"})}),(0,o.jsx)("span",{className:"sr-only",children:"Delete Category"})]})})]})]})},e.id))})})}),(0,o.jsx)(t5,{open:!!y,onOpenChange:()=>m(null),children:(0,o.jsxs)(rc,{className:"glass-premium",children:[(0,o.jsxs)(ru,{children:[(0,o.jsxs)(rd,{className:"flex items-center gap-2 text-destructive",children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.5,repeat:1/0},children:(0,o.jsx)(tS.A,{className:"h-5 w-5"})}),"Delete Category?"]}),(0,o.jsxs)(rp,{className:"text-muted-foreground",children:["This action will permanently delete the category",e.find(e=>e.id===y)?.subCategories.length?" and all its subcategories":"",". This cannot be undone."]})]}),(0,o.jsxs)(rf,{className:"gap-2",children:[(0,o.jsx)(w.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,o.jsx)(ry,{className:"btn-glass",children:"Cancel"})}),(0,o.jsx)(w.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,o.jsxs)(rh,{onClick:()=>{if(y){let t=e.find(e=>e.id===y);n(y),v({title:"Category Deleted",description:`${t?.name||"Category"} has been deleted.`}),m(null)}},className:"bg-gradient-to-r from-destructive to-destructive/80 hover:from-destructive/90 hover:to-destructive/70 text-white",children:[(0,o.jsx)(tS.A,{className:"mr-2 h-4 w-4"}),"Delete Forever"]})})]})]})})]})})}let rj=(0,P.A)("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);var rO=r(49384),rS=r(45603),rA=r.n(rS),rP=r(63866),rk=r.n(rP),rN=r(77822),rE=r.n(rN),rM=r(40491),rC=r.n(rM),rT=r(93490),r_=r.n(rT),rI=function(e){return 0===e?0:e>0?1:-1},rD=function(e){return rk()(e)&&e.indexOf("%")===e.length-1},rR=function(e){return r_()(e)&&!rE()(e)},rB=function(e){return rR(e)||rk()(e)},rL=0,r$=function(e){var t=++rL;return"".concat(e||"").concat(t)},rz=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!rR(e)&&!rk()(e))return n;if(rD(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return rE()(r)&&(r=n),o&&r>t&&(r=t),r},rF=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},rU=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},rq=function(e,t){return rR(e)&&rR(t)?function(r){return e+r*(t-e)}:function(){return t}};function rH(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):rC()(e,t))===r}):null}var rW=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},rV=r(37456),rG=r.n(rV),rX=r(5231),rY=r.n(rX),rK=r(55048),rZ=r.n(rK),rJ=r(29632);function rQ(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function r0(e){return(r0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r1=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],r2=["points","pathLength"],r5={svg:["viewBox","children"],polygon:r2,polyline:r2},r4=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],r3=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,i.isValidElement)(e)&&(r=e.props),!rZ()(r))return null;var n={};return Object.keys(r).forEach(function(e){r4.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},r6=function(e,t,r){if(!rZ()(e)||"object"!==r0(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];r4.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},r8=["children"],r7=["children"];function r9(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var ne={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},nt=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},nr=null,nn=null,no=function e(t){if(t===nr&&Array.isArray(nn))return nn;var r=[];return i.Children.forEach(t,function(t){rG()(t)||((0,rJ.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),nn=r,nr=t,r};function ni(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return nt(e)}):[nt(t)],no(e).forEach(function(e){var t=rC()(e,"type.displayName")||rC()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function na(e,t){var r=ni(e,t);return r&&r[0]}var nl=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!rR(r)&&!(r<=0)&&!!rR(n)&&!(n<=0)},ns=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],nc=function(e,t,r,n){var o,i=null!==(o=null==r5?void 0:r5[n])&&void 0!==o?o:[];return!rY()(e)&&(n&&i.includes(t)||r1.includes(t))||r&&r4.includes(t)},nu=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,i.isValidElement)(e)&&(n=e.props),!rZ()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;nc(null===(i=n)||void 0===i?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},nf=function e(t,r){if(t===r)return!0;var n=i.Children.count(t);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return nd(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=t[o],l=r[o];if(Array.isArray(a)||Array.isArray(l)){if(!e(a,l))return!1}else if(!nd(a,l))return!1}return!0},nd=function(e,t){if(rG()(e)&&rG()(t))return!0;if(!rG()(e)&&!rG()(t)){var r=e.props||{},n=r.children,o=r9(r,r8),i=t.props||{},a=i.children,l=r9(i,r7);if(n&&a)return rQ(o,l)&&nf(n,a);if(!n&&!a)return rQ(o,l)}return!1},np=function(e,t){var r=[],n={};return no(e).forEach(function(e,o){var i;if((i=e)&&i.type&&rk()(i.type)&&ns.indexOf(i.type)>=0)r.push(e);else if(e){var a=nt(e.type),l=t[a]||{},s=l.handler,c=l.once;if(s&&(!c||!n[a])){var u=s(e,a,o);r.push(u),n[a]=!0}}}),r},nh=function(e){var t=e&&e.type;return t&&ne[t]?ne[t]:null};function ny(e){return(ny="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nm(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ny(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ny(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ny(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ng(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var nb=(0,i.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,l=void 0===o?{width:-1,height:-1}:o,s=e.width,c=void 0===s?"100%":s,u=e.height,f=void 0===u?"100%":u,d=e.minWidth,p=void 0===d?0:d,h=e.minHeight,y=e.maxHeight,m=e.children,v=e.debounce,g=void 0===v?0:v,b=e.id,x=e.className,w=e.onResize,j=e.style,O=(0,i.useRef)(null),S=(0,i.useRef)();S.current=w,(0,i.useImperativeHandle)(t,function(){return Object.defineProperty(O.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),O.current},configurable:!0})});var A=function(e){if(Array.isArray(e))return e}(r=(0,i.useState)({containerWidth:l.width,containerHeight:l.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(r,2)||function(e,t){if(e){if("string"==typeof e)return ng(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ng(e,t)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),P=A[0],k=A[1],N=(0,i.useCallback)(function(e,t){k(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;N(n,o),null===(t=S.current)||void 0===t||t.call(S,n,o)};g>0&&(e=rA()(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=O.current.getBoundingClientRect();return N(r.width,r.height),t.observe(O.current),function(){t.disconnect()}},[N,g]);var E=(0,i.useMemo)(function(){var e=P.containerWidth,t=P.containerHeight;if(e<0||t<0)return null;rW(rD(c)||rD(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),rW(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=rD(c)?e:c,o=rD(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),rW(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,c,f,p,h,n);var l=!Array.isArray(m)&&nt(m.type).endsWith("Chart");return a().Children.map(m,function(e){return a().isValidElement(e)?(0,i.cloneElement)(e,nv({width:r,height:o},l?{style:nv({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,m,f,y,h,p,P,c]);return a().createElement("div",{id:b?"".concat(b):void 0,className:(0,rO.A)("recharts-responsive-container",x),style:nv(nv({},void 0===j?{}:j),{},{width:c,height:f,minWidth:p,minHeight:h,maxHeight:y}),ref:O},E)}),nx=r(85938),nw=r.n(nx);function nj(e){return(nj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nO(){return(nO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nS(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nA(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=nj(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nj(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nk(e){return Array.isArray(e)&&rB(e[0])&&rB(e[1])?e.join(" ~ "):e}var nN=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,l=e.labelStyle,s=e.payload,c=e.formatter,u=e.itemSorter,f=e.wrapperClassName,d=e.labelClassName,p=e.label,h=e.labelFormatter,y=e.accessibilityLayer,m=nP({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),v=nP({margin:0},void 0===l?{}:l),g=!rG()(p),b=g?p:"",x=(0,rO.A)("recharts-default-tooltip",f),w=(0,rO.A)("recharts-tooltip-label",d);return g&&h&&null!=s&&(b=h(p,s)),a().createElement("div",nO({className:x,style:m},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:v},a().isValidElement(b)?b:"".concat(b)),function(){if(s&&s.length){var e=(u?nw()(s,u):s).map(function(e,t){if("none"===e.type)return null;var n=nP({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||c||nk,l=e.value,u=e.name,f=l,d=u;if(o&&null!=f&&null!=d){var p=o(l,u,e,t,s);if(Array.isArray(p)){var h=function(e){if(Array.isArray(e))return e}(p)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(p,2)||function(e,t){if(e){if("string"==typeof e)return nS(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nS(e,t)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],d=h[1]}else f=p}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},rB(d)?a().createElement("span",{className:"recharts-tooltip-item-name"},d):null,rB(d)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function nE(e){return(nE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nM(e,t,r){var n;return(n=function(e,t){if("object"!=nE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==nE(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var nC="recharts-tooltip-wrapper",nT={visibility:"hidden"};function n_(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,l=e.tooltipDimension,s=e.viewBox,c=e.viewBoxDimension;if(i&&rR(i[n]))return i[n];var u=r[n]-l-o,f=r[n]+o;return t[n]?a[n]?u:f:a[n]?u<s[n]?Math.max(f,s[n]):Math.max(u,s[n]):f+l>s[n]+c?Math.max(u,s[n]):Math.max(f,s[n])}function nI(e){return(nI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nD(Object(r),!0).forEach(function(t){nz(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nB(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(nB=function(){return!!e})()}function nL(e){return(nL=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function n$(e,t){return(n$=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function nz(e,t,r){return(t=nF(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nF(e){var t=function(e,t){if("object"!=nI(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nI(t)?t:t+""}var nU=function(e){var t;function r(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r);for(var e,t,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=nL(t),nz(e=function(e,t){if(t&&("object"===nI(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,nB()?Reflect.construct(t,n||[],nL(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),nz(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=e.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=e.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n$(e,t)}(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,l,s,c,u,f,d,p,h,y,m,v,g,b,x=this,w=this.props,j=w.active,O=w.allowEscapeViewBox,S=w.animationDuration,A=w.animationEasing,P=w.children,k=w.coordinate,N=w.hasPayload,E=w.isAnimationActive,M=w.offset,C=w.position,T=w.reverseDirection,_=w.useTranslate3d,I=w.viewBox,D=w.wrapperStyle,R=(d=(e={allowEscapeViewBox:O,coordinate:k,offsetTopLeft:M,position:C,reverseDirection:T,tooltipBox:this.state.lastBoundingBox,useTranslate3d:_,viewBox:I}).allowEscapeViewBox,p=e.coordinate,h=e.offsetTopLeft,y=e.position,m=e.reverseDirection,v=e.tooltipBox,g=e.useTranslate3d,b=e.viewBox,v.height>0&&v.width>0&&p?(r=(t={translateX:u=n_({allowEscapeViewBox:d,coordinate:p,key:"x",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.width,viewBox:b,viewBoxDimension:b.width}),translateY:f=n_({allowEscapeViewBox:d,coordinate:p,key:"y",offsetTopLeft:h,position:y,reverseDirection:m,tooltipDimension:v.height,viewBox:b,viewBoxDimension:b.height}),useTranslate3d:g}).translateX,n=t.translateY,c={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):c=nT,{cssProperties:c,cssClasses:(i=(o={translateX:u,translateY:f,coordinate:p}).coordinate,l=o.translateX,s=o.translateY,(0,rO.A)(nC,nM(nM(nM(nM({},"".concat(nC,"-right"),rR(l)&&i&&rR(i.x)&&l>=i.x),"".concat(nC,"-left"),rR(l)&&i&&rR(i.x)&&l<i.x),"".concat(nC,"-bottom"),rR(s)&&i&&rR(i.y)&&s>=i.y),"".concat(nC,"-top"),rR(s)&&i&&rR(i.y)&&s<i.y)))}),B=R.cssClasses,L=R.cssProperties,$=nR(nR({transition:E&&j?"transform ".concat(S,"ms ").concat(A):void 0},L),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&N?"visible":"hidden",position:"absolute",top:0,left:0},D);return a().createElement("div",{tabIndex:-1,className:B,style:$,ref:function(e){x.wrapperNode=e}},P)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nF(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),nq={isSsr:!0,get:function(e){return nq[e]},set:function(e,t){if("string"==typeof e)nq[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){nq[t]=e[t]})}}},nH=r(36315),nW=r.n(nH);function nV(e,t,r){return!0===t?nW()(e,r):rY()(t)?nW()(e,t):e}function nG(e){return(nG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nX(Object(r),!0).forEach(function(t){nQ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nK(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(nK=function(){return!!e})()}function nZ(e){return(nZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function nJ(e,t){return(nJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function nQ(e,t,r){return(t=n0(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n0(e){var t=function(e,t){if("object"!=nG(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nG(t)?t:t+""}function n1(e){return e.dataKey}var n2=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=nZ(e),function(e,t){if(t&&("object"===nG(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,nK()?Reflect.construct(e,t||[],nZ(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nJ(e,t)}(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,l=r.animationEasing,s=r.content,c=r.coordinate,u=r.filterNull,f=r.isAnimationActive,d=r.offset,p=r.payload,h=r.payloadUniqBy,y=r.position,m=r.reverseDirection,v=r.useTranslate3d,g=r.viewBox,b=r.wrapperStyle,x=null!=p?p:[];u&&x.length&&(x=nV(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,n1));var w=x.length>0;return a().createElement(nU,{allowEscapeViewBox:o,animationDuration:i,animationEasing:l,isAnimationActive:f,active:n,coordinate:c,hasPayload:w,offset:d,position:y,reverseDirection:m,useTranslate3d:v,viewBox:g,wrapperStyle:b},(e=nY(nY({},this.props),{},{payload:x}),a().isValidElement(s)?a().cloneElement(s,e):"function"==typeof s?a().createElement(s,e):a().createElement(nN,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n0(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);nQ(n2,"displayName","Tooltip"),nQ(n2,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!nq.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var n5=["children","width","height","viewBox","className","style","title","desc"];function n4(){return(n4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function n3(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,l=e.style,s=e.title,c=e.desc,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,n5),f=o||{width:r,height:n,x:0,y:0},d=(0,rO.A)("recharts-surface",i);return a().createElement("svg",n4({},nu(u,!0,"svg"),{className:d,width:r,height:n,style:l,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,s),a().createElement("desc",null,c),t)}var n6=r(69433),n8=r.n(n6);let n7=Math.cos,n9=Math.sin,oe=Math.sqrt,ot=Math.PI,or=2*ot,on={draw(e,t){let r=oe(t/ot);e.moveTo(r,0),e.arc(0,0,r,0,or)}},oo=oe(1/3),oi=2*oo,oa=n9(ot/10)/n9(7*ot/10),ol=n9(or/10)*oa,os=-n7(or/10)*oa,oc=oe(3),ou=oe(3)/2,of=1/oe(12),od=(of/2+1)*3;function op(e){return function(){return e}}let oh=Math.PI,oy=2*oh,om=oy-1e-6;function ov(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class og{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?ov:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return ov;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e*=1,t*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,l=r-e,s=n-t,c=i-e,u=a-t,f=c*c+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(u*l-s*c)>1e-6&&o){let d=r-i,p=n-a,h=l*l+s*s,y=Math.sqrt(h),m=Math.sqrt(f),v=o*Math.tan((oh-Math.acos((h+f-(d*d+p*p))/(2*y*m)))/2),g=v/m,b=v/y;Math.abs(g-1)>1e-6&&this._append`L${e+g*c},${t+g*u}`,this._append`A${o},${o},0,0,${+(u*d>c*p)},${this._x1=e+b*l},${this._y1=t+b*s}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,o,i){if(e*=1,t*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),l=r*Math.sin(n),s=e+a,c=t+l,u=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${s},${c}`:(Math.abs(this._x1-s)>1e-6||Math.abs(this._y1-c)>1e-6)&&this._append`L${s},${c}`,r&&(f<0&&(f=f%oy+oy),f>om?this._append`A${r},${r},0,1,${u},${e-a},${t-l}A${r},${r},0,1,${u},${this._x1=s},${this._y1=c}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=oh)},${u},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function ob(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new og(t)}function ox(e){return(ox="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}og.prototype,oe(3),oe(3);var ow=["type","size","sizeType"];function oj(){return(oj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oO(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ox(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ox(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ox(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var oA={symbolCircle:on,symbolCross:{draw(e,t){let r=oe(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=oe(t/oi),n=r*oo;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=oe(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=oe(.8908130915292852*t),n=ol*r,o=os*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=or*t/5,a=n7(i),l=n9(i);e.lineTo(l*r,-a*r),e.lineTo(a*n-l*o,l*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-oe(t/(3*oc));e.moveTo(0,2*r),e.lineTo(-oc*r,-r),e.lineTo(oc*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=oe(t/od),n=r/2,o=r*of,i=r*of+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-ou*o,ou*n+-.5*o),e.lineTo(-.5*n-ou*i,ou*n+-.5*i),e.lineTo(-.5*a-ou*i,ou*a+-.5*i),e.lineTo(-.5*n+ou*o,-.5*o-ou*n),e.lineTo(-.5*n+ou*i,-.5*i-ou*n),e.lineTo(-.5*a+ou*i,-.5*i-ou*a),e.closePath()}}},oP=Math.PI/180,ok=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*oP;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},oN=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,l=e.sizeType,s=void 0===l?"area":l,c=oS(oS({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ow)),{},{type:n,size:i,sizeType:s}),u=c.className,f=c.cx,d=c.cy,p=nu(c,!0);return f===+f&&d===+d&&i===+i?a().createElement("path",oj({},p,{className:(0,rO.A)("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(d,")"),d:(t=oA["symbol".concat(n8()(n))]||on,(function(e,t){let r=null,n=ob(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:op(e||on),t="function"==typeof t?t:op(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:op(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:op(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(ok(i,s,n))())})):null};function oE(e){return(oE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function oM(){return(oM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function oC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}oN.registerSymbol=function(e,t){oA["symbol".concat(n8()(e))]=t};function oT(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(oT=function(){return!!e})()}function o_(e){return(o_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function oI(e,t){return(oI=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function oD(e,t,r){return(t=oR(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oR(e){var t=function(e,t){if("object"!=oE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oE(t)?t:t+""}var oB=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=o_(e),function(e,t){if(t&&("object"===oE(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,oT()?Reflect.construct(e,t||[],o_(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oI(e,t)}(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oC(Object(r),!0).forEach(function(t){oD(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,a().cloneElement(e.legendIcon,i)}return a().createElement(oN,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,l=t.inactiveColor,s={x:0,y:0,width:32,height:32},c={display:"horizontal"===o?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,rO.A)(oD(oD({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var d=rY()(t.value)?null:t.value;rW(!rY()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var p=t.inactive?l:t.color;return a().createElement("li",oM({className:f,style:c,key:"legend-item-".concat(r)},r6(e.props,t,r)),a().createElement(n3,{width:n,height:n,viewBox:s,style:u},e.renderIcon(t)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:p}},o?o(d,t,r):d))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,oR(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function oL(e){return(oL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}oD(oB,"displayName","Legend"),oD(oB,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var o$=["ref"];function oz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function oF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?oz(Object(r),!0).forEach(function(t){oV(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function oU(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,oG(n.key),n)}}function oq(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(oq=function(){return!!e})()}function oH(e){return(oH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function oW(e,t){return(oW=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function oV(e,t,r){return(t=oG(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oG(e){var t=function(e,t){if("object"!=oL(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=oL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oL(t)?t:t+""}function oX(e){return e.value}var oY=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=oH(t),oV(e=function(e,t){if(t&&("object"===oL(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,oq()?Reflect.construct(t,r||[],oH(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&oW(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?oF({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,l=n.margin,s=n.chartWidth,c=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((s||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:l&&l.right||0}:{left:l&&l.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((c||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:l&&l.bottom||0}:{top:l&&l.top||0}),oF(oF({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,l=t.payloadUniqBy,s=t.payload,c=oF(oF({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:c,ref:function(t){e.wrapperNode=t}},function(e,t){if(a().isValidElement(e))return a().cloneElement(e,t);if("function"==typeof e)return a().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,o$);return a().createElement(oB,r)}(r,oF(oF({},this.props),{},{payload:nV(s,l,oX)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=oF(oF({},this.defaultProps),e.props).layout;return"vertical"===r&&rR(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&oU(n.prototype,t),r&&oU(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);oV(oY,"displayName","Legend"),oV(oY,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});let oK={light:"",dark:".dark"},oZ=i.createContext(null);function oJ(){let e=i.useContext(oZ);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let oQ=i.forwardRef(({id:e,className:t,children:r,config:n,...a},l)=>{let s=i.useId(),c=`chart-${e||s.replace(/:/g,"")}`;return(0,o.jsx)(oZ.Provider,{value:{config:n},children:(0,o.jsxs)("div",{"data-chart":c,ref:l,className:(0,C.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",t),...a,children:[(0,o.jsx)(o0,{id:c,config:n}),(0,o.jsx)(nb,{children:r})]})})});oQ.displayName="Chart";let o0=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(oK).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},o1=i.forwardRef(({active:e,payload:t,className:r,indicator:n="dot",hideLabel:a=!1,hideIndicator:l=!1,label:s,labelFormatter:c,labelClassName:u,formatter:f,color:d,nameKey:p,labelKey:h},y)=>{let{config:m}=oJ(),v=i.useMemo(()=>{if(a||!t?.length)return null;let[e]=t,r=`${h||e.dataKey||e.name||"value"}`,n=o5(m,e,r),i=h||"string"!=typeof s?n?.label:m[s]?.label||s;return c?(0,o.jsx)("div",{className:(0,C.cn)("font-medium",u),children:c(i,t)}):i?(0,o.jsx)("div",{className:(0,C.cn)("font-medium",u),children:i}):null},[s,c,t,a,u,m,h]);if(!e||!t?.length)return null;let g=1===t.length&&"dot"!==n;return(0,o.jsxs)("div",{ref:y,className:(0,C.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",r),children:[g?null:v,(0,o.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${p||e.name||e.dataKey||"value"}`,i=o5(m,e,r),a=d||e.payload.fill||e.color;return(0,o.jsx)("div",{className:(0,C.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===n&&"items-center"),children:f&&e?.value!==void 0&&e.name?f(e.value,e.name,e,t,e.payload):(0,o.jsxs)(o.Fragment,{children:[i?.icon?(0,o.jsx)(i.icon,{}):!l&&(0,o.jsx)("div",{className:(0,C.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===n,"w-1":"line"===n,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===n,"my-0.5":g&&"dashed"===n}),style:{"--color-bg":a,"--color-border":a}}),(0,o.jsxs)("div",{className:(0,C.cn)("flex flex-1 justify-between leading-none",g?"items-end":"items-center"),children:[(0,o.jsxs)("div",{className:"grid gap-1.5",children:[g?v:null,(0,o.jsx)("span",{className:"text-muted-foreground",children:i?.label||e.name})]}),e.value&&(0,o.jsx)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});o1.displayName="ChartTooltip";let o2=i.forwardRef(({className:e,hideIcon:t=!1,payload:r,verticalAlign:n="bottom",nameKey:i},a)=>{let{config:l}=oJ();return r?.length?(0,o.jsx)("div",{ref:a,className:(0,C.cn)("flex items-center justify-center gap-4","top"===n?"pb-3":"pt-3",e),children:r.map(e=>{let r=`${i||e.dataKey||"value"}`,n=o5(l,e,r);return(0,o.jsxs)("div",{className:(0,C.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[n?.icon&&!t?(0,o.jsx)(n.icon,{}):(0,o.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),n?.label]},e.value)})}):null});function o5(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,o=r;return r in t&&"string"==typeof t[r]?o=t[r]:n&&r in n&&"string"==typeof n[r]&&(o=n[r]),o in e?e[o]:e[r]}o2.displayName="ChartLegend";var o4=r(34990),o3=r.n(o4);function o6(e,t){if(!e)throw Error("Invariant failed")}var o8=["children","className"];function o7(){return(o7=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var o9=a().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,o8),i=(0,rO.A)("recharts-layer",n);return a().createElement("g",o7({className:i},nu(o,!0),{ref:t}),r)});function ie(){return(ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var it=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,i=(0,rO.A)("recharts-dot",o);return t===+t&&r===+r&&n===+n?a().createElement("circle",ie({},nu(e,!1),r3(e),{className:i,cx:t,cy:r,r:n})):null},ir=r(87955),io=r.n(ir),ii=Object.getOwnPropertyNames,ia=Object.getOwnPropertySymbols,il=Object.prototype.hasOwnProperty;function is(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function ic(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var l=e(t,r,n);return o.delete(t),o.delete(r),l}}function iu(e){return ii(e).concat(ia(e))}var id=Object.hasOwn||function(e,t){return il.call(e,t)};function ip(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var ih=Object.getOwnPropertyDescriptor,iy=Object.keys;function im(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function iv(e,t){return ip(e.getTime(),t.getTime())}function ig(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function ib(e,t){return e===t}function ix(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),l=e.entries(),s=0;(n=l.next())&&!n.done;){for(var c=t.entries(),u=!1,f=0;(o=c.next())&&!o.done;){if(a[f]){f++;continue}var d=n.value,p=o.value;if(r.equals(d[0],p[0],s,f,e,t,r)&&r.equals(d[1],p[1],d[0],p[0],e,t,r)){u=a[f]=!0;break}f++}if(!u)return!1;s++}return!0}function iw(e,t,r){var n=iy(e),o=n.length;if(iy(t).length!==o)return!1;for(;o-- >0;)if(!iN(e,t,r,n[o]))return!1;return!0}function ij(e,t,r){var n,o,i,a=iu(e),l=a.length;if(iu(t).length!==l)return!1;for(;l-- >0;)if(!iN(e,t,r,n=a[l])||(o=ih(e,n),i=ih(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function iO(e,t){return ip(e.valueOf(),t.valueOf())}function iS(e,t){return e.source===t.source&&e.flags===t.flags}function iA(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),l=e.values();(n=l.next())&&!n.done;){for(var s=t.values(),c=!1,u=0;(o=s.next())&&!o.done;){if(!a[u]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){c=a[u]=!0;break}u++}if(!c)return!1}return!0}function iP(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function ik(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function iN(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||id(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var iE=Array.isArray,iM="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,iC=Object.assign,iT=Object.prototype.toString.call.bind(Object.prototype.toString),i_=iI();function iI(e){void 0===e&&(e={});var t,r,n,o,i,a,l,s,c,u,f,d,p,h=e.circular,y=e.createInternalComparator,m=e.createState,v=e.strict,g=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?ij:im,areDatesEqual:iv,areErrorsEqual:ig,areFunctionsEqual:ib,areMapsEqual:n?is(ix,ij):ix,areNumbersEqual:ip,areObjectsEqual:n?ij:iw,arePrimitiveWrappersEqual:iO,areRegExpsEqual:iS,areSetsEqual:n?is(iA,ij):iA,areTypedArraysEqual:n?ij:iP,areUrlsEqual:ik};if(r&&(o=iC({},o,r(o))),t){var i=ic(o.areArraysEqual),a=ic(o.areMapsEqual),l=ic(o.areObjectsEqual),s=ic(o.areSetsEqual);o=iC({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:l,areSetsEqual:s})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,l=t.areNumbersEqual,s=t.areObjectsEqual,c=t.arePrimitiveWrappersEqual,u=t.areRegExpsEqual,f=t.areSetsEqual,d=t.areTypedArraysEqual,p=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?l(e,t,h):"function"===y&&i(e,t,h);var m=e.constructor;if(m!==t.constructor)return!1;if(m===Object)return s(e,t,h);if(iE(e))return r(e,t,h);if(null!=iM&&iM(e))return d(e,t,h);if(m===Date)return n(e,t,h);if(m===RegExp)return u(e,t,h);if(m===Map)return a(e,t,h);if(m===Set)return f(e,t,h);var v=iT(e);return"[object Date]"===v?n(e,t,h):"[object RegExp]"===v?u(e,t,h):"[object Map]"===v?a(e,t,h):"[object Set]"===v?f(e,t,h):"[object Object]"===v?"function"!=typeof e.then&&"function"!=typeof t.then&&s(e,t,h):"[object URL]"===v?p(e,t,h):"[object Error]"===v?o(e,t,h):"[object Arguments]"===v?s(e,t,h):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&c(e,t,h)}),b=y?y(g):function(e,t,r,n,o,i,a){return g(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var l=n(),s=l.cache;return r(e,a,{cache:void 0===s?t?new WeakMap:void 0:s,equals:o,meta:l.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:g,createState:m,equals:b,strict:void 0!==v&&v})}function iD(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function iR(e){return(iR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function iB(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function iL(e){return(iL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i$(Object(r),!0).forEach(function(t){iF(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function iF(e,t,r){var n;return(n=function(e,t){if("object"!==iL(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==iL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===iL(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}iI({strict:!0}),iI({circular:!0}),iI({circular:!0,strict:!0}),iI({createInternalComparator:function(){return ip}}),iI({strict:!0,createInternalComparator:function(){return ip}}),iI({circular:!0,createInternalComparator:function(){return ip}}),iI({circular:!0,createInternalComparator:function(){return ip},strict:!0});var iU=function(e){return e},iq=function(e,t){return Object.keys(t).reduce(function(r,n){return iz(iz({},r),{},iF({},n,e(n,t[n])))},{})},iH=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},iW=function(e,t,r,n,o,i,a,l){};function iV(e,t){if(e){if("string"==typeof e)return iG(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iG(e,t)}}function iG(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var iX=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},iY=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},iK=function(e,t){return function(r){return iY(iX(e,t),r)}},iZ=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],l=n[2],s=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,l=1,s=1;break;case"ease":i=.25,a=.1,l=.25,s=1;break;case"ease-in":i=.42,a=0,l=1,s=1;break;case"ease-out":i=.42,a=0,l=.58,s=1;break;case"ease-in-out":i=0,a=0,l=.58,s=1;break;default:var c=n[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u,f=function(e){if(Array.isArray(e))return e}(u=c[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(u,4)||iV(u,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],l=f[2],s=f[3]}else iW(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}iW([i,l,a,s].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var d=iK(i,l),p=iK(a,s),h=(e=i,t=l,function(r){var n;return iY([].concat(function(e){if(Array.isArray(e))return iG(e)}(n=iX(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||iV(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=d(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return p(r)};return y.isStepper=!1,y},iJ=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,l=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,l=n*a/1e3+e;return 1e-4>Math.abs(l-t)&&1e-4>Math.abs(i)?[t,0]:[l,i]};return l.isStepper=!0,l.dt=a,l},iQ=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return iZ(n);case"spring":return iJ();default:if("cubic-bezier"===n.split("(")[0])return iZ(n);iW(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(iW(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function i0(e){return(i0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i1(e){return function(e){if(Array.isArray(e))return i6(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||i3(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i5(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i2(Object(r),!0).forEach(function(t){i4(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i4(e,t,r){var n;return(n=function(e,t){if("object"!==i0(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==i0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===i0(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i3(e,t){if(e){if("string"==typeof e)return i6(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i6(e,t)}}function i6(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var i8=function(e,t,r){return e+(t-e)*r},i7=function(e){return e.from!==e.to},i9=function e(t,r,n){var o=iq(function(e,r){if(i7(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(n,2)||i3(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return i5(i5({},r),{},{from:i,velocity:a})}return r},r);return n<1?iq(function(e,t){return i7(t)?i5(i5({},t),{},{velocity:i8(t.velocity,o[e].velocity,n),from:i8(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let ae=function(e,t,r,n,o){var i,a,l=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),s=l.reduce(function(r,n){return i5(i5({},r),{},i4({},n,[e[n],t[n]]))},{}),c=l.reduce(function(r,n){return i5(i5({},r),{},i4({},n,{from:e[n],velocity:0,to:t[n]}))},{}),u=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;c=i9(r,c,a),o(i5(i5(i5({},e),t),iq(function(e,t){return t.from},c))),i=n,Object.values(c).filter(i7).length&&(u=requestAnimationFrame(f))}:function(i){a||(a=i);var l=(i-a)/n,c=iq(function(e,t){return i8.apply(void 0,i1(t).concat([r(l)]))},s);if(o(i5(i5(i5({},e),t),c)),l<1)u=requestAnimationFrame(f);else{var d=iq(function(e,t){return i8.apply(void 0,i1(t).concat([r(1)]))},s);o(i5(i5(i5({},e),t),d))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(u)}}};function at(e){return(at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ar=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function an(e){return function(e){if(Array.isArray(e))return ao(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ao(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ao(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ao(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function ai(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ai(Object(r),!0).forEach(function(t){al(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ai(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function al(e,t,r){return(t=as(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function as(e){var t=function(e,t){if("object"!==at(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==at(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===at(t)?t:String(t)}function ac(e,t){return(ac=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function au(e,t){if(t&&("object"===at(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return af(e)}function af(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ad(e){return(ad=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var ap=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ac(e,t)}(o,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=ad(o);return e=t?Reflect.construct(r,arguments,ad(this).constructor):r.apply(this,arguments),au(this,e)});function o(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,o);var r=n.call(this,e,t),i=r.props,a=i.isActive,l=i.attributeName,s=i.from,c=i.to,u=i.steps,f=i.children,d=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(af(r)),r.changeStyle=r.changeStyle.bind(af(r)),!a||d<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:c}),au(r);if(u&&u.length)r.state={style:u[0].style};else if(s){if("function"==typeof f)return r.state={style:s},au(r);r.state={style:l?al({},l,s):s}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,l=t.from,s=this.state.style;if(n){if(!r){var c={style:o?al({},o,a):a};this.state&&s&&(o&&s[o]!==a||!o&&s!==a)&&this.setState(c);return}if(!i_(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||i?l:e.to;if(this.state&&s){var d={style:o?al({},o,f):f};(o&&s[o]!==f||!o&&s!==f)&&this.setState(d)}this.runAnimation(aa(aa({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,l=e.onAnimationEnd,s=e.onAnimationStart,c=ae(r,n,iQ(i),o,this.changeStyle);this.manager.start([s,a,function(){t.stopJSAnimation=c()},o,l])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,l=i.duration;return this.manager.start([o].concat(an(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,l=void 0===a?"ease":a,s=n.style,c=n.properties,u=n.onAnimationEnd,f=o>0?r[o-1]:n,d=c||Object.keys(s);if("function"==typeof l||"spring"===l)return[].concat(an(e),[t.runJSAnimation.bind(t,{from:f.style,to:s,duration:i,easing:l}),i]);var p=iH(d,i,l),h=aa(aa(aa({},f.style),s),{},{transition:p});return[].concat(an(e),[h,i,u]).filter(iU)},[a,Math.max(void 0===l?0:l,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){if(!this.manager){var t,r,n,o;this.manager=(r=function(){return null},n=!1,o=function e(t){if(!n){if(Array.isArray(t)){if(!t.length)return;var o=function(e){if(Array.isArray(e))return e}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return iB(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iB(e,t)}}(t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){iD(e.bind(null,a),i);return}e(i),iD(e.bind(null,a));return}"object"===iR(t)&&r(t),"function"==typeof t&&t()}},{stop:function(){n=!0},start:function(e){n=!1,o(e)},subscribe:function(e){return r=e,function(){r=function(){return null}}}})}var i=e.begin,a=e.duration,l=e.attributeName,s=e.to,c=e.easing,u=e.onAnimationStart,f=e.onAnimationEnd,d=e.steps,p=e.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(e);return}if(d.length>1){this.runStepAnimation(e);return}var y=l?al({},l,s):s,m=iH(Object.keys(y),a,c);h.start([u,i,aa(aa({},y),{},{transition:m}),a,f])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ar)),l=i.Children.count(t),s=this.state.style;if("function"==typeof t)return t(s);if(!n||0===l||r<=0)return t;var c=function(e){var t=e.props,r=t.style,n=t.className;return(0,i.cloneElement)(e,aa(aa({},o),{},{style:aa(aa({},void 0===r?{}:r),s),className:n}))};return 1===l?c(i.Children.only(t)):a().createElement("div",null,i.Children.map(t,function(e){return c(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,as(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function ah(e){return(ah="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ay(){return(ay=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function am(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function av(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ag(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?av(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=ah(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ah(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ah(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):av(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ap.displayName="Animate",ap.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ap.propTypes={from:io().oneOfType([io().object,io().string]),to:io().oneOfType([io().object,io().string]),attributeName:io().string,duration:io().number,begin:io().number,easing:io().oneOfType([io().string,io().func]),steps:io().arrayOf(io().shape({duration:io().number.isRequired,style:io().object.isRequired,easing:io().oneOfType([io().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),io().func]),properties:io().arrayOf("string"),onAnimationEnd:io().func})),children:io().oneOfType([io().node,io().func]),isActive:io().bool,canBegin:io().bool,onAnimationEnd:io().func,shouldReAnimate:io().bool,onAnimationStart:io().func,onAnimationReStart:io().func};var ab=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,s=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(c,",").concat(e+s*u[0],",").concat(t)),i+="L ".concat(e+r-s*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*u[1])),i+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(c,",\n        ").concat(e+r-s*u[2],",").concat(t+n)),i+="L ".concat(e+s*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*u[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var d=Math.min(a,o);i="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+s*d,",").concat(t,"\n            L ").concat(e+r-s*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e+r-s*d,",").concat(t+n,"\n            L ").concat(e+s*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*d," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},ax=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,l=t.height;if(Math.abs(a)>0&&Math.abs(l)>0){var s=Math.min(o,o+a),c=Math.max(o,o+a),u=Math.min(i,i+l),f=Math.max(i,i+l);return r>=s&&r<=c&&n>=u&&n<=f}return!1},aw={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},aj=function(e){var t,r=ag(ag({},aw),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(t,2)||function(e,t){if(e){if("string"==typeof e)return am(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return am(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),l=o[0],s=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&s(e)}catch(e){}},[]);var c=r.x,u=r.y,f=r.width,d=r.height,p=r.radius,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,g=r.isAnimationActive,b=r.isUpdateAnimationActive;if(c!==+c||u!==+u||f!==+f||d!==+d||0===f||0===d)return null;var x=(0,rO.A)("recharts-rectangle",h);return b?a().createElement(ap,{canBegin:l>0,from:{width:f,height:d,x:c,y:u},to:{width:f,height:d,x:c,y:u},duration:m,animationEasing:y,isActive:b},function(e){var t=e.width,o=e.height,i=e.x,s=e.y;return a().createElement(ap,{canBegin:l>0,from:"0px ".concat(-1===l?1:l,"px"),to:"".concat(l,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,isActive:g,easing:y},a().createElement("path",ay({},nu(r,!0),{className:x,d:ab(i,s,t,o,p),ref:n})))}):a().createElement("path",ay({},nu(r,!0),{className:x,d:ab(c,u,f,d,p)}))};function aO(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function aS(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class aA extends Map{constructor(e,t=ak){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(aP(this,e))}has(e){return super.has(aP(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function aP({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function ak(e){return null!==e&&"object"==typeof e?e.valueOf():e}let aN=Symbol("implicit");function aE(){var e=new aA,t=[],r=[],n=aN;function o(o){let i=e.get(o);if(void 0===i){if(n!==aN)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new aA,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return aE(t,r).unknown(n)},aO.apply(o,arguments),o}function aM(){var e,t,r=aE().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,l=!1,s=0,c=0,u=.5;function f(){var r=n().length,f=a<i,d=f?a:i,p=f?i:a;e=(p-d)/Math.max(1,r-s+2*c),l&&(e=Math.floor(e)),d+=(p-d-e*(r-s))*u,t=e*(1-s),l&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return d+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i*=1,a*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(s=Math.min(1,c=+e),f()):s},r.paddingInner=function(e){return arguments.length?(s=Math.min(1,e),f()):s},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return aM(n(),[i,a]).round(l).paddingInner(s).paddingOuter(c).align(u)},aO.apply(f(),arguments)}function aC(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(aM.apply(null,arguments).paddingInner(1))}function aT(e){return(aT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a_(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=aT(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=aT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==aT(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function aD(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var aR={widthCache:{},cacheCount:0},aB={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},aL="recharts_measurement_span",a$=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||nq.isSsr)return{width:0,height:0};var n=(Object.keys(t=aI({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(aR.widthCache[o])return aR.widthCache[o];try{var i=document.getElementById(aL);i||((i=document.createElement("span")).setAttribute("id",aL),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=aI(aI({},aB),n);Object.assign(i.style,a),i.textContent="".concat(e);var l=i.getBoundingClientRect(),s={width:l.width,height:l.height};return aR.widthCache[o]=s,++aR.cacheCount>2e3&&(aR.cacheCount=0,aR.widthCache={}),s}catch(e){return{width:0,height:0}}};function az(e){return(az="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function aF(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return aU(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aU(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function aU(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function aq(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=az(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=az(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==az(t)?t:t+""}(n.key),n)}}var aH=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,aW=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,aV=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,aG=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,aX={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},aY=Object.keys(aX),aK=function(){var e,t;function r(e,t){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||aV.test(t)||(this.num=NaN,this.unit=""),aY.includes(t)&&(this.num=e*aX[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=aF(null!==(t=aG.exec(e))&&void 0!==t?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&aq(r.prototype,e),t&&aq(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function aZ(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=aF(null!==(r=aH.exec(t))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],l=aK.parse(null!=o?o:""),s=aK.parse(null!=a?a:""),c="*"===i?l.multiply(s):l.divide(s);if(c.isNaN())return"NaN";t=t.replace(aH,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=aF(null!==(u=aW.exec(t))&&void 0!==u?u:[],4),d=f[1],p=f[2],h=f[3],y=aK.parse(null!=d?d:""),m=aK.parse(null!=h?h:""),v="+"===p?y.add(m):y.subtract(m);if(v.isNaN())return"NaN";t=t.replace(aW,v.toString())}return t}var aJ=/\(([^()]*)\)/;function aQ(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=aF(aJ.exec(t),2)[1];t=t.replace(aJ,aZ(r))}return t}(t),t=aZ(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var a0=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],a1=["dx","dy","angle","className","breakAll"];function a2(){return(a2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function a5(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function a4(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return a3(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a3(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a3(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var a6=/[ \f\n\r\t\v\u2028\u2029]+/,a8=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];rG()(t)||(o=r?t.toString().split(""):t.toString().split(a6));var i=o.map(function(e){return{word:e,width:a$(e,n).width}}),a=r?0:a$("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},a7=function(e,t,r,n,o){var i,a=e.maxLines,l=e.children,s=e.style,c=e.breakAll,u=rR(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,l=e[e.length-1];return l&&(null==n||o||l.width+a+r<Number(n))?(l.words.push(i),l.width+=a+r):e.push({words:[i],width:a}),e},[])},d=f(t);if(!u)return d;for(var p=function(e){var t=f(a8({breakAll:c,style:s,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=l.length-1,m=0;h<=y&&m<=l.length-1;){var v=Math.floor((h+y)/2),g=a4(p(v-1),2),b=g[0],x=g[1],w=a4(p(v),1)[0];if(b||w||(h=v+1),b&&w&&(y=v-1),!b&&w){i=x;break}m++}return i||d},a9=function(e){return[{words:rG()(e)?[]:e.toString().split(a6)}]},le=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!nq.isSsr){var l=a8({breakAll:i,children:n,style:o});if(!l)return a9(n);var s=l.wordsWithComputedWidth,c=l.spaceWidth;return a7({breakAll:i,children:n,maxLines:a,style:o},s,c,t,r)}return a9(n)},lt="#808080",lr=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,l=void 0===o?0:o,s=e.lineHeight,c=void 0===s?"1em":s,u=e.capHeight,f=void 0===u?"0.71em":u,d=e.scaleToFit,p=void 0!==d&&d,h=e.textAnchor,y=e.verticalAnchor,m=e.fill,v=void 0===m?lt:m,g=a5(e,a0),b=(0,i.useMemo)(function(){return le({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:p,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,p,g.style,g.width]),x=g.dx,w=g.dy,j=g.angle,O=g.className,S=g.breakAll,A=a5(g,a1);if(!rB(n)||!rB(l))return null;var P=n+(rR(x)?x:0),k=l+(rR(w)?w:0);switch(void 0===y?"end":y){case"start":t=aQ("calc(".concat(f,")"));break;case"middle":t=aQ("calc(".concat((b.length-1)/2," * -").concat(c," + (").concat(f," / 2))"));break;default:t=aQ("calc(".concat(b.length-1," * -").concat(c,")"))}var N=[];if(p){var E=b[0].width,M=g.width;N.push("scale(".concat((rR(M)?M/E:1)/E,")"))}return j&&N.push("rotate(".concat(j,", ").concat(P,", ").concat(k,")")),N.length&&(A.transform=N.join(" ")),a().createElement("text",a2({},nu(A,!0),{x:P,y:k,className:(0,rO.A)("recharts-text",O),textAnchor:void 0===h?"start":h,fill:v.includes("url")?lt:v}),b.map(function(e,r){var n=e.words.join(S?"":" ");return a().createElement("tspan",{x:P,dy:0===r?t:c,key:"".concat(n,"-").concat(r)},n)}))};let ln=Math.sqrt(50),lo=Math.sqrt(10),li=Math.sqrt(2);function la(e,t,r){let n,o,i;let a=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(a)),s=a/Math.pow(10,l),c=s>=ln?10:s>=lo?5:s>=li?2:1;return(l<0?(n=Math.round(e*(i=Math.pow(10,-l)/c)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,l)*c)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?la(e,t,2*r):[n,o,i]}function ll(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?la(t,e,r):la(e,t,r);if(!(i>=o))return[];let l=i-o+1,s=Array(l);if(n){if(a<0)for(let e=0;e<l;++e)s[e]=-((i-e)/a);else for(let e=0;e<l;++e)s[e]=(i-e)*a}else if(a<0)for(let e=0;e<l;++e)s[e]=-((o+e)/a);else for(let e=0;e<l;++e)s[e]=(o+e)*a;return s}function ls(e,t,r){return la(e*=1,t*=1,r*=1)[2]}function lc(e,t,r){t*=1,e*=1,r*=1;let n=t<e,o=n?ls(t,e,r):ls(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function lu(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function lf(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ld(e){let t,r,n;function o(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>r(e[t],n)?i=t+1:a=t}while(i<a)}return i}return 2!==e.length?(t=lu,r=(t,r)=>lu(e(t),r),n=(t,r)=>e(t)-r):(t=e===lu||e===lf?e:lp,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function lp(){return 0}function lh(e){return null===e?NaN:+e}let ly=ld(lu),lm=ly.right;function lv(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function lg(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function lb(){}ly.left,ld(lh).center;var lx="\\s*([+-]?\\d+)\\s*",lw="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lj="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",lO=/^#([0-9a-f]{3,8})$/,lS=RegExp(`^rgb\\(${lx},${lx},${lx}\\)$`),lA=RegExp(`^rgb\\(${lj},${lj},${lj}\\)$`),lP=RegExp(`^rgba\\(${lx},${lx},${lx},${lw}\\)$`),lk=RegExp(`^rgba\\(${lj},${lj},${lj},${lw}\\)$`),lN=RegExp(`^hsl\\(${lw},${lj},${lj}\\)$`),lE=RegExp(`^hsla\\(${lw},${lj},${lj},${lw}\\)$`),lM={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function lC(){return this.rgb().formatHex()}function lT(){return this.rgb().formatRgb()}function l_(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=lO.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?lI(t):3===r?new lB(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?lD(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?lD(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=lS.exec(e))?new lB(t[1],t[2],t[3],1):(t=lA.exec(e))?new lB(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=lP.exec(e))?lD(t[1],t[2],t[3],t[4]):(t=lk.exec(e))?lD(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=lN.exec(e))?lq(t[1],t[2]/100,t[3]/100,1):(t=lE.exec(e))?lq(t[1],t[2]/100,t[3]/100,t[4]):lM.hasOwnProperty(e)?lI(lM[e]):"transparent"===e?new lB(NaN,NaN,NaN,0):null}function lI(e){return new lB(e>>16&255,e>>8&255,255&e,1)}function lD(e,t,r,n){return n<=0&&(e=t=r=NaN),new lB(e,t,r,n)}function lR(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof lb||(o=l_(o)),o)?new lB((o=o.rgb()).r,o.g,o.b,o.opacity):new lB:new lB(e,t,r,null==n?1:n)}function lB(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function lL(){return`#${lU(this.r)}${lU(this.g)}${lU(this.b)}`}function l$(){let e=lz(this.opacity);return`${1===e?"rgb(":"rgba("}${lF(this.r)}, ${lF(this.g)}, ${lF(this.b)}${1===e?")":`, ${e})`}`}function lz(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function lF(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function lU(e){return((e=lF(e))<16?"0":"")+e.toString(16)}function lq(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new lW(e,t,r,n)}function lH(e){if(e instanceof lW)return new lW(e.h,e.s,e.l,e.opacity);if(e instanceof lb||(e=l_(e)),!e)return new lW;if(e instanceof lW)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,l=i-o,s=(i+o)/2;return l?(a=t===i?(r-n)/l+(r<n)*6:r===i?(n-t)/l+2:(t-r)/l+4,l/=s<.5?i+o:2-i-o,a*=60):l=s>0&&s<1?0:a,new lW(a,l,s,e.opacity)}function lW(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function lV(e){return(e=(e||0)%360)<0?e+360:e}function lG(e){return Math.max(0,Math.min(1,e||0))}function lX(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function lY(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}lv(lb,l_,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:lC,formatHex:lC,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return lH(this).formatHsl()},formatRgb:lT,toString:lT}),lv(lB,lR,lg(lb,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new lB(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new lB(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new lB(lF(this.r),lF(this.g),lF(this.b),lz(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:lL,formatHex:lL,formatHex8:function(){return`#${lU(this.r)}${lU(this.g)}${lU(this.b)}${lU((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:l$,toString:l$})),lv(lW,function(e,t,r,n){return 1==arguments.length?lH(e):new lW(e,t,r,null==n?1:n)},lg(lb,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new lW(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new lW(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new lB(lX(e>=240?e-240:e+120,o,n),lX(e,o,n),lX(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new lW(lV(this.h),lG(this.s),lG(this.l),lz(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=lz(this.opacity);return`${1===e?"hsl(":"hsla("}${lV(this.h)}, ${100*lG(this.s)}%, ${100*lG(this.l)}%${1===e?")":`, ${e})`}`}}));let lK=e=>()=>e;function lZ(e,t){var r,n,o=t-e;return o?(r=e,n=o,function(e){return r+e*n}):lK(isNaN(e)?t:e)}let lJ=function e(t){var r,n=1==(r=+t)?lZ:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):lK(isNaN(e)?t:e)};function o(e,t){var r=n((e=lR(e)).r,(t=lR(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=lZ(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function lQ(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(r=0;r<o;++r)n=lR(t[r]),i[r]=n.r||0,a[r]=n.g||0,l[r]=n.b||0;return i=e(i),a=e(a),l=e(l),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=l(e),n+""}}}lQ(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,l=n<t-1?e[n+2]:2*i-o;return lY((r-n/t)*t,a,o,i,l)}}),lQ(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],l=e[(n+2)%t];return lY((r-n/t)*t,o,i,a,l)}});function l0(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var l1=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,l2=RegExp(l1.source,"g");function l5(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?lK(t):("number"===o?l0:"string"===o?(n=l_(t))?(t=n,lJ):function(e,t){var r,n,o,i,a,l=l1.lastIndex=l2.lastIndex=0,s=-1,c=[],u=[];for(e+="",t+="";(o=l1.exec(e))&&(i=l2.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),c[s]?c[s]+=a:c[++s]=a),(o=o[0])===(i=i[0])?c[s]?c[s]+=i:c[++s]=i:(c[++s]=null,u.push({i:s,x:l0(o,i)})),l=l2.lastIndex;return l<t.length&&(a=t.slice(l),c[s]?c[s]+=a:c[++s]=a),c.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)c[(r=u[n]).i]=r.x(e);return c.join("")})}:t instanceof l_?lJ:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=l5(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=l5(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:l0:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function l4(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function l3(e){return+e}var l6=[0,1];function l8(e){return e}function l7(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function l9(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=l7(o,n),i=r(a,i)):(n=l7(n,o),i=r(i,a)),function(e){return i(n(e))}}function se(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=l7(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=lm(e,t,1,n)-1;return i[r](o[r](t))}}function st(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function sr(){var e,t,r,n,o,i,a=l6,l=l6,s=l5,c=l8;function u(){var e,t,r,s=Math.min(a.length,l.length);return c!==l8&&(e=a[0],t=a[s-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=s>2?se:l9,o=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(o||(o=n(a.map(e),l,s)))(e(c(t)))}return f.invert=function(r){return c(t((i||(i=n(l,a.map(e),l0)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,l3),u()):a.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),s=l4,u()},f.clamp=function(e){return arguments.length?(c=!!e||l8,u()):c!==l8},f.interpolate=function(e){return arguments.length?(s=e,u()):s},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function sn(){return sr()(l8,l8)}var so=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function si(e){var t;if(!(t=so.exec(e)))throw Error("invalid format: "+e);return new sa({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function sa(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function sl(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function ss(e){return(e=sl(Math.abs(e)))?e[1]:NaN}function sc(e,t){var r=sl(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}si.prototype=sa.prototype,sa.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let su={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>sc(100*e,t),r:sc,s:function(e,t){var r=sl(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(u$=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+sl(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function sf(e){return e}var sd=Array.prototype.map,sp=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function sh(e,t,r,n){var o,i,a,l=lc(e,t,r);switch((n=si(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null==n.precision&&!isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ss(s)/3)))-ss(Math.abs(l))))&&(n.precision=a),uU(n,s);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(a=Math.max(0,ss(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=l)))-ss(o))+1)&&(n.precision=a-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(a=Math.max(0,-ss(Math.abs(l))))&&(n.precision=a-("%"===n.type)*2)}return uF(n)}function sy(e){var t=e.domain;return e.ticks=function(e){var r=t();return ll(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return sh(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,l=i.length-1,s=i[a],c=i[l],u=10;for(c<s&&(o=s,s=c,c=o,o=a,a=l,l=o);u-- >0;){if((o=ls(s,c,r))===n)return i[a]=s,i[l]=c,t(i);if(o>0)s=Math.floor(s/o)*o,c=Math.ceil(c/o)*o;else if(o<0)s=Math.ceil(s*o)/o,c=Math.floor(c*o)/o;else break;n=o}return e},e}function sm(){var e=sn();return e.copy=function(){return st(e,sm())},aO.apply(e,arguments),sy(e)}function sv(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function sg(e){return Math.log(e)}function sb(e){return Math.exp(e)}function sx(e){return-Math.log(-e)}function sw(e){return-Math.exp(-e)}function sj(e){return isFinite(e)?+("1e"+e):e<0?0:e}function sO(e){return(t,r)=>-e(-t,r)}function sS(e){let t,r;let n=e(sg,sb),o=n.domain,i=10;function a(){var a,l;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=i)?sj:l===Math.E?Math.exp:e=>Math.pow(l,e),o()[0]<0?(t=sO(t),r=sO(r),e(sx,sw)):e(sg,sb),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a;let l=o(),s=l[0],c=l[l.length-1],u=c<s;u&&([s,c]=[c,s]);let f=t(s),d=t(c),p=null==e?10:+e,h=[];if(!(i%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),s>0){for(;f<=d;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<s)){if(a>c)break;h.push(a)}}else for(;f<=d;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<s)){if(a>c)break;h.push(a)}2*h.length<p&&(h=ll(s,c,p))}else h=ll(f,d,Math.min(d-f,p)).map(r);return u?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=si(o)).precision||(o.trim=!0),o=uF(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(sv(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function sA(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function sP(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function sk(e){var t=1,r=e(sA(1),sP(t));return r.constant=function(r){return arguments.length?e(sA(t=+r),sP(t)):t},sy(r)}function sN(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function sE(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function sM(e){return e<0?-e*e:e*e}function sC(e){var t=e(l8,l8),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(l8,l8):.5===r?e(sE,sM):e(sN(r),sN(1/r)):r},sy(t)}function sT(){var e=sC(sr());return e.copy=function(){return st(e,sT()).exponent(e.exponent())},aO.apply(e,arguments),e}function s_(){return sT.apply(null,arguments).exponent(.5)}function sI(e){return Math.sign(e)*e*e}function sD(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function sR(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}uF=(uz=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?sf:(t=sd.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,l=t[0],s=0;o>0&&l>0&&(s+l+1>n&&(l=Math.max(1,n-s)),i.push(e.substring(o-=l,o+l)),!((s+=l+1)>n));)l=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?sf:(n=sd.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),c=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=si(e)).fill,r=e.align,n=e.sign,d=e.symbol,p=e.zero,h=e.width,y=e.comma,m=e.precision,v=e.trim,g=e.type;"n"===g?(y=!0,g="g"):su[g]||(void 0===m&&(m=12),v=!0,g="g"),(p||"0"===t&&"="===r)&&(p=!0,t="0",r="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===d?a:/[%p]/.test(g)?c:"",w=su[g],j=/[defgprs%]/.test(g);function O(e){var i,a,c,d=b,O=x;if("c"===g)O=w(e)+O,e="";else{var S=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),m),v&&(e=function(e){e:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),d=(S?"("===n?n:u:"-"===n||"("===n?"":n)+d,O=("s"===g?sp[8+u$/3]:"")+O+(S&&"("===n?")":""),j){for(i=-1,a=e.length;++i<a;)if(48>(c=e.charCodeAt(i))||c>57){O=(46===c?l+e.slice(i+1):e.slice(i))+O,e=e.slice(0,i);break}}}y&&!p&&(e=o(e,1/0));var A=d.length+e.length+O.length,P=A<h?Array(h-A+1).join(t):"";switch(y&&p&&(e=o(P+e,P.length?h-O.length:1/0),P=""),r){case"<":e=d+e+O+P;break;case"=":e=d+P+e+O;break;case"^":e=P.slice(0,A=P.length>>1)+d+e+O+P.slice(A);break;default:e=P+d+e+O}return s(e)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),O.toString=function(){return e+""},O}return{format:d,formatPrefix:function(e,t){var r=d(((e=si(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(ss(t)/3))),o=Math.pow(10,-n),i=sp[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,uU=uz.formatPrefix;function sB(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function sL(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let s$=new Date,sz=new Date;function sF(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a;let l=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return l;do l.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return l},o.filter=r=>sF(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(o.count=(t,n)=>(s$.setTime(+t),sz.setTime(+n),e(s$),e(sz),Math.floor(r(s$,sz))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let sU=sF(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);sU.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?sF(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):sU:null,sU.range;let sq=sF(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());sq.range;let sH=sF(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());sH.range;let sW=sF(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());sW.range;let sV=sF(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());sV.range;let sG=sF(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());sG.range;let sX=sF(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);sX.range;let sY=sF(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);sY.range;let sK=sF(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function sZ(e){return sF(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}sK.range;let sJ=sZ(0),sQ=sZ(1),s0=sZ(2),s1=sZ(3),s2=sZ(4),s5=sZ(5),s4=sZ(6);function s3(e){return sF(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}sJ.range,sQ.range,s0.range,s1.range,s2.range,s5.range,s4.range;let s6=s3(0),s8=s3(1),s7=s3(2),s9=s3(3),ce=s3(4),ct=s3(5),cr=s3(6);s6.range,s8.range,s7.range,s9.range,ce.range,ct.range,cr.range;let cn=sF(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());cn.range;let co=sF(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());co.range;let ci=sF(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());ci.every=e=>isFinite(e=Math.floor(e))&&e>0?sF(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,ci.range;let ca=sF(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function cl(e,t,r,n,o,i){let a=[[sq,1,1e3],[sq,5,5e3],[sq,15,15e3],[sq,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let o=Math.abs(r-t)/n,i=ld(([,,e])=>e).right(a,o);if(i===a.length)return e.every(lc(t/31536e6,r/31536e6,n));if(0===i)return sU.every(Math.max(lc(t,r,n),1));let[l,s]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return l.every(s)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:l(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},l]}ca.every=e=>isFinite(e=Math.floor(e))&&e>0?sF(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,ca.range;let[cs,cc]=cl(ca,co,s6,sK,sG,sW),[cu,cf]=cl(ci,cn,sJ,sX,sV,sH);function cd(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function cp(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function ch(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var cy={"-":"",_:" ",0:"0"},cm=/^\s*\d+/,cv=/^%/,cg=/[\\^$*+?|[\]().{}]/g;function cb(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function cx(e){return e.replace(cg,"\\$&")}function cw(e){return RegExp("^(?:"+e.map(cx).join("|")+")","i")}function cj(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function cO(e,t,r){var n=cm.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function cS(e,t,r){var n=cm.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function cA(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function cP(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function ck(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function cN(e,t,r){var n=cm.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function cE(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function cM(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function cC(e,t,r){var n=cm.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function cT(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function c_(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function cI(e,t,r){var n=cm.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function cD(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function cR(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function cB(e,t,r){var n=cm.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function cL(e,t,r){var n=cm.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function c$(e,t,r){var n=cm.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function cz(e,t,r){var n=cv.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function cF(e,t,r){var n=cm.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function cU(e,t,r){var n=cm.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function cq(e,t){return cb(e.getDate(),t,2)}function cH(e,t){return cb(e.getHours(),t,2)}function cW(e,t){return cb(e.getHours()%12||12,t,2)}function cV(e,t){return cb(1+sX.count(ci(e),e),t,3)}function cG(e,t){return cb(e.getMilliseconds(),t,3)}function cX(e,t){return cG(e,t)+"000"}function cY(e,t){return cb(e.getMonth()+1,t,2)}function cK(e,t){return cb(e.getMinutes(),t,2)}function cZ(e,t){return cb(e.getSeconds(),t,2)}function cJ(e){var t=e.getDay();return 0===t?7:t}function cQ(e,t){return cb(sJ.count(ci(e)-1,e),t,2)}function c0(e){var t=e.getDay();return t>=4||0===t?s2(e):s2.ceil(e)}function c1(e,t){return e=c0(e),cb(s2.count(ci(e),e)+(4===ci(e).getDay()),t,2)}function c2(e){return e.getDay()}function c5(e,t){return cb(sQ.count(ci(e)-1,e),t,2)}function c4(e,t){return cb(e.getFullYear()%100,t,2)}function c3(e,t){return cb((e=c0(e)).getFullYear()%100,t,2)}function c6(e,t){return cb(e.getFullYear()%1e4,t,4)}function c8(e,t){var r=e.getDay();return cb((e=r>=4||0===r?s2(e):s2.ceil(e)).getFullYear()%1e4,t,4)}function c7(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+cb(t/60|0,"0",2)+cb(t%60,"0",2)}function c9(e,t){return cb(e.getUTCDate(),t,2)}function ue(e,t){return cb(e.getUTCHours(),t,2)}function ut(e,t){return cb(e.getUTCHours()%12||12,t,2)}function ur(e,t){return cb(1+sY.count(ca(e),e),t,3)}function un(e,t){return cb(e.getUTCMilliseconds(),t,3)}function uo(e,t){return un(e,t)+"000"}function ui(e,t){return cb(e.getUTCMonth()+1,t,2)}function ua(e,t){return cb(e.getUTCMinutes(),t,2)}function ul(e,t){return cb(e.getUTCSeconds(),t,2)}function us(e){var t=e.getUTCDay();return 0===t?7:t}function uc(e,t){return cb(s6.count(ca(e)-1,e),t,2)}function uu(e){var t=e.getUTCDay();return t>=4||0===t?ce(e):ce.ceil(e)}function uf(e,t){return e=uu(e),cb(ce.count(ca(e),e)+(4===ca(e).getUTCDay()),t,2)}function ud(e){return e.getUTCDay()}function up(e,t){return cb(s8.count(ca(e)-1,e),t,2)}function uh(e,t){return cb(e.getUTCFullYear()%100,t,2)}function uy(e,t){return cb((e=uu(e)).getUTCFullYear()%100,t,2)}function um(e,t){return cb(e.getUTCFullYear()%1e4,t,4)}function uv(e,t){var r=e.getUTCDay();return cb((e=r>=4||0===r?ce(e):ce.ceil(e)).getUTCFullYear()%1e4,t,4)}function ug(){return"+0000"}function ub(){return"%"}function ux(e){return+e}function uw(e){return Math.floor(+e/1e3)}function uj(e){return new Date(e)}function uO(e){return e instanceof Date?+e:+new Date(+e)}function uS(e,t,r,n,o,i,a,l,s,c){var u=sn(),f=u.invert,d=u.domain,p=c(".%L"),h=c(":%S"),y=c("%I:%M"),m=c("%I %p"),v=c("%a %d"),g=c("%b %d"),b=c("%B"),x=c("%Y");function w(e){return(s(e)<e?p:l(e)<e?h:a(e)<e?y:i(e)<e?m:n(e)<e?o(e)<e?v:g:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,uO)):d().map(uj)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:c(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(sv(r,e)):u},u.copy=function(){return st(u,uS(e,t,r,n,o,i,a,l,s,c))},u}function uA(){return aO.apply(uS(cu,cf,ci,cn,sJ,sX,sV,sH,sq,uH).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function uP(){return aO.apply(uS(cs,cc,ca,co,s6,sY,sG,sW,sq,uW).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function uk(){var e,t,r,n,o,i=0,a=1,l=l8,s=!1;function c(t){return null==t||isNaN(t*=1)?o:l(0===r?.5:(t=(n(t)-e)*r,s?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(o){return arguments.length?([i,a]=o,e=n(i*=1),t=n(a*=1),r=e===t?0:1/(t-e),c):[i,a]},c.clamp=function(e){return arguments.length?(s=!!e,c):s},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=u(l5),c.rangeRound=u(l4),c.unknown=function(e){return arguments.length?(o=e,c):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),c}}function uN(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function uE(){var e=sC(uk());return e.copy=function(){return uN(e,uE()).exponent(e.exponent())},aS.apply(e,arguments)}function uM(){return uE.apply(null,arguments).exponent(.5)}function uC(){var e,t,r,n,o,i,a,l=0,s=.5,c=1,u=1,f=l8,d=!1;function p(e){return isNaN(e*=1)?a:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:o),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=l5);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),p):[f(0),f(.5),f(1)]}}return p.domain=function(a){return arguments.length?([l,s,c]=a,e=i(l*=1),t=i(s*=1),r=i(c*=1),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,p):[l,s,c]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(l5),p.rangeRound=h(l4),p.unknown=function(e){return arguments.length?(a=e,p):a},function(a){return i=a,e=a(l),t=a(s),r=a(c),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,p}}function uT(){var e=sC(uC());return e.copy=function(){return uN(e,uT()).exponent(e.exponent())},aS.apply(e,arguments)}function u_(){return uT.apply(null,arguments).exponent(.5)}function uI(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],l=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<l;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function uD(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function uR(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function uB(e,t){return e[t]}function uL(e){let t=[];return t.key=e,t}uH=(uq=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,l=e.months,s=e.shortMonths,c=cw(o),u=cj(o),f=cw(i),d=cj(i),p=cw(a),h=cj(a),y=cw(l),m=cj(l),v=cw(s),g=cj(s),b={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return s[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:cq,e:cq,f:cX,g:c3,G:c8,H:cH,I:cW,j:cV,L:cG,m:cY,M:cK,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:ux,s:uw,S:cZ,u:cJ,U:cQ,V:c1,w:c2,W:c5,x:null,X:null,y:c4,Y:c6,Z:c7,"%":ub},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return s[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:c9,e:c9,f:uo,g:uy,G:uv,H:ue,I:ut,j:ur,L:un,m:ui,M:ua,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:ux,s:uw,S:ul,u:us,U:uc,V:uf,w:ud,W:up,x:null,X:null,y:uh,Y:um,Z:ug,"%":ub},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=v.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:c_,e:c_,f:c$,g:cE,G:cN,H:cD,I:cD,j:cI,L:cL,m:cT,M:cR,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:cC,Q:cF,s:cU,S:cB,u:cS,U:cA,V:cP,w:cO,W:ck,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:cE,Y:cN,Z:cM,"%":cz};function j(e,t){return function(r){var n,o,i,a=[],l=-1,s=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(a.push(e.slice(s,l)),null!=(o=cy[n=e.charAt(++l)])?n=e.charAt(++l):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),s=l+1);return a.push(e.slice(s,l)),a.join("")}}function O(e,t){return function(r){var n,o,i=ch(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=cp(ch(i.y,0,1))).getUTCDay())>4||0===o?s8.ceil(n):s8(n),n=sY.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=cd(ch(i.y,0,1))).getDay())>4||0===o?sQ.ceil(n):sQ(n),n=sX.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?cp(ch(i.y,0,1)).getUTCDay():cd(ch(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,cp(i)):cd(i)}}function S(e,t,r,n){for(var o,i,a=0,l=t.length,s=r.length;a<l;){if(n>=s)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in cy?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return b.x=j(r,b),b.X=j(n,b),b.c=j(t,b),x.x=j(r,x),x.X=j(n,x),x.c=j(t,x),{format:function(e){var t=j(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=O(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=j(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=O(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,uq.parse,uW=uq.utcFormat,uq.utcParse,Array.prototype.slice;var u$,uz,uF,uU,uq,uH,uW,uV,uG,uX=r(90453),uY=r.n(uX),uK=r(15883),uZ=r.n(uK),uJ=r(21592),uQ=r.n(uJ),u0=r(71967),u1=r.n(u0),u2=!0,u5="[DecimalError] ",u4=u5+"Invalid argument: ",u3=u5+"Exponent out of range: ",u6=Math.floor,u8=Math.pow,u7=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,u9=u6(1286742750677284.5),fe={};function ft(e,t){var r,n,o,i,a,l,s,c,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),u2?ff(t,f):t;if(s=e.d,c=t.d,a=e.e,o=t.e,s=s.slice(),i=a-o){for(i<0?(n=s,i=-i,l=c.length):(n=c,o=a,l=s.length),i>(l=(a=Math.ceil(f/7))>l?a+1:l+1)&&(i=l,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((l=s.length)-(i=c.length)<0&&(i=l,n=c,c=s,s=n),r=0;i;)r=(s[--i]=s[i]+c[i]+r)/1e7|0,s[i]%=1e7;for(r&&(s.unshift(r),++o),l=s.length;0==s[--l];)s.pop();return t.d=s,t.e=o,u2?ff(t,f):t}function fr(e,t,r){if(e!==~~e||e<t||e>r)throw Error(u4+e)}function fn(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=fs(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=fs(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}fe.absoluteValue=fe.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},fe.comparedTo=fe.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},fe.decimalPlaces=fe.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},fe.dividedBy=fe.div=function(e){return fo(this,new this.constructor(e))},fe.dividedToIntegerBy=fe.idiv=function(e){var t=this.constructor;return ff(fo(this,new t(e),0,1),t.precision)},fe.equals=fe.eq=function(e){return!this.cmp(e)},fe.exponent=function(){return fa(this)},fe.greaterThan=fe.gt=function(e){return this.cmp(e)>0},fe.greaterThanOrEqualTo=fe.gte=function(e){return this.cmp(e)>=0},fe.isInteger=fe.isint=function(){return this.e>this.d.length-2},fe.isNegative=fe.isneg=function(){return this.s<0},fe.isPositive=fe.ispos=function(){return this.s>0},fe.isZero=function(){return 0===this.s},fe.lessThan=fe.lt=function(e){return 0>this.cmp(e)},fe.lessThanOrEqualTo=fe.lte=function(e){return 1>this.cmp(e)},fe.logarithm=fe.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(uG))throw Error(u5+"NaN");if(this.s<1)throw Error(u5+(this.s?"NaN":"-Infinity"));return this.eq(uG)?new r(0):(u2=!1,t=fo(fc(this,o),fc(e,o),o),u2=!0,ff(t,n))},fe.minus=fe.sub=function(e){return e=new this.constructor(e),this.s==e.s?fd(this,e):ft(this,(e.s=-e.s,e))},fe.modulo=fe.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u5+"NaN");return this.s?(u2=!1,t=fo(this,e,0,1).times(e),u2=!0,this.minus(t)):ff(new r(this),n)},fe.naturalExponential=fe.exp=function(){return fi(this)},fe.naturalLogarithm=fe.ln=function(){return fc(this)},fe.negated=fe.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},fe.plus=fe.add=function(e){return e=new this.constructor(e),this.s==e.s?ft(this,e):fd(this,(e.s=-e.s,e))},fe.precision=fe.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(u4+e);if(t=fa(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},fe.squareRoot=fe.sqrt=function(){var e,t,r,n,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(u5+"NaN")}for(e=fa(this),u2=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=fn(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=u6((e+1)/2)-(e<0||e%2),n=new l(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(o.toString()),o=a=(r=l.precision)+3;;)if(n=(i=n).plus(fo(this,i,a+2)).times(.5),fn(i.d).slice(0,a)===(t=fn(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(ff(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return u2=!0,ff(n,r)},fe.times=fe.mul=function(e){var t,r,n,o,i,a,l,s,c,u=this.constructor,f=this.d,d=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(s=f.length)<(c=d.length)&&(i=f,f=d,d=i,a=s,s=c,c=a),i=[],n=a=s+c;n--;)i.push(0);for(n=c;--n>=0;){for(t=0,o=s+n;o>n;)l=i[o]+d[n]*f[o-n-1]+t,i[o--]=l%1e7|0,t=l/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,u2?ff(e,u.precision):e},fe.toDecimalPlaces=fe.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(fr(e,0,1e9),void 0===t?t=n.rounding:fr(t,0,8),ff(r,e+fa(r)+1,t))},fe.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=fp(n,!0):(fr(e,0,1e9),void 0===t?t=o.rounding:fr(t,0,8),r=fp(n=ff(new o(n),e+1,t),!0,e+1)),r},fe.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?fp(this):(fr(e,0,1e9),void 0===t?t=o.rounding:fr(t,0,8),r=fp((n=ff(new o(this),e+fa(this)+1,t)).abs(),!1,e+fa(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},fe.toInteger=fe.toint=function(){var e=this.constructor;return ff(new e(this),fa(this)+1,e.rounding)},fe.toNumber=function(){return+this},fe.toPower=fe.pow=function(e){var t,r,n,o,i,a,l=this,s=l.constructor,c=+(e=new s(e));if(!e.s)return new s(uG);if(!(l=new s(l)).s){if(e.s<1)throw Error(u5+"Infinity");return l}if(l.eq(uG))return l;if(n=s.precision,e.eq(uG))return ff(l,n);if(a=(t=e.e)>=(r=e.d.length-1),i=l.s,a){if((r=c<0?-c:c)<=0x1fffffffffffff){for(o=new s(uG),t=Math.ceil(n/7+4),u2=!1;r%2&&fh((o=o.times(l)).d,t),0!==(r=u6(r/2));)fh((l=l.times(l)).d,t);return u2=!0,e.s<0?new s(uG).div(o):ff(o,n)}}else if(i<0)throw Error(u5+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,u2=!1,o=e.times(fc(l,n+12)),u2=!0,(o=fi(o)).s=i,o},fe.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=fa(o),n=fp(o,r<=i.toExpNeg||r>=i.toExpPos)):(fr(e,1,1e9),void 0===t?t=i.rounding:fr(t,0,8),r=fa(o=ff(new i(o),e,t)),n=fp(o,e<=r||r<=i.toExpNeg,e)),n},fe.toSignificantDigits=fe.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(fr(e,1,1e9),void 0===t?t=r.rounding:fr(t,0,8)),ff(new r(this),e,t)},fe.toString=fe.valueOf=fe.val=fe.toJSON=fe[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=fa(this),t=this.constructor;return fp(this,e<=t.toExpNeg||e>=t.toExpPos)};var fo=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var l,s,c,u,f,d,p,h,y,m,v,g,b,x,w,j,O,S,A=n.constructor,P=n.s==o.s?1:-1,k=n.d,N=o.d;if(!n.s)return new A(n);if(!o.s)throw Error(u5+"Division by zero");for(c=0,s=n.e-o.e,O=N.length,w=k.length,h=(p=new A(P)).d=[];N[c]==(k[c]||0);)++c;if(N[c]>(k[c]||0)&&--s,(g=null==i?i=A.precision:a?i+(fa(n)-fa(o))+1:i)<0)return new A(0);if(g=g/7+2|0,c=0,1==O)for(u=0,N=N[0],g++;(c<w||u)&&g--;c++)b=1e7*u+(k[c]||0),h[c]=b/N|0,u=b%N|0;else{for((u=1e7/(N[0]+1)|0)>1&&(N=e(N,u),k=e(k,u),O=N.length,w=k.length),x=O,m=(y=k.slice(0,O)).length;m<O;)y[m++]=0;(S=N.slice()).unshift(0),j=N[0],N[1]>=1e7/2&&++j;do u=0,(l=t(N,y,O,m))<0?(v=y[0],O!=m&&(v=1e7*v+(y[1]||0)),(u=v/j|0)>1?(u>=1e7&&(u=1e7-1),d=(f=e(N,u)).length,m=y.length,1==(l=t(f,y,d,m))&&(u--,r(f,O<d?S:N,d))):(0==u&&(l=u=1),f=N.slice()),(d=f.length)<m&&f.unshift(0),r(y,f,m),-1==l&&(m=y.length,(l=t(N,y,O,m))<1&&(u++,r(y,O<m?S:N,m))),m=y.length):0===l&&(u++,y=[0]),h[c++]=u,l&&y[0]?y[m++]=k[x]||0:(y=[k[x]],m=1);while((x++<w||void 0!==y[0])&&g--)}return h[0]||h.shift(),p.e=s,ff(p,a?i+fa(p)+1:i)}}();function fi(e,t){var r,n,o,i,a,l=0,s=0,c=e.constructor,u=c.precision;if(fa(e)>16)throw Error(u3+fa(e));if(!e.s)return new c(uG);for(null==t?(u2=!1,a=u):a=t,i=new c(.03125);e.abs().gte(.1);)e=e.times(i),s+=5;for(a+=Math.log(u8(2,s))/Math.LN10*2+5|0,r=n=o=new c(uG),c.precision=a;;){if(n=ff(n.times(e),a),r=r.times(++l),fn((i=o.plus(fo(n,r,a))).d).slice(0,a)===fn(o.d).slice(0,a)){for(;s--;)o=ff(o.times(o),a);return c.precision=u,null==t?(u2=!0,ff(o,u)):o}o=i}}function fa(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function fl(e,t,r){if(t>e.LN10.sd())throw u2=!0,r&&(e.precision=r),Error(u5+"LN10 precision limit exceeded");return ff(new e(e.LN10),t)}function fs(e){for(var t="";e--;)t+="0";return t}function fc(e,t){var r,n,o,i,a,l,s,c,u,f=1,d=e,p=d.d,h=d.constructor,y=h.precision;if(d.s<1)throw Error(u5+(d.s?"NaN":"-Infinity"));if(d.eq(uG))return new h(0);if(null==t?(u2=!1,c=y):c=t,d.eq(10))return null==t&&(u2=!0),fl(h,c);if(h.precision=c+=10,n=(r=fn(p)).charAt(0),!(15e14>Math.abs(i=fa(d))))return s=fl(h,c+2,y).times(i+""),d=fc(new h(n+"."+r.slice(1)),c-10).plus(s),h.precision=y,null==t?(u2=!0,ff(d,y)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=fn((d=d.times(e)).d)).charAt(0),f++;for(i=fa(d),n>1?(d=new h("0."+r),i++):d=new h(n+"."+r.slice(1)),l=a=d=fo(d.minus(uG),d.plus(uG),c),u=ff(d.times(d),c),o=3;;){if(a=ff(a.times(u),c),fn((s=l.plus(fo(a,new h(o),c))).d).slice(0,c)===fn(l.d).slice(0,c))return l=l.times(2),0!==i&&(l=l.plus(fl(h,c+2,y).times(i+""))),l=fo(l,new h(f),c),h.precision=y,null==t?(u2=!0,ff(l,y)):l;l=s,o+=2}}function fu(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,e.e=u6((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),u2&&(e.e>u9||e.e<-u9))throw Error(u3+r)}else e.s=0,e.e=0,e.d=[0];return e}function ff(e,t,r){var n,o,i,a,l,s,c,u,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,c=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,c=i=f[u];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(l=c/(i=u8(10,a-o-1))%10|0,s=t<0||void 0!==f[u+1]||c%i,s=r<4?(l||s)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||s||6==r&&(n>0?o>0?c/u8(10,a-o):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return s?(i=fa(e),f.length=1,t=t-i-1,f[0]=u8(10,(7-t%7)%7),e.e=u6(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,i=1,u--):(f.length=u+1,i=u8(10,7-n),f[u]=o>0?(c/u8(10,a-o)%u8(10,o)|0)*i:0),s)for(;;){if(0==u){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}if(f[u]+=i,1e7!=f[u])break;f[u--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(u2&&(e.e>u9||e.e<-u9))throw Error(u3+fa(e));return e}function fd(e,t){var r,n,o,i,a,l,s,c,u,f,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),u2?ff(t,p):t;if(s=e.d,f=t.d,n=t.e,c=e.e,s=s.slice(),a=c-n){for((u=a<0)?(r=s,a=-a,l=f.length):(r=f,n=c,l=s.length),a>(o=Math.max(Math.ceil(p/7),l)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((u=(o=s.length)<(l=f.length))&&(l=o),o=0;o<l;o++)if(s[o]!=f[o]){u=s[o]<f[o];break}a=0}for(u&&(r=s,s=f,f=r,t.s=-t.s),l=s.length,o=f.length-l;o>0;--o)s[l++]=0;for(o=f.length;o>a;){if(s[--o]<f[o]){for(i=o;i&&0===s[--i];)s[i]=1e7-1;--s[i],s[o]+=1e7}s[o]-=f[o]}for(;0===s[--l];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,u2?ff(t,p):t):new d(0)}function fp(e,t,r){var n,o=fa(e),i=fn(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+fs(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+fs(-o-1)+i,r&&(n=r-a)>0&&(i+=fs(n))):o>=a?(i+=fs(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+fs(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=fs(n))),e.s<0?"-"+i:i}function fh(e,t){if(e.length>t)return e.length=t,!0}function fy(e){if(!e||"object"!=typeof e)throw Error(u5+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]])){if(u6(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(u4+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(u4+r+": "+n)}return this}var uV=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(u4+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return fu(this,e.toString())}if("string"!=typeof e)throw Error(u4+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,u7.test(e))fu(this,e);else throw Error(u4+e)}if(i.prototype=fe,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=fy,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});uG=new uV(1);let fm=uV;function fv(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var fg=function(e){return e},fb={},fx=function(e){return e===fb},fw=function(e){return function t(){return 0==arguments.length||1==arguments.length&&fx(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},fj=function(e){return function e(t,r){return 1===t?r:fw(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==fb}).length;return a>=t?r.apply(void 0,o):e(t-a,fw(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return fx(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return fv(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return fv(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fv(e,t)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},fO=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},fS=fj(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),fA=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return fg;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},fP=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},fk=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};fj(function(e,t,r){var n=+e;return n+r*(+t-n)}),fj(function(e,t,r){var n=t-+e;return(r-e)/(n=n||1/0)}),fj(function(e,t,r){var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let fN={rangeStep:function(e,t,r){for(var n=new fm(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){var t;return 0===e?1:Math.floor(new fm(e).abs().log(10).toNumber())+1}};function fE(e){return function(e){if(Array.isArray(e))return fT(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||fC(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fM(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,l=e[Symbol.iterator]();!(n=(a=l.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==l.return||l.return()}finally{if(o)throw i}}return r}}(e,t)||fC(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fC(e,t){if(e){if("string"==typeof e)return fT(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fT(e,t)}}function fT(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function f_(e){var t=fM(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function fI(e,t,r){if(e.lte(0))return new fm(0);var n=fN.getDigitCount(e.toNumber()),o=new fm(10).pow(n),i=e.div(o),a=1!==n?.05:.1,l=new fm(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?l:new fm(Math.ceil(l))}function fD(e,t,r){var n=1,o=new fm(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new fm(10).pow(fN.getDigitCount(e)-1),o=new fm(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new fm(Math.floor(e)))}else 0===e?o=new fm(Math.floor((t-1)/2)):r||(o=new fm(Math.floor(e)));var a=Math.floor((t-1)/2);return fA(fS(function(e){return o.add(new fm(e-a).mul(n)).toNumber()}),fO)(0,t)}var fR=fk(function(e){var t=fM(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),l=fM(f_([r,n]),2),s=l[0],c=l[1];if(s===-1/0||c===1/0){var u=c===1/0?[s].concat(fE(fO(0,o-1).map(function(){return 1/0}))):[].concat(fE(fO(0,o-1).map(function(){return-1/0})),[c]);return r>n?fP(u):u}if(s===c)return fD(s,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new fm(0),tickMin:new fm(0),tickMax:new fm(0)};var l=fI(new fm(r).sub(t).div(n-1),o,a),s=Math.ceil((i=t<=0&&r>=0?new fm(0):(i=new fm(t).add(r).div(2)).sub(new fm(i).mod(l))).sub(t).div(l).toNumber()),c=Math.ceil(new fm(r).sub(i).div(l).toNumber()),u=s+c+1;return u>n?e(t,r,n,o,a+1):(u<n&&(c=r>0?c+(n-u):c,s=r>0?s:s+(n-u)),{step:l,tickMin:i.sub(new fm(s).mul(l)),tickMax:i.add(new fm(c).mul(l))})}(s,c,a,i),d=f.step,p=f.tickMin,h=f.tickMax,y=fN.rangeStep(p,h.add(new fm(.1).mul(d)),d);return r>n?fP(y):y});fk(function(e){var t=fM(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),l=fM(f_([r,n]),2),s=l[0],c=l[1];if(s===-1/0||c===1/0)return[r,n];if(s===c)return fD(s,o,i);var u=fI(new fm(c).sub(s).div(a-1),i,0),f=fA(fS(function(e){return new fm(s).add(new fm(e).mul(u)).toNumber()}),fO)(0,a).filter(function(e){return e>=s&&e<=c});return r>n?fP(f):f});var fB=fk(function(e,t){var r=fM(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=fM(f_([n,o]),2),l=a[0],s=a[1];if(l===-1/0||s===1/0)return[n,o];if(l===s)return[l];var c=Math.max(t,2),u=fI(new fm(s).sub(l).div(c-1),i,0),f=[].concat(fE(fN.rangeStep(new fm(l),new fm(s).sub(new fm(.99).mul(u)),u)),[s]);return n>o?fP(f):f}),fL=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function f$(e){return(f$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fz(){return(fz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fF(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fU(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(fU=function(){return!!e})()}function fq(e){return(fq=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function fH(e,t){return(fH=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function fW(e,t,r){return(t=fV(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fV(e){var t=function(e,t){if("object"!=f$(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f$(t)?t:t+""}var fG=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=fq(e),function(e,t){if(t&&("object"===f$(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,fU()?Reflect.construct(e,t||[],fq(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fH(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,l=e.dataPointFormatter,s=e.xAxis,c=e.yAxis,u=nu(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fL),!1);"x"===this.props.direction&&"number"!==s.type&&o6(!1);var f=i.map(function(e){var i,f,d=l(e,o),p=d.x,h=d.y,y=d.value,m=d.errorVal;if(!m)return null;var v=[];if(Array.isArray(m)){var g=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(m,2)||function(e,t){if(e){if("string"==typeof e)return fF(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fF(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=g[0],f=g[1]}else i=f=m;if("vertical"===r){var b=s.scale,x=h+t,w=x+n,j=x-n,O=b(y-i),S=b(y+f);v.push({x1:S,y1:w,x2:S,y2:j}),v.push({x1:O,y1:x,x2:S,y2:x}),v.push({x1:O,y1:w,x2:O,y2:j})}else if("horizontal"===r){var A=c.scale,P=p+t,k=P-n,N=P+n,E=A(y-i),M=A(y+f);v.push({x1:k,y1:M,x2:N,y2:M}),v.push({x1:P,y1:E,x2:P,y2:M}),v.push({x1:k,y1:E,x2:N,y2:E})}return a().createElement(o9,fz({className:"recharts-errorBar",key:"bar-".concat(v.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},u),v.map(function(e){return a().createElement("line",fz({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return a().createElement(o9,{className:"recharts-errorBars"},f)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fV(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function fX(e){return(fX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fY(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fY(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=fX(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fX(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fY(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fW(fG,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),fW(fG,"displayName","ErrorBar");var fZ=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=na(r,oY);if(!a)return null;var l=oY.defaultProps,s=void 0!==l?fK(fK({},l),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?fK(fK({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:s.iconType||a||"square",color:f8(t),value:i||o,payload:n}}),fK(fK(fK({},s),oY.getWithHeight(a,o)),{},{payload:t,item:a})};function fJ(e){return(fJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fQ(e){return function(e){if(Array.isArray(e))return f0(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return f0(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f0(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f0(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function f1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f1(Object(r),!0).forEach(function(t){f5(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f5(e,t,r){var n;return(n=function(e,t){if("object"!=fJ(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==fJ(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f4(e,t,r){return rG()(e)||rG()(t)?r:rB(t)?rC()(e,t,r):rY()(t)?t(e):r}function f3(e,t,r,n){var o=uQ()(e,function(e){return f4(e,t)});if("number"===r){var i=o.filter(function(e){return rR(e)||parseFloat(e)});return i.length?[uZ()(i),uY()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!rG()(e)}):o).map(function(e){return rB(e)||e instanceof Date?e:""})}var f6=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var l=o.range,s=0;s<a;s++){var c=s>0?n[s-1].coordinate:n[a-1].coordinate,u=n[s].coordinate,f=s>=a-1?n[0].coordinate:n[s+1].coordinate,d=void 0;if(rI(u-c)!==rI(f-u)){var p=[];if(rI(f-u)===rI(l[1]-l[0])){d=f;var h=u+l[1]-l[0];p[0]=Math.min(h,(h+c)/2),p[1]=Math.max(h,(h+c)/2)}else{d=c;var y=f+l[1]-l[0];p[0]=Math.min(u,(y+u)/2),p[1]=Math.max(u,(y+u)/2)}var m=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>m[0]&&e<=m[1]||e>=p[0]&&e<=p[1]){i=n[s].index;break}}else{var v=Math.min(c,f),g=Math.max(c,f);if(e>(v+u)/2&&e<=(g+u)/2){i=n[s].index;break}}}else for(var b=0;b<a;b++)if(0===b&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b>0&&b<a-1&&e>(r[b].coordinate+r[b-1].coordinate)/2&&e<=(r[b].coordinate+r[b+1].coordinate)/2||b===a-1&&e>(r[b].coordinate+r[b-1].coordinate)/2){i=r[b].index;break}return i},f8=function(e){var t,r,n=e.type.displayName,o=null!==(t=e.type)&&void 0!==t&&t.defaultProps?f2(f2({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},f7=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),l=0,s=a.length;l<s;l++)for(var c=o[a[l]].stackGroups,u=Object.keys(c),f=0,d=u.length;f<d;f++){var p=c[u[f]],h=p.items,y=p.cateAxisId,m=h.filter(function(e){return nt(e.type).indexOf("Bar")>=0});if(m&&m.length){var v=m[0].type.defaultProps,g=void 0!==v?f2(f2({},v),m[0].props):m[0].props,b=g.barSize,x=g[y];i[x]||(i[x]=[]);var w=rG()(b)?t:b;i[x].push({item:m[0],stackList:m.slice(1),barSize:rG()(w)?void 0:rz(w,r,0)})}}return i},f9=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,l=e.maxBarSize,s=a.length;if(s<1)return null;var c=rz(r,o,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,d=o/s,p=a.reduce(function(e,t){return e+t.barSize||0},0);(p+=(s-1)*c)>=o&&(p-=(s-1)*c,c=0),p>=o&&d>0&&(f=!0,d*=.9,p=s*d);var h={offset:((o-p)/2>>0)-c,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+c,size:f?d:t.barSize}},n=[].concat(fQ(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},u)}else{var y=rz(n,o,0,!0);o-2*y-(s-1)*c<=0&&(c=0);var m=(o-2*y-(s-1)*c)/s;m>1&&(m>>=0);var v=l===+l?Math.min(m,l):m;t=a.reduce(function(e,t,r){var n=[].concat(fQ(e),[{item:t.item,position:{offset:y+(m+c)*r+(m-v)/2,size:v}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},u)}return t},de=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,l=fZ({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(l){var s=n||{},c=s.width,u=s.height,f=l.align,d=l.verticalAlign,p=l.layout;if(("vertical"===p||"horizontal"===p&&"middle"===d)&&"center"!==f&&rR(e[f]))return f2(f2({},e),{},f5({},f,e[f]+(c||0)));if(("horizontal"===p||"vertical"===p&&"center"===f)&&"middle"!==d&&rR(e[d]))return f2(f2({},e),{},f5({},d,e[d]+(u||0)))}return e},dt=function(e,t,r,n,o){var i=ni(t.props.children,fG).filter(function(e){var t;return t=e.props.direction,!!rG()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=f4(t,r);if(rG()(n))return e;var o=Array.isArray(n)?[uZ()(n),uY()(n)]:[n,n],i=a.reduce(function(e,r){var n=f4(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},dr=function(e,t,r,n,o){var i=t.map(function(t){return dt(e,t,r,o,n)}).filter(function(e){return!rG()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},dn=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&dt(e,t,i,n)||f3(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},di=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},da=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,l="scaleBand"===e.realScaleType?n.bandwidth()/2:2,s=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/l:0;return(s="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*rI(a[0]-a[1])*s:s,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+s,value:e,offset:s}}).filter(function(e){return!rE()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+s,value:e,index:t,offset:s}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+s,value:e,offset:s}}):n.domain().map(function(e,t){return{coordinate:n(e)+s,value:o?o[e]:e,index:t,offset:s}})},dl=new WeakMap,ds=function(e,t){if("function"!=typeof t)return e;dl.has(e)||dl.set(e,new WeakMap);var r=dl.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},dc=function(e,t,r){var o=e.scale,i=e.type,a=e.layout,l=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===l?{scale:aM(),realScaleType:"band"}:"radial"===a&&"angleAxis"===l?{scale:sm(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:aC(),realScaleType:"point"}:"category"===i?{scale:aM(),realScaleType:"band"}:{scale:sm(),realScaleType:"linear"};if(rk()(o)){var s="scale".concat(n8()(o));return{scale:(n[s]||aC)(),realScaleType:n[s]?s:"point"}}return rY()(o)?{scale:o}:{scale:aC(),realScaleType:"point"}},du=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),l=e(t[r-1]);(a<o||a>i||l<o||l>i)&&e.domain([t[0],t[r-1]])}},df={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var l=rE()(e[a][r][1])?e[a][r][0]:e[a][r][1];l>=0?(e[a][r][0]=o,e[a][r][1]=o+l,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+l,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}uI(e,t)}},none:uI,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,l=0;a<r;++a)l+=e[a][n][1]||0;o[n][1]+=o[n][0]=-l/2}uI(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var l=0,s=0,c=0;l<o;++l){for(var u=e[t[l]],f=u[a][1]||0,d=(f-(u[a-1][1]||0))/2,p=0;p<l;++p){var h=e[t[p]];d+=(h[a][1]||0)-(h[a-1][1]||0)}s+=f,c+=d*f}r[a-1][1]+=r[a-1][0]=i,s&&(i-=c/s)}r[a-1][1]+=r[a-1][0]=i,uI(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=rE()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},dd=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=df[r];return(function(){var e=op([]),t=uR,r=uI,n=uB;function o(o){var i,a,l=Array.from(e.apply(this,arguments),uL),s=l.length,c=-1;for(let e of o)for(i=0,++c;i<s;++i)(l[i][c]=[0,+n(e,l[i].key,c,o)]).data=e;for(i=0,a=uD(t(l));i<s;++i)l[a[i]].index=i;return r(l,a),l}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:op(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:op(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?uR:"function"==typeof e?e:op(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?uI:e,o):r},o})().keys(n).value(function(e,t){return+f4(e,t,0)}).order(uR).offset(o)(e)},dp=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!==(o=t.type)&&void 0!==o&&o.defaultProps?f2(f2({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var l=i[r],s=e[l]||{hasStack:!1,stackGroups:{}};if(rB(a)){var c=s.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};c.items.push(t),s.hasStack=!0,s.stackGroups[a]=c}else s.stackGroups[r$("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return f2(f2({},e),{},f5({},l,s))},{});return Object.keys(a).reduce(function(t,i){var l=a[i];return l.hasStack&&(l.stackGroups=Object.keys(l.stackGroups).reduce(function(t,i){var a=l.stackGroups[i];return f2(f2({},t),{},f5({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:dd(e,a.items,o)}))},{})),f2(f2({},t),{},f5({},i,l))},{})},dh=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,l=r||t.scale;if("auto"!==l&&"linear"!==l)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var s=e.domain();if(!s.length)return null;var c=fR(s,o,a);return e.domain([uZ()(c),uY()(c)]),{niceTicks:c}}return o&&"number"===n?{niceTicks:fB(e.domain(),o,a)}:null},dy=function(e,t){var r,n=(null!==(r=e.type)&&void 0!==r&&r.defaultProps?f2(f2({},e.type.defaultProps),e.props):e.props).stackId;if(rB(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},dm=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[uZ()(t.concat([e[0]]).filter(rR)),uY()(t.concat([e[1]]).filter(rR))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},dv=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,dg=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,db=function(e,t,r){if(rY()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(rR(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(dv.test(e[0])){var o=+dv.exec(e[0])[1];n[0]=t[0]-o}else rY()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(rR(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(dg.test(e[1])){var i=+dg.exec(e[1])[1];n[1]=t[1]+i}else rY()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},dx=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=nw()(t,function(e){return e.coordinate}),i=1/0,a=1,l=o.length;a<l;a++){var s=o[a],c=o[a-1];i=Math.min((s.coordinate||0)-(c.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},dw=function(e,t,r){return!e||!e.length||u1()(e,rC()(r,"type.defaultProps.domain"))?t:e},dj=function(e,t){var r=e.type.defaultProps?f2(f2({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,l=r.tooltipType,s=r.chartType,c=r.hide;return f2(f2({},nu(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:f8(e),value:f4(t,n),type:l,payload:t,chartType:s,hide:c})};function dO(e){return(dO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dS(Object(r),!0).forEach(function(t){dP(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dP(e,t,r){var n;return(n=function(e,t){if("object"!=dO(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==dO(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var dk=["Webkit","Moz","O","ms"],dN=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=dk.reduce(function(e,n){return dA(dA({},e),{},dP({},n+r,t))},{});return n[e]=t,n};function dE(e){return(dE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dM(){return(dM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dC(Object(r),!0).forEach(function(t){dB(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d_(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dL(n.key),n)}}function dI(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dI=function(){return!!e})()}function dD(e){return(dD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dR(e,t){return(dR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dB(e,t,r){return(t=dL(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dL(e){var t=function(e,t){if("object"!=dE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dE(t)?t:t+""}var d$=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var l=t.length,s=aC().domain(o3()(0,l)).range([o,o+i-a]),c=s.domain().map(function(e){return s(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(r),endX:s(n),scale:s,scaleValues:c}},dz=function(e){return e.changedTouches&&!!e.changedTouches.length},dF=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=dD(r),dB(t=function(e,t){if(t&&("object"===dE(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,dI()?Reflect.construct(r,o||[],dD(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),dB(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),dB(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),dB(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),dB(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),dB(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),dB(t,"handleSlideDragStart",function(e){var r=dz(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dR(e,t)}(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,l=i.data.length-1,s=Math.min(t,r),c=Math.max(t,r),u=n.getIndexInRange(o,s),f=n.getIndexInRange(o,c);return{startIndex:u-u%a,endIndex:f===l?l:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=f4(r[e],o,e);return rY()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,l=i.width,s=i.travellerWidth,c=i.startIndex,u=i.endIndex,f=i.onChange,d=e.pageX-r;d>0?d=Math.min(d,a+l-s-o,a+l-s-n):d<0&&(d=Math.max(d,a-n,a-o));var p=this.getIndex({startX:n+d,endX:o+d});(p.startIndex!==c||p.endIndex!==u)&&f&&f(p),this.setState({startX:n+d,endX:o+d,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=dz(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],l=this.props,s=l.x,c=l.width,u=l.travellerWidth,f=l.onChange,d=l.gap,p=l.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,s+c-u-a):y<0&&(y=Math.max(y,s-a)),h[n]=a+y;var m=this.getIndex(h),v=m.startIndex,g=m.endIndex,b=function(){var e=p.length-1;return"startX"===n&&(o>i?v%d==0:g%d==0)||!!(o<i)&&g===e||"endX"===n&&(o>i?g%d==0:v%d==0)||!!(o>i)&&g===e};this.setState(dB(dB({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&b()&&f(m)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,l=this.state[t],s=o.indexOf(l);if(-1!==s){var c=s+e;if(-1!==c&&!(c>=o.length)){var u=o[c];("startX"!==t||!(u>=a))&&("endX"!==t||!(u<=i))&&this.setState(dB({},t,u),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,l=e.stroke;return a().createElement("rect",{stroke:l,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,l=e.data,s=e.children,c=e.padding,u=i.Children.only(s);return u?a().cloneElement(u,{x:t,y:r,width:n,height:o,margin:c,compact:!0,data:l}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,l=this.props,s=l.y,c=l.travellerWidth,u=l.height,f=l.traveller,d=l.ariaLabel,p=l.data,h=l.startIndex,y=l.endIndex,m=Math.max(e,this.props.x),v=dT(dT({},nu(this.props,!1)),{},{x:m,y:s,width:c,height:u}),g=d||"Min value: ".concat(null===(r=p[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=p[y])||void 0===o?void 0:o.name);return a().createElement(o9,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,v))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,l=r.travellerWidth,s=Math.min(e,t)+l,c=Math.max(Math.abs(t-e)-l,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:s,y:n,width:c,height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,l=e.stroke,s=this.state,c=s.startX,u=s.endX,f={pointerEvents:"none",fill:l};return a().createElement(o9,{className:"recharts-brush-texts"},a().createElement(lr,dM({textAnchor:"end",verticalAnchor:"middle",x:Math.min(c,u)-5,y:n+o/2},f),this.getTextOfTick(t)),a().createElement(lr,dM({textAnchor:"start",verticalAnchor:"middle",x:Math.max(c,u)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,l=e.width,s=e.height,c=e.alwaysShowText,u=this.state,f=u.startX,d=u.endX,p=u.isTextActive,h=u.isSlideMoving,y=u.isTravellerMoving,m=u.isTravellerFocused;if(!t||!t.length||!rR(o)||!rR(i)||!rR(l)||!rR(s)||l<=0||s<=0)return null;var v=(0,rO.A)("recharts-brush",r),g=1===a().Children.count(n),b=dN("userSelect","none");return a().createElement(o9,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,d),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(d,"endX"),(p||h||y||m||c)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,l=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:t+1,y1:l,x2:t+n-1,y2:l,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:t+1,y1:l+2,x2:t+n-1,y2:l+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rY()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,l=e.startIndex,s=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return dT({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?d$({data:r,width:n,x:o,travellerWidth:i,startIndex:l,endIndex:s}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var c=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:c}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&d_(n.prototype,t),r&&d_(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function dU(e){return(dU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dq(Object(r),!0).forEach(function(t){dW(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dW(e,t,r){var n;return(n=function(e,t){if("object"!=dU(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==dU(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dV(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}dB(dF,"displayName","Brush"),dB(dF,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var dG=Math.PI/180,dX=function(e,t,r,n){return{x:e+Math.cos(-dG*n)*r,y:t+Math.sin(-dG*n)*r}},dY=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},dK=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},dZ=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=dK({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var l=Math.acos((r-o)/a);return n>i&&(l=2*Math.PI-l),{radius:a,angle:180*l/Math.PI,angleInRadian:l}},dJ=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},dQ=function(e,t){var r,n=dZ({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,l=t.outerRadius;if(o<a||o>l)return!1;if(0===o)return!0;var s=dJ(t),c=s.startAngle,u=s.endAngle,f=i;if(c<=u){for(;f>u;)f-=360;for(;f<c;)f+=360;r=f>=c&&f<=u}else{for(;f>c;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=c}return r?dH(dH({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},d0=function(e){return(0,i.isValidElement)(e)||rY()(e)||"boolean"==typeof e?"":e.className};function d1(e){return(d1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var d2=["offset"];function d5(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function d4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d3(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d4(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=d1(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d1(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d6(){return(d6=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var d8=function(e){var t=e.value,r=e.formatter,n=rG()(e.children)?t:e.children;return rY()(r)?r(n):n},d7=function(e,t,r){var n,o,i=e.position,l=e.viewBox,s=e.offset,c=e.className,u=l.cx,f=l.cy,d=l.innerRadius,p=l.outerRadius,h=l.startAngle,y=l.endAngle,m=l.clockWise,v=(d+p)/2,g=rI(y-h)*Math.min(Math.abs(y-h),360),b=g>=0?1:-1;"insideStart"===i?(n=h+b*s,o=m):"insideEnd"===i?(n=y-b*s,o=!m):"end"===i&&(n=y+b*s,o=m),o=g<=0?o:!o;var x=dX(u,f,v,n),w=dX(u,f,v,n+(o?1:-1)*359),j="M".concat(x.x,",").concat(x.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),O=rG()(e.id)?r$("recharts-radial-line-"):e.id;return a().createElement("text",d6({},r,{dominantBaseline:"central",className:(0,rO.A)("recharts-radial-bar-label",c)}),a().createElement("defs",null,a().createElement("path",{id:O,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(O)},t))},d9=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,l=t.outerRadius,s=(t.startAngle+t.endAngle)/2;if("outside"===n){var c=dX(o,i,l+r,s),u=c.x;return{x:u,y:c.y,textAnchor:u>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=dX(o,i,(a+l)/2,s);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},pe=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,l=t.width,s=t.height,c=s>=0?1:-1,u=c*n,f=c>0?"end":"start",d=c>0?"start":"end",p=l>=0?1:-1,h=p*n,y=p>0?"end":"start",m=p>0?"start":"end";if("top"===o)return d3(d3({},{x:i+l/2,y:a-c*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:l}:{});if("bottom"===o)return d3(d3({},{x:i+l/2,y:a+s+u,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+s),0),width:l}:{});if("left"===o){var v={x:i-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"};return d3(d3({},v),r?{width:Math.max(v.x-r.x,0),height:s}:{})}if("right"===o){var g={x:i+l+h,y:a+s/2,textAnchor:m,verticalAnchor:"middle"};return d3(d3({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:s}:{})}var b=r?{width:l,height:s}:{};return"insideLeft"===o?d3({x:i+h,y:a+s/2,textAnchor:m,verticalAnchor:"middle"},b):"insideRight"===o?d3({x:i+l-h,y:a+s/2,textAnchor:y,verticalAnchor:"middle"},b):"insideTop"===o?d3({x:i+l/2,y:a+u,textAnchor:"middle",verticalAnchor:d},b):"insideBottom"===o?d3({x:i+l/2,y:a+s-u,textAnchor:"middle",verticalAnchor:f},b):"insideTopLeft"===o?d3({x:i+h,y:a+u,textAnchor:m,verticalAnchor:d},b):"insideTopRight"===o?d3({x:i+l-h,y:a+u,textAnchor:y,verticalAnchor:d},b):"insideBottomLeft"===o?d3({x:i+h,y:a+s-u,textAnchor:m,verticalAnchor:f},b):"insideBottomRight"===o?d3({x:i+l-h,y:a+s-u,textAnchor:y,verticalAnchor:f},b):rZ()(o)&&(rR(o.x)||rD(o.x))&&(rR(o.y)||rD(o.y))?d3({x:i+rz(o.x,l),y:a+rz(o.y,s),textAnchor:"end",verticalAnchor:"end"},b):d3({x:i+l/2,y:a+s/2,textAnchor:"middle",verticalAnchor:"middle"},b)};function pt(e){var t,r=e.offset,n=d3({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,d2)),o=n.viewBox,l=n.position,s=n.value,c=n.children,u=n.content,f=n.className,d=n.textBreakAll;if(!o||rG()(s)&&rG()(c)&&!(0,i.isValidElement)(u)&&!rY()(u))return null;if((0,i.isValidElement)(u))return(0,i.cloneElement)(u,n);if(rY()(u)){if(t=(0,i.createElement)(u,n),(0,i.isValidElement)(t))return t}else t=d8(n);var p="cx"in o&&rR(o.cx),h=nu(n,!0);if(p&&("insideStart"===l||"insideEnd"===l||"end"===l))return d7(n,t,h);var y=p?d9(n):pe(n);return a().createElement(lr,d6({className:(0,rO.A)("recharts-label",void 0===f?"":f)},h,y,{breakAll:d}),t)}pt.displayName="Label";var pr=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,l=e.radius,s=e.innerRadius,c=e.outerRadius,u=e.x,f=e.y,d=e.top,p=e.left,h=e.width,y=e.height,m=e.clockWise,v=e.labelViewBox;if(v)return v;if(rR(h)&&rR(y)){if(rR(u)&&rR(f))return{x:u,y:f,width:h,height:y};if(rR(d)&&rR(p))return{x:d,y:p,width:h,height:y}}return rR(u)&&rR(f)?{x:u,y:f,width:0,height:0}:rR(t)&&rR(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:s||0,outerRadius:c||l||a||0,clockWise:m}:e.viewBox?e.viewBox:{}};pt.parseViewBox=pr,pt.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var l=e.children,s=pr(e),c=ni(l,pt).map(function(e,r){return(0,i.cloneElement)(e,{viewBox:t||s,key:"label-".concat(r)})});if(!o)return c;return[(r=e.label,n=t||s,r?!0===r?a().createElement(pt,{key:"label-implicit",viewBox:n}):rB(r)?a().createElement(pt,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===pt?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(pt,{key:"label-implicit",content:r,viewBox:n}):rY()(r)?a().createElement(pt,{key:"label-implicit",content:r,viewBox:n}):rZ()(r)?a().createElement(pt,d6({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(e){if(Array.isArray(e))return d5(e)}(c)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(c)||function(e,t){if(e){if("string"==typeof e)return d5(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d5(e,t)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var pn=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},po=r(69691),pi=r.n(po),pa=r(47212),pl=r.n(pa);function ps(e){return(ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pc(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pp(n.key),n)}}function pu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pu(Object(r),!0).forEach(function(t){pd(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pd(e,t,r){return(t=pp(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pp(e){var t=function(e,t){if("object"!=ps(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ps(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ps(t)?t:t+""}var ph=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},py=function(){var e,t;function r(e){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&pc(r.prototype,e),t&&pc(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();pd(py,"EPS",1e-4);var pm=function(e){var t=Object.keys(e).reduce(function(t,r){return pf(pf({},t),{},pd({},r,py.create(e[r])))},{});return pf(pf({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return pi()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return pl()(e,function(e,r){return t[r].isInRange(e)})}})};function pv(){return(pv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pg(e){return(pg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function px(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pb(Object(r),!0).forEach(function(t){pS(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pw=function(){return!!e})()}function pj(e){return(pj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pO(e,t){return(pO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pS(e,t,r){return(t=pA(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pA(e){var t=function(e,t){if("object"!=pg(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pg(t)?t:t+""}var pP=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=pm({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return pn(e,"discard")&&!i.isInRange(a)?null:a},pk=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pj(e),function(e,t){if(t&&("object"===pg(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pw()?Reflect.construct(e,t||[],pj(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pO(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,o=e.r,i=e.alwaysShow,l=e.clipPathId,s=rB(t),c=rB(n);if(rW(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!c)return null;var u=pP(this.props);if(!u)return null;var f=u.x,d=u.y,p=this.props,h=p.shape,y=p.className,m=px(px({clipPath:pn(this.props,"hidden")?"url(#".concat(l,")"):void 0},nu(this.props,!0)),{},{cx:f,cy:d});return a().createElement(o9,{className:(0,rO.A)("recharts-reference-dot",y)},r.renderDot(h,m),pt.renderCallByParent(this.props,{x:f-o,y:d-o,width:2*o,height:2*o}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pA(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);pS(pk,"displayName","ReferenceDot"),pS(pk,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),pS(pk,"renderDot",function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rY()(e)?e(t):a().createElement(it,pv({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var pN=r(67367),pE=r.n(pN);r(22964);var pM=r(86451),pC=r.n(pM)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),pT=(0,i.createContext)(void 0),p_=(0,i.createContext)(void 0),pI=(0,i.createContext)(void 0),pD=(0,i.createContext)({}),pR=(0,i.createContext)(void 0),pB=(0,i.createContext)(0),pL=(0,i.createContext)(0),p$=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,l=e.children,s=e.width,c=e.height,u=pC(o);return a().createElement(pT.Provider,{value:r},a().createElement(p_.Provider,{value:n},a().createElement(pD.Provider,{value:o},a().createElement(pI.Provider,{value:u},a().createElement(pR.Provider,{value:i},a().createElement(pB.Provider,{value:c},a().createElement(pL.Provider,{value:s},l)))))))},pz=function(e){var t=(0,i.useContext)(pT);null==t&&o6(!1);var r=t[e];return null==r&&o6(!1),r},pF=function(e){var t=(0,i.useContext)(p_);null==t&&o6(!1);var r=t[e];return null==r&&o6(!1),r};function pU(e){return(pU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pq(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pq=function(){return!!e})()}function pH(e){return(pH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pW(e,t){return(pW=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pV(Object(r),!0).forEach(function(t){pX(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pX(e,t,r){return(t=pY(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pY(e){var t=function(e,t){if("object"!=pU(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pU(t)?t:t+""}function pK(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function pZ(){return(pZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var pJ=function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rY()(e)?e(t):a().createElement("line",pZ({},t,{className:"recharts-reference-line-line"}))},pQ=function(e,t,r,n,o,i,a,l,s){var c=o.x,u=o.y,f=o.width,d=o.height;if(r){var p=s.y,h=e.y.apply(p,{position:i});if(pn(s,"discard")&&!e.y.isInRange(h))return null;var y=[{x:c+f,y:h},{x:c,y:h}];return"left"===l?y.reverse():y}if(t){var m=s.x,v=e.x.apply(m,{position:i});if(pn(s,"discard")&&!e.x.isInRange(v))return null;var g=[{x:v,y:u+d},{x:v,y:u}];return"top"===a?g.reverse():g}if(n){var b=s.segment.map(function(t){return e.apply(t,{position:i})});return pn(s,"discard")&&pE()(b,function(t){return!e.isInRange(t)})?null:b}return null};function p0(e){var t,r=e.x,n=e.y,o=e.segment,l=e.xAxisId,s=e.yAxisId,c=e.shape,u=e.className,f=e.alwaysShow,d=(0,i.useContext)(pR),p=pz(l),h=pF(s),y=(0,i.useContext)(pI);if(!d||!y)return null;rW(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=pQ(pm({x:p.scale,y:h.scale}),rB(r),rB(n),o&&2===o.length,y,e.position,p.orientation,h.orientation,e);if(!m)return null;var v=function(e){if(Array.isArray(e))return e}(m)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(m,2)||function(e,t){if(e){if("string"==typeof e)return pK(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pK(e,t)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=v[0],b=g.x,x=g.y,w=v[1],j=w.x,O=w.y,S=pG(pG({clipPath:pn(e,"hidden")?"url(#".concat(d,")"):void 0},nu(e,!0)),{},{x1:b,y1:x,x2:j,y2:O});return a().createElement(o9,{className:(0,rO.A)("recharts-reference-line",u)},pJ(c,S),pt.renderCallByParent(e,ph({x:(t={x1:b,y1:x,x2:j,y2:O}).x1,y:t.y1},{x:t.x2,y:t.y2})))}var p1=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=pH(e),function(e,t){if(t&&("object"===pU(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pq()?Reflect.construct(e,t||[],pH(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pW(e,t)}(r,e),t=[{key:"render",value:function(){return a().createElement(p0,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pY(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function p2(){return(p2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p5(e){return(p5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p3(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p4(Object(r),!0).forEach(function(t){p9(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}pX(p1,"displayName","ReferenceLine"),pX(p1,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function p6(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(p6=function(){return!!e})()}function p8(e){return(p8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function p7(e,t){return(p7=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p9(e,t,r){return(t=he(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function he(e){var t=function(e,t){if("object"!=p5(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p5(t)?t:t+""}var ht=function(e,t,r,n,o){var i=o.x1,a=o.x2,l=o.y1,s=o.y2,c=o.xAxis,u=o.yAxis;if(!c||!u)return null;var f=pm({x:c.scale,y:u.scale}),d={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(l,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(s,{position:"end"}):f.y.rangeMax};return!pn(o,"discard")||f.isInRange(d)&&f.isInRange(p)?ph(d,p):null},hr=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=p8(e),function(e,t){if(t&&("object"===p5(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,p6()?Reflect.construct(e,t||[],p8(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p7(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,o=e.y1,i=e.y2,l=e.className,s=e.alwaysShow,c=e.clipPathId;rW(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var u=rB(t),f=rB(n),d=rB(o),p=rB(i),h=this.props.shape;if(!u&&!f&&!d&&!p&&!h)return null;var y=ht(u,f,d,p,this.props);if(!y&&!h)return null;var m=pn(this.props,"hidden")?"url(#".concat(c,")"):void 0;return a().createElement(o9,{className:(0,rO.A)("recharts-reference-area",l)},r.renderRect(h,p3(p3({clipPath:m},nu(this.props,!0)),y)),pt.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,he(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function hn(e){return function(e){if(Array.isArray(e))return ho(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ho(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ho(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ho(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}p9(hr,"displayName","ReferenceArea"),p9(hr,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),p9(hr,"renderRect",function(e,t){var r;return a().isValidElement(e)?a().cloneElement(e,t):rY()(e)?e(t):a().createElement(aj,p2({},t,{className:"recharts-reference-area-rect"}))});var hi=function(e,t,r,n,o){var i=ni(e,p1),a=ni(e,pk),l=[].concat(hn(i),hn(a)),s=ni(e,hr),c="".concat(n,"Id"),u=n[0],f=t;if(l.length&&(f=l.reduce(function(e,t){if(t.props[c]===r&&pn(t.props,"extendDomain")&&rR(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),s.length){var d="".concat(u,"1"),p="".concat(u,"2");f=s.reduce(function(e,t){if(t.props[c]===r&&pn(t.props,"extendDomain")&&rR(t.props[d])&&rR(t.props[p])){var n=t.props[d],o=t.props[p];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return rR(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},ha=r(11117),hl=new(r.n(ha)()),hs="recharts.syncMouseEvents";function hc(e){return(hc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hu(e,t,r){return(t=hf(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hf(e){var t=function(e,t){if("object"!=hc(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hc(t)?t:t+""}var hd=function(){var e,t;return e=function e(){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,e),hu(this,"activeIndex",0),hu(this,"coordinateList",[]),hu(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,l=void 0===a?null:a,s=e.offset,c=void 0===s?null:s,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!==(t=null!=n?n:this.coordinateList)&&void 0!==t?t:[],this.container=null!=i?i:this.container,this.layout=null!=l?l:this.layout,this.offset=null!=c?c:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,l=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,s=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,c=o+this.offset.top+i/2+s;this.mouseHandlerCallback({pageX:n+a+l,pageY:c})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hf(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}(),hp=r(38404),hh=r.n(hp),hy=r(98451),hm=r.n(hy);function hv(e){return(hv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hg(){return(hg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hb(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function hx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hx(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hv(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hv(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hx(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hj=function(e,t,r,n,o){var i,a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+o)+"L ".concat(e+r-a/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},hO={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},hS=function(e){var t,r=hw(hw({},hO),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(t,2)||function(e,t){if(e){if("string"==typeof e)return hb(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hb(e,t)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),l=o[0],s=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&s(e)}catch(e){}},[]);var c=r.x,u=r.y,f=r.upperWidth,d=r.lowerWidth,p=r.height,h=r.className,y=r.animationEasing,m=r.animationDuration,v=r.animationBegin,g=r.isUpdateAnimationActive;if(c!==+c||u!==+u||f!==+f||d!==+d||p!==+p||0===f&&0===d||0===p)return null;var b=(0,rO.A)("recharts-trapezoid",h);return g?a().createElement(ap,{canBegin:l>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:u},to:{upperWidth:f,lowerWidth:d,height:p,x:c,y:u},duration:m,animationEasing:y,isActive:g},function(e){var t=e.upperWidth,o=e.lowerWidth,i=e.height,s=e.x,c=e.y;return a().createElement(ap,{canBegin:l>0,from:"0px ".concat(-1===l?1:l,"px"),to:"".concat(l,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:m,easing:y},a().createElement("path",hg({},nu(r,!0),{className:b,d:hj(s,c,t,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",hg({},nu(r,!0),{className:b,d:hj(c,u,f,d,p)})))};function hA(e){return(hA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hP(){return(hP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function hk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hk(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hA(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hA(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hE=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,l=e.cornerRadius,s=e.cornerIsExternal,c=l*(a?1:-1)+n,u=Math.asin(l/c)/dG,f=s?o:o+i*u;return{center:dX(t,r,c,f),circleTangency:dX(t,r,n,f),lineTangency:dX(t,r,c*Math.cos(u*dG),s?o-i*u:o),theta:u}},hM=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.startAngle,a=e.endAngle,l=rI(a-i)*Math.min(Math.abs(a-i),359.999),s=i+l,c=dX(t,r,o,i),u=dX(t,r,o,s),f="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>s),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var d=dX(t,r,n,i),p=dX(t,r,n,s);f+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},hC=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,l=e.cornerIsExternal,s=e.startAngle,c=e.endAngle,u=rI(c-s),f=hE({cx:t,cy:r,radius:o,angle:s,sign:u,cornerRadius:i,cornerIsExternal:l}),d=f.circleTangency,p=f.lineTangency,h=f.theta,y=hE({cx:t,cy:r,radius:o,angle:c,sign:-u,cornerRadius:i,cornerIsExternal:l}),m=y.circleTangency,v=y.lineTangency,g=y.theta,b=l?Math.abs(s-c):Math.abs(s-c)-h-g;if(b<0)return a?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):hM({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:s,endAngle:c});var x="M ".concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(b>180),",").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n  ");if(n>0){var w=hE({cx:t,cy:r,radius:n,angle:s,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:l}),j=w.circleTangency,O=w.lineTangency,S=w.theta,A=hE({cx:t,cy:r,radius:n,angle:c,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:l}),P=A.circleTangency,k=A.lineTangency,N=A.theta,E=l?Math.abs(s-c):Math.abs(s-c)-S-N;if(E<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(u>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},hT={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},h_=function(e){var t,r=hN(hN({},hT),e),n=r.cx,o=r.cy,i=r.innerRadius,l=r.outerRadius,s=r.cornerRadius,c=r.forceCornerRadius,u=r.cornerIsExternal,f=r.startAngle,d=r.endAngle,p=r.className;if(l<i||f===d)return null;var h=(0,rO.A)("recharts-sector",p),y=l-i,m=rz(s,y,0,!0);return t=m>0&&360>Math.abs(f-d)?hC({cx:n,cy:o,innerRadius:i,outerRadius:l,cornerRadius:Math.min(m,y/2),forceCornerRadius:c,cornerIsExternal:u,startAngle:f,endAngle:d}):hM({cx:n,cy:o,innerRadius:i,outerRadius:l,startAngle:f,endAngle:d}),a().createElement("path",hP({},nu(r,!0),{className:h,d:t,role:"img"}))},hI=["option","shapeType","propTransformer","activeClassName","isActive"];function hD(e){return(hD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function hR(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hR(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=hD(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=hD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hD(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hR(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hL(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return a().createElement(aj,r);case"trapezoid":return a().createElement(hS,r);case"sector":return a().createElement(h_,r);case"symbols":if("symbols"===t)return a().createElement(oN,r);break;default:return null}}function h$(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,l=e.activeClassName,s=e.isActive,c=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,hI);if((0,i.isValidElement)(r))t=(0,i.cloneElement)(r,hB(hB({},c),(0,i.isValidElement)(r)?r.props:r));else if(rY()(r))t=r(c);else if(hh()(r)&&!hm()(r)){var u=(void 0===o?function(e,t){return hB(hB({},t),e)}:o)(r,c);t=a().createElement(hL,{shapeType:n,elementProps:u})}else t=a().createElement(hL,{shapeType:n,elementProps:c});return s?a().createElement(o9,{className:void 0===l?"recharts-active-shape":l},t):t}function hz(e,t){return null!=t&&"trapezoids"in e.props}function hF(e,t){return null!=t&&"sectors"in e.props}function hU(e,t){return null!=t&&"points"in e.props}function hq(e,t){var r,n,o=e.x===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.y)||e.y===t.y;return o&&i}function hH(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function hW(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}function hV(){}function hG(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function hX(e){this._context=e}function hY(e){this._context=e}function hK(e){this._context=e}hX.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:hG(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:hG(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},hY.prototype={areaStart:hV,areaEnd:hV,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:hG(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},hK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:hG(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class hZ{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function hJ(e){this._context=e}function hQ(e){this._context=e}function h0(e){return new hQ(e)}hJ.prototype={areaStart:hV,areaEnd:hV,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function h1(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function h2(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function h5(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,l=(i-n)/3;e._context.bezierCurveTo(n+l,o+l*t,i-l,a-l*r,i,a)}function h4(e){this._context=e}function h3(e){this._context=new h6(e)}function h6(e){this._context=e}function h8(e){this._context=e}function h7(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function h9(e,t){this._context=e,this._t=t}function ye(e){return e[0]}function yt(e){return e[1]}function yr(e,t){var r=op(!0),n=null,o=h0,i=null,a=ob(l);function l(l){var s,c,u,f=(l=uD(l)).length,d=!1;for(null==n&&(i=o(u=a())),s=0;s<=f;++s)!(s<f&&r(c=l[s],s,l))===d&&((d=!d)?i.lineStart():i.lineEnd()),d&&i.point(+e(c,s,l),+t(c,s,l));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?ye:op(e),t="function"==typeof t?t:void 0===t?yt:op(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:op(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:op(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:op(!!e),l):r},l.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),l):o},l.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),l):n},l}function yn(e,t,r){var n=null,o=op(!0),i=null,a=h0,l=null,s=ob(c);function c(c){var u,f,d,p,h,y=(c=uD(c)).length,m=!1,v=Array(y),g=Array(y);for(null==i&&(l=a(h=s())),u=0;u<=y;++u){if(!(u<y&&o(p=c[u],u,c))===m){if(m=!m)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(v[d],g[d]);l.lineEnd(),l.areaEnd()}}m&&(v[u]=+e(p,u,c),g[u]=+t(p,u,c),l.point(n?+n(p,u,c):v[u],r?+r(p,u,c):g[u]))}if(h)return l=null,h+""||null}function u(){return yr().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?ye:op(+e),t="function"==typeof t?t:void 0===t?op(0):op(+t),r="function"==typeof r?r:void 0===r?yt:op(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:op(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:op(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:op(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:op(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:op(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:op(+e),c):r},c.lineX0=c.lineY0=function(){return u().x(e).y(t)},c.lineY1=function(){return u().x(e).y(r)},c.lineX1=function(){return u().x(n).y(t)},c.defined=function(e){return arguments.length?(o="function"==typeof e?e:op(!!e),c):o},c.curve=function(e){return arguments.length?(a=e,null!=i&&(l=a(i)),c):a},c.context=function(e){return arguments.length?(null==e?i=l=null:l=a(i=e),c):i},c}function yo(e){return(yo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yi(){return(yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function ya(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ya(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=yo(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yo(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yo(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ya(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}hQ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},h4.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:h5(this,this._t0,h2(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,h5(this,h2(this,r=h1(this,e,t)),r);break;default:h5(this,this._t0,r=h1(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(h3.prototype=Object.create(h4.prototype)).point=function(e,t){h4.prototype.point.call(this,t,e)},h6.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},h8.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=h7(e),o=h7(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},h9.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var ys={curveBasisClosed:function(e){return new hY(e)},curveBasisOpen:function(e){return new hK(e)},curveBasis:function(e){return new hX(e)},curveBumpX:function(e){return new hZ(e,!0)},curveBumpY:function(e){return new hZ(e,!1)},curveLinearClosed:function(e){return new hJ(e)},curveLinear:h0,curveMonotoneX:function(e){return new h4(e)},curveMonotoneY:function(e){return new h3(e)},curveNatural:function(e){return new h8(e)},curveStep:function(e){return new h9(e,.5)},curveStepAfter:function(e){return new h9(e,1)},curveStepBefore:function(e){return new h9(e,0)}},yc=function(e){return e.x===+e.x&&e.y===+e.y},yu=function(e){return e.x},yf=function(e){return e.y},yd=function(e,t){if(rY()(e))return e;var r="curve".concat(n8()(e));return("curveMonotone"===r||"curveBump"===r)&&t?ys["".concat(r).concat("vertical"===t?"Y":"X")]:ys[r]||h0},yp=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,l=e.connectNulls,s=void 0!==l&&l,c=yd(void 0===r?"linear":r,a),u=s?o.filter(function(e){return yc(e)}):o;if(Array.isArray(i)){var f=s?i.filter(function(e){return yc(e)}):i,d=u.map(function(e,t){return yl(yl({},e),{},{base:f[t]})});return(t="vertical"===a?yn().y(yf).x1(yu).x0(function(e){return e.base.x}):yn().x(yu).y1(yf).y0(function(e){return e.base.y})).defined(yc).curve(c),t(d)}return(t="vertical"===a&&rR(i)?yn().y(yf).x1(yu).x0(i):rR(i)?yn().x(yu).y1(yf).y0(i):yr().x(yu).y(yf)).defined(yc).curve(c),t(u)},yh=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?yp(e):n;return a().createElement("path",yi({},nu(e,!1),r3(e),{className:(0,rO.A)("recharts-curve",t),d:i,ref:o}))};function yy(e){return(yy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ym=["x","y","top","left","width","height","className"];function yv(){return(yv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var yb=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,l=void 0===i?0:i,s=e.left,c=void 0===s?0:s,u=e.width,f=void 0===u?0:u,d=e.height,p=void 0===d?0:d,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yg(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=yy(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yy(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yg(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:l,left:c,width:f,height:p},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ym));return rR(r)&&rR(o)&&rR(f)&&rR(p)&&rR(l)&&rR(c)?a().createElement("path",yv({},nu(y,!0),{className:(0,rO.A)("recharts-cross",h),d:"M".concat(r,",").concat(l,"v").concat(p,"M").concat(c,",").concat(o,"h").concat(f)})):null};function yx(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[dX(t,r,n,o),dX(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function yw(e){return(yw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yj(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=yw(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yw(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yS(e){var t,r,n,o,a=e.element,l=e.tooltipEventType,s=e.isActive,c=e.activeCoordinate,u=e.activePayload,f=e.offset,d=e.activeTooltipIndex,p=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!m||!s||!c||"ScatterChart"!==y&&"axis"!==l)return null;var v=yh;if("ScatterChart"===y)o=c,v=yb;else if("BarChart"===y)t=p/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?c.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:c.y-t,width:"horizontal"===h?p:f.width-1,height:"horizontal"===h?f.height-1:p},v=aj;else if("radial"===h){var g=yx(c),b=g.cx,x=g.cy,w=g.radius;o={cx:b,cy:x,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},v=h_}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return yx(t);var l=t.cx,s=t.cy,c=t.innerRadius,u=t.outerRadius,f=t.angle,d=dX(l,s,c,f),p=dX(l,s,u,f);n=d.x,o=d.y,i=p.x,a=p.y}return[{x:n,y:o},{x:i,y:a}]}(h,c,f)},v=yh;var j=yO(yO(yO(yO({stroke:"#ccc",pointerEvents:"none"},f),o),nu(m,!1)),{},{payload:u,payloadIndex:d,className:(0,rO.A)("recharts-tooltip-cursor",m.className)});return(0,i.isValidElement)(m)?(0,i.cloneElement)(m,j):(0,i.createElement)(v,j)}var yA=["item"],yP=["children","className","width","height","style","compact","title","desc"];function yk(e){return(yk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function yN(){return(yN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function yE(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||yD(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yM(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function yC(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(yC=function(){return!!e})()}function yT(e){return(yT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function y_(e,t){return(y_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function yI(e){return function(e){if(Array.isArray(e))return yR(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||yD(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yD(e,t){if(e){if("string"==typeof e)return yR(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yR(e,t)}}function yR(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function yB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yB(Object(r),!0).forEach(function(t){y$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y$(e,t,r){return(t=yz(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yz(e){var t=function(e,t){if("object"!=yk(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=yk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==yk(t)?t:t+""}var yF={xAxis:["bottom","top"],yAxis:["left","right"]},yU={width:"100%",height:"100%"},yq={x:0,y:0};function yH(e){return e}var yW=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return yL(yL(yL({},n),dX(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var l=o.coordinate,s=n.angle;return yL(yL(yL({},n),dX(n.cx,n.cy,l,s)),{},{angle:s,radius:l})}return yq},yV=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(yI(e),yI(r)):e},[]);return i.length>0?i:e&&e.length&&rR(n)&&rR(o)?e.slice(n,o+1):[]};function yG(e){return"number"===e?[0,"auto"]:void 0}var yX=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=yV(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,l){var s,c,u=null!==(s=l.props.data)&&void 0!==s?s:t;return(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),c=i.dataKey&&!i.allowDuplicatedCategory?rH(void 0===u?a:u,i.dataKey,n):u&&u[r]||a[r])?[].concat(yI(o),[dj(l,c)]):o},[])},yY=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,l=e.tooltipAxis,s=e.tooltipTicks,c=f6(i,a,s,l);if(c>=0&&s){var u=s[c]&&s[c].value,f=yX(e,t,c,u),d=yW(r,a,c,o);return{activeTooltipIndex:c,activeLabel:u,activePayload:f,activeCoordinate:d}}return null},yK=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,l=t.dataStartIndex,s=t.dataEndIndex,c=e.layout,u=e.children,f=e.stackOffset,d=di(c,o);return r.reduce(function(t,r){var p=void 0!==r.type.defaultProps?yL(yL({},r.type.defaultProps),r.props):r.props,h=p.type,y=p.dataKey,m=p.allowDataOverflow,v=p.allowDuplicatedCategory,g=p.scale,b=p.ticks,x=p.includeHidden,w=p[i];if(t[w])return t;var j=yV(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i])===w}),dataStartIndex:l,dataEndIndex:s}),O=j.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&rR(n)&&rR(o))return!0}return!1})(p.domain,m,h)&&(P=db(p.domain,null,m),d&&("number"===h||"auto"!==g)&&(N=f3(j,y,"category")));var S=yG(h);if(!P||0===P.length){var A,P,k,N,E,M=null!==(E=p.domain)&&void 0!==E?E:S;if(y){if(P=f3(j,y,h),"category"===h&&d){var C=rU(P);v&&C?(k=P,P=o3()(0,O)):v||(P=dw(M,P,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(yI(e),[t])},[]))}else if("category"===h)P=v?P.filter(function(e){return""!==e&&!rG()(e)}):dw(M,P,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||rG()(t)?e:[].concat(yI(e),[t])},[]);else if("number"===h){var T=dr(j,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,c);T&&(P=T)}d&&("number"===h||"auto"!==g)&&(N=f3(j,y,"category"))}else P=d?o3()(0,O):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:dm(a[w].stackGroups,l,s):dn(j,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,c,!0);"number"===h?(P=hi(u,P,w,o,b),M&&(P=db(M,P,m))):"category"===h&&M&&P.every(function(e){return M.indexOf(e)>=0})&&(P=M)}return yL(yL({},t),{},y$({},w,yL(yL({},p),{},{axisType:o,domain:P,categoricalDomain:N,duplicateDomain:k,originalDomain:null!==(A=p.domain)&&void 0!==A?A:S,isCategorical:d,layout:c})))},{})},yZ=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,l=t.dataStartIndex,s=t.dataEndIndex,c=e.layout,u=e.children,f=yV(e.data,{graphicalItems:r,dataStartIndex:l,dataEndIndex:s}),d=f.length,p=di(c,o),h=-1;return r.reduce(function(e,t){var y,m=(void 0!==t.type.defaultProps?yL(yL({},t.type.defaultProps),t.props):t.props)[i],v=yG("number");return e[m]?e:(h++,y=p?o3()(0,d):a&&a[m]&&a[m].hasStack?hi(u,y=dm(a[m].stackGroups,l,s),m,o):hi(u,y=db(v,dn(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===m&&!o}),"number",c),n.defaultProps.allowDataOverflow),m,o),yL(yL({},e),{},y$({},m,yL(yL({axisType:o},n.defaultProps),{},{hide:!0,orientation:rC()(yF,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:v,isCategorical:p,layout:c}))))},{})},yJ=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,l=t.dataStartIndex,s=t.dataEndIndex,c=e.children,u="".concat(n,"Id"),f=ni(c,o),d={};return f&&f.length?d=yK(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:l,dataEndIndex:s}):i&&i.length&&(d=yZ(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:l,dataEndIndex:s})),d},yQ=function(e){var t=rF(e),r=da(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:nw()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:dx(t,r)}},y0=function(e){var t=e.children,r=e.defaultShowTooltip,n=na(t,dF),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},y1=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},y2=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,l=void 0===a?{}:a,s=r.width,c=r.height,u=r.children,f=r.margin||{},d=na(u,dF),p=na(u,oY),h=Object.keys(l).reduce(function(e,t){var r=l[t],n=r.orientation;return r.mirror||r.hide?e:yL(yL({},e),{},y$({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:yL(yL({},e),{},y$({},n,rC()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=yL(yL({},y),h),v=m.bottom;d&&(m.bottom+=d.props.height||dF.defaultProps.height),p&&t&&(m=de(m,n,r,t));var g=s-m.left-m.right,b=c-m.top-m.bottom;return yL(yL({brushBottom:v},m),{},{width:Math.max(g,0),height:Math.max(b,0)})},y5=["points","className","baseLinePoints","connectNulls"];function y4(){return(y4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function y3(e){return function(e){if(Array.isArray(e))return y6(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return y6(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y6(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y6(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var y8=function(e){return e&&e.x===+e.x&&e.y===+e.y},y7=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){y8(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),y8(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},y9=function(e,t){var r=y7(e);t&&(r=[r.reduce(function(e,t){return[].concat(y3(e),y3(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},me=function(e,t,r){var n=y9(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(y9(t.reverse(),r).slice(1))},mt=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,o=e.connectNulls,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,y5);if(!t||!t.length)return null;var l=(0,rO.A)("recharts-polygon",r);if(n&&n.length){var s=i.stroke&&"none"!==i.stroke,c=me(t,n,o);return a().createElement("g",{className:l},a().createElement("path",y4({},nu(i,!0),{fill:"Z"===c.slice(-1)?i.fill:"none",stroke:"none",d:c})),s?a().createElement("path",y4({},nu(i,!0),{fill:"none",d:y9(t,o)})):null,s?a().createElement("path",y4({},nu(i,!0),{fill:"none",d:y9(n,o)})):null)}var u=y9(t,o);return a().createElement("path",y4({},nu(i,!0),{fill:"Z"===u.slice(-1)?i.fill:"none",className:l,d:u}))};function mr(e){return(mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mn(){return(mn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mo(Object(r),!0).forEach(function(t){mu(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ma(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mf(n.key),n)}}function ml(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ml=function(){return!!e})()}function ms(e){return(ms=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mc(e,t){return(mc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mu(e,t,r){return(t=mf(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mf(e){var t=function(e,t){if("object"!=mr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mr(t)?t:t+""}var md=Math.PI/180,mp=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=ms(e),function(e,t){if(t&&("object"===mr(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ml()?Reflect.construct(e,t||[],ms(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mc(e,t)}(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,o=t.radius,i=t.orientation,a=t.tickSize,l=dX(r,n,o,e.coordinate),s=dX(r,n,o+("inner"===i?-1:1)*(a||8),e.coordinate);return{x1:l.x,y1:l.y,x2:s.x,y2:s.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*md);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,o=e.axisLine,i=e.axisLineType,l=mi(mi({},nu(this.props,!1)),{},{fill:"none"},nu(o,!1));if("circle"===i)return a().createElement(it,mn({className:"recharts-polar-angle-axis-line"},l,{cx:t,cy:r,r:n}));var s=this.props.ticks.map(function(e){return dX(t,r,n,e.coordinate)});return a().createElement(mt,mn({className:"recharts-polar-angle-axis-line"},l,{points:s}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.tickLine,l=t.tickFormatter,s=t.stroke,c=nu(this.props,!1),u=nu(o,!1),f=mi(mi({},c),{},{fill:"none"},nu(i,!1)),d=r.map(function(t,r){var d=e.getTickLineCoord(t),p=mi(mi(mi({textAnchor:e.getTickTextAnchor(t)},c),{},{stroke:"none",fill:s},u),{},{index:r,payload:t,x:d.x2,y:d.y2});return a().createElement(o9,mn({className:(0,rO.A)("recharts-polar-angle-axis-tick",d0(o)),key:"tick-".concat(t.coordinate)},r6(e.props,t,r)),i&&a().createElement("line",mn({className:"recharts-polar-angle-axis-tick-line"},f,d)),o&&n.renderTickItem(o,p,l?l(t.value,r):t.value))});return a().createElement(o9,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?a().createElement(o9,{className:(0,rO.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):rY()(e)?e(t):a().createElement(lr,mn({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&ma(n.prototype,t),r&&ma(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mu(mp,"displayName","PolarAngleAxis"),mu(mp,"axisType","angleAxis"),mu(mp,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var mh=r(57088),my=r.n(mh),mm=r(10034),mv=r.n(mm),mg=["cx","cy","angle","ticks","axisLine"],mb=["ticks","tick","angle","tickFormatter","stroke"];function mx(e){return(mx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mw(){return(mw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mj(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mj(Object(r),!0).forEach(function(t){mE(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mj(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mS(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function mA(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mM(n.key),n)}}function mP(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mP=function(){return!!e})()}function mk(e){return(mk=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mN(e,t){return(mN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mE(e,t,r){return(t=mM(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mM(e){var t=function(e,t){if("object"!=mx(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mx(t)?t:t+""}var mC=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=mk(e),function(e,t){if(t&&("object"===mx(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,mP()?Reflect.construct(e,t||[],mk(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mN(e,t)}(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle;return dX(r.cx,r.cy,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=my()(o,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:mv()(o,function(e){return e.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=e.axisLine,l=mS(e,mg),s=o.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),c=dX(t,r,s[0],n),u=dX(t,r,s[1],n),f=mO(mO(mO({},nu(l,!1)),{},{fill:"none"},nu(i,!1)),{},{x1:c.x,y1:c.y,x2:u.x,y2:u.y});return a().createElement("line",mw({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.angle,l=t.tickFormatter,s=t.stroke,c=mS(t,mb),u=this.getTickTextAnchor(),f=nu(c,!1),d=nu(o,!1),p=r.map(function(t,r){var c=e.getTickValueCoord(t),p=mO(mO(mO(mO({textAnchor:u,transform:"rotate(".concat(90-i,", ").concat(c.x,", ").concat(c.y,")")},f),{},{stroke:"none",fill:s},d),{},{index:r},c),{},{payload:t});return a().createElement(o9,mw({className:(0,rO.A)("recharts-polar-radius-axis-tick",d0(o)),key:"tick-".concat(t.coordinate)},r6(e.props,t,r)),n.renderTickItem(o,p,l?l(t.value,r):t.value))});return a().createElement(o9,{className:"recharts-polar-radius-axis-ticks"},p)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?a().createElement(o9,{className:(0,rO.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),pt.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){var n;return a().isValidElement(e)?a().cloneElement(e,t):rY()(e)?e(t):a().createElement(lr,mw({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&mA(n.prototype,t),r&&mA(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mE(mC,"displayName","PolarRadiusAxis"),mE(mC,"axisType","radiusAxis"),mE(mC,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var mT=r(5359),m_=r.n(mT);function mI(e){return(mI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var mD=["valueAccessor"],mR=["data","dataKey","clockWise","id","textBreakAll"];function mB(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function mL(){return(mL=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function m$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m$(Object(r),!0).forEach(function(t){var n,o,i;n=e,o=t,i=r[t],(o=function(e){var t=function(e,t){if("object"!=mI(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mI(t)?t:t+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mF(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var mU=function(e){return Array.isArray(e.value)?m_()(e.value):e.value};function mq(e){var t=e.valueAccessor,r=void 0===t?mU:t,n=mF(e,mD),o=n.data,i=n.dataKey,l=n.clockWise,s=n.id,c=n.textBreakAll,u=mF(n,mR);return o&&o.length?a().createElement(o9,{className:"recharts-label-list"},o.map(function(e,t){var n=rG()(i)?r(e,t):f4(e&&e.payload,i),o=rG()(s)?{}:{id:"".concat(s,"-").concat(t)};return a().createElement(pt,mL({},nu(e,!0),u,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:c,viewBox:pt.parseViewBox(rG()(l)?e:mz(mz({},e),{},{clockWise:l})),key:"label-".concat(t),index:t}))})):null}mq.displayName="LabelList",mq.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=ni(e.children,mq).map(function(e,r){return(0,i.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label)?!0===r?a().createElement(mq,{key:"labelList-implicit",data:t}):a().isValidElement(r)||rY()(r)?a().createElement(mq,{key:"labelList-implicit",data:t,content:r}):rZ()(r)?a().createElement(mq,mL({data:t},r,{key:"labelList-implicit"})):null:null].concat(function(e){if(Array.isArray(e))return mB(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return mB(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mB(e,t)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var mH=function(e){return null};function mW(e){return(mW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function mV(){return(mV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function mG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mG(Object(r),!0).forEach(function(t){mQ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mY(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,m0(n.key),n)}}function mK(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(mK=function(){return!!e})()}function mZ(e){return(mZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function mJ(e,t){return(mJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function mQ(e,t,r){return(t=m0(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m0(e){var t=function(e,t){if("object"!=mW(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=mW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mW(t)?t:t+""}mH.displayName="Cell";var m1=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=mZ(r),mQ(t=function(e,t){if(t&&("object"===mW(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,mK()?Reflect.construct(r,o||[],mZ(this).constructor):r.apply(this,o)),"pieRef",null),mQ(t,"sectorRefs",[]),mQ(t,"id",r$("recharts-pie-")),mQ(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),rY()(e)&&e()}),mQ(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),rY()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mJ(e,t)}(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,o=t.labelLine,i=t.dataKey,l=t.valueKey,s=nu(this.props,!1),c=nu(r,!1),u=nu(o,!1),f=r&&r.offsetRadius||20,d=e.map(function(e,t){var d=(e.startAngle+e.endAngle)/2,p=dX(e.cx,e.cy,e.outerRadius+f,d),h=mX(mX(mX(mX({},s),e),{},{stroke:"none"},c),{},{index:t,textAnchor:n.getTextAnchor(p.x,e.cx)},p),y=mX(mX(mX(mX({},s),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[dX(e.cx,e.cy,e.outerRadius,d),p]}),m=i;return rG()(i)&&rG()(l)?m="value":rG()(i)&&(m=l),a().createElement(o9,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,h,f4(e,m)))});return a().createElement(o9,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return e.map(function(r,l){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var s=t.isActiveIndex(l),c=i&&t.hasActiveIndex()?i:null,u=mX(mX({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(o9,mV({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},r6(t.props,r,l),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(l)}),a().createElement(h$,mV({option:s?n:c,isActive:s,shapeType:"sector"},u)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,o=t.animationBegin,i=t.animationDuration,l=t.animationEasing,s=t.animationId,c=this.state,u=c.prevSectors,f=c.prevIsAnimationActive;return a().createElement(ap,{begin:o,duration:i,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=u&&u[t],a=t>0?rC()(e,"paddingAngle",0):0;if(r){var l=rq(r.endAngle-r.startAngle,e.endAngle-e.startAngle),s=mX(mX({},e),{},{startAngle:i+a,endAngle:i+l(n)+a});o.push(s),i=s.endAngle}else{var c=rq(0,e.endAngle-e.startAngle)(n),f=mX(mX({},e),{},{startAngle:i+a,endAngle:i+c+a});o.push(f),i=f.endAngle}}),a().createElement(o9,null,e.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!u1()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,o=t.className,i=t.label,l=t.cx,s=t.cy,c=t.innerRadius,u=t.outerRadius,f=t.isAnimationActive,d=this.state.isAnimationFinished;if(r||!n||!n.length||!rR(l)||!rR(s)||!rR(c)||!rR(u))return null;var p=(0,rO.A)("recharts-pie",o);return a().createElement(o9,{tabIndex:this.props.rootTabIndex,className:p,ref:function(t){e.pieRef=t}},this.renderSectors(),i&&this.renderLabels(n),pt.renderCallByParent(this.props,null,!1),(!f||d)&&mq.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);if(rY()(e))return e(t);var n=(0,rO.A)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a().createElement(yh,mV({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);var n=r;if(rY()(e)&&(n=e(t),a().isValidElement(n)))return n;var o=(0,rO.A)("recharts-pie-label-text","boolean"==typeof e||rY()(e)?"":e.className);return a().createElement(lr,mV({},t,{alignmentBaseline:"middle",className:o}),n)}}],t&&mY(n.prototype,t),r&&mY(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);mQ(m1,"displayName","Pie"),mQ(m1,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!nq.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),mQ(m1,"parseDeltaAngle",function(e,t){return rI(t-e)*Math.min(Math.abs(t-e),360)}),mQ(m1,"getRealPieData",function(e){var t=e.data,r=e.children,n=nu(e,!1),o=ni(r,mH);return t&&t.length?t.map(function(e,t){return mX(mX(mX({payload:e},n),e),o&&o[t]&&o[t].props)}):o&&o.length?o.map(function(e){return mX(mX({},n),e.props)}):[]}),mQ(m1,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,o=t.width,i=t.height,a=dY(o,i);return{cx:n+rz(e.cx,o,o/2),cy:r+rz(e.cy,i,i/2),innerRadius:rz(e.innerRadius,a,0),outerRadius:rz(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(o*o+i*i)/2}}),mQ(m1,"getComposedData",function(e){var t,r,n=e.item,o=e.offset,i=void 0!==n.type.defaultProps?mX(mX({},n.type.defaultProps),n.props):n.props,a=m1.getRealPieData(i);if(!a||!a.length)return null;var l=i.cornerRadius,s=i.startAngle,c=i.endAngle,u=i.paddingAngle,f=i.dataKey,d=i.nameKey,p=i.valueKey,h=i.tooltipType,y=Math.abs(i.minAngle),m=m1.parseCoordinateOfPie(i,o),v=m1.parseDeltaAngle(s,c),g=Math.abs(v),b=f;rG()(f)&&rG()(p)?(rW(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b="value"):rG()(f)&&(rW(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b=p);var x=a.filter(function(e){return 0!==f4(e,b,0)}).length,w=g-x*y-(g>=360?x:x-1)*u,j=a.reduce(function(e,t){var r=f4(t,b,0);return e+(rR(r)?r:0)},0);return j>0&&(t=a.map(function(e,t){var n,o=f4(e,b,0),i=f4(e,d,t),a=(rR(o)?o:0)/j,c=(n=t?r.endAngle+rI(v)*u*+(0!==o):s)+rI(v)*((0!==o?y:0)+a*w),f=(n+c)/2,p=(m.innerRadius+m.outerRadius)/2,g=[{name:i,value:o,payload:e,dataKey:b,type:h}],x=dX(m.cx,m.cy,p,f);return r=mX(mX(mX({percent:a,cornerRadius:l,name:i,tooltipPayload:g,midAngle:f,middleRadius:p,tooltipPosition:x},e),m),{},{value:f4(e,b),startAngle:n,endAngle:c,payload:e,paddingAngle:rI(v)*u})})),mX(mX({},m),{},{sectors:t,data:a})});var m2=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,l=e.validateTooltipEventTypes,s=void 0===l?["axis"]:l,c=e.axisComponents,u=e.legendContent,f=e.formatAxisMap,d=e.defaultProps,p=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,l=t.dataEndIndex,s=e.barSize,u=e.layout,f=e.barGap,d=e.barCategoryGap,p=e.maxBarSize,h=y1(u),y=h.numericAxisName,m=h.cateAxisName,v=!!r&&!!r.length&&r.some(function(e){var t=nt(e&&e.type);return t&&t.indexOf("Bar")>=0}),g=[];return r.forEach(function(r,h){var b=yV(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:l}),x=void 0!==r.type.defaultProps?yL(yL({},r.type.defaultProps),r.props):r.props,w=x.dataKey,j=x.maxBarSize,O=x["".concat(y,"Id")],S=x["".concat(m,"Id")],A=c.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||o6(!1);var i=n[o];return yL(yL({},e),{},y$(y$({},r.axisType,i),"".concat(r.axisType,"Ticks"),da(i)))},{}),P=A[m],k=A["".concat(m,"Ticks")],N=n&&n[O]&&n[O].hasStack&&dy(r,n[O].stackGroups),E=nt(r.type).indexOf("Bar")>=0,M=dx(P,k),C=[],T=v&&f7({barSize:s,stackGroups:n,totalSize:"xAxis"===m?A[m].width:"yAxis"===m?A[m].height:void 0});if(E){var _,I,D=rG()(j)?p:j,R=null!==(_=null!==(I=dx(P,k,!0))&&void 0!==I?I:D)&&void 0!==_?_:0;C=f9({barGap:f,barCategoryGap:d,bandSize:R!==M?R:M,sizeList:T[S],maxBarSize:D}),R!==M&&(C=C.map(function(e){return yL(yL({},e),{},{position:yL(yL({},e.position),{},{offset:e.position.offset-R/2})})}))}var B=r&&r.type&&r.type.getComposedData;B&&g.push({props:yL(yL({},B(yL(yL({},A),{},{displayedData:b,props:e,dataKey:w,item:r,bandSize:M,barPosition:C,offset:o,stackedData:N,layout:u,dataStartIndex:a,dataEndIndex:l}))),{},y$(y$(y$({key:r.key||"item-".concat(h)},y,A[y]),m,A[m]),"animationId",i)),childIndex:no(e.children).indexOf(r),item:r})}),g},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,l=e.updateId;if(!nl({props:o}))return null;var s=o.children,u=o.layout,d=o.stackOffset,h=o.data,y=o.reverseStackOrder,m=y1(u),v=m.numericAxisName,g=m.cateAxisName,b=ni(s,r),x=dp(h,b,"".concat(v,"Id"),"".concat(g,"Id"),d,y),w=c.reduce(function(e,t){var r="".concat(t.axisType,"Map");return yL(yL({},e),{},y$({},r,yJ(o,yL(yL({},t),{},{graphicalItems:b,stackGroups:t.axisType===v&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),j=y2(yL(yL({},w),{},{props:o,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],j,e.replace("Map",""),t)});var O=yQ(w["".concat(g,"Map")]),S=p(o,yL(yL({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:l,graphicalItems:b,stackGroups:x,offset:j}));return yL(yL({formattedGraphicalItems:S,graphicalItems:b,offset:j,stackGroups:x},O),w)},y=function(e){var r;function n(e){var r,o,l,s,c;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),s=n,c=[e],s=yT(s),y$(l=function(e,t){if(t&&("object"===yk(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,yC()?Reflect.construct(s,c||[],yT(this).constructor):s.apply(this,c)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),y$(l,"accessibilityManager",new hd),y$(l,"handleLegendBBoxUpdate",function(e){if(e){var t=l.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;l.setState(yL({legendBBox:e},h({props:l.props,dataStartIndex:r,dataEndIndex:n,updateId:o},yL(yL({},l.state),{},{legendBBox:e}))))}}),y$(l,"handleReceiveSyncEvent",function(e,t,r){l.props.syncId===e&&(r!==l.eventEmitterSymbol||"function"==typeof l.props.syncMethod)&&l.applySyncEvent(t)}),y$(l,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==l.state.dataStartIndex||r!==l.state.dataEndIndex){var n=l.state.updateId;l.setState(function(){return yL({dataStartIndex:t,dataEndIndex:r},h({props:l.props,dataStartIndex:t,dataEndIndex:r,updateId:n},l.state))}),l.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),y$(l,"handleMouseEnter",function(e){var t=l.getMouseInfo(e);if(t){var r=yL(yL({},t),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseEnter;rY()(n)&&n(r,e)}}),y$(l,"triggeredAfterMouseMove",function(e){var t=l.getMouseInfo(e),r=t?yL(yL({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseMove;rY()(n)&&n(r,e)}),y$(l,"handleItemMouseEnter",function(e){l.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),y$(l,"handleItemMouseLeave",function(){l.setState(function(){return{isTooltipActive:!1}})}),y$(l,"handleMouseMove",function(e){e.persist(),l.throttleTriggeredAfterMouseMove(e)}),y$(l,"handleMouseLeave",function(e){l.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};l.setState(t),l.triggerSyncEvent(t);var r=l.props.onMouseLeave;rY()(r)&&r(t,e)}),y$(l,"handleOuterEvent",function(e){var t,r,n=nh(e),o=rC()(l.props,"".concat(n));n&&rY()(o)&&o(null!==(t=/.*touch.*/i.test(n)?l.getMouseInfo(e.changedTouches[0]):l.getMouseInfo(e))&&void 0!==t?t:{},e)}),y$(l,"handleClick",function(e){var t=l.getMouseInfo(e);if(t){var r=yL(yL({},t),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onClick;rY()(n)&&n(r,e)}}),y$(l,"handleMouseDown",function(e){var t=l.props.onMouseDown;rY()(t)&&t(l.getMouseInfo(e),e)}),y$(l,"handleMouseUp",function(e){var t=l.props.onMouseUp;rY()(t)&&t(l.getMouseInfo(e),e)}),y$(l,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&l.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),y$(l,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&l.handleMouseDown(e.changedTouches[0])}),y$(l,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&l.handleMouseUp(e.changedTouches[0])}),y$(l,"handleDoubleClick",function(e){var t=l.props.onDoubleClick;rY()(t)&&t(l.getMouseInfo(e),e)}),y$(l,"handleContextMenu",function(e){var t=l.props.onContextMenu;rY()(t)&&t(l.getMouseInfo(e),e)}),y$(l,"triggerSyncEvent",function(e){void 0!==l.props.syncId&&hl.emit(hs,l.props.syncId,e,l.eventEmitterSymbol)}),y$(l,"applySyncEvent",function(e){var t=l.props,r=t.layout,n=t.syncMethod,o=l.state.updateId,i=e.dataStartIndex,a=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)l.setState(yL({dataStartIndex:i,dataEndIndex:a},h({props:l.props,dataStartIndex:i,dataEndIndex:a,updateId:o},l.state)));else if(void 0!==e.activeTooltipIndex){var s=e.chartX,c=e.chartY,u=e.activeTooltipIndex,f=l.state,d=f.offset,p=f.tooltipTicks;if(!d)return;if("function"==typeof n)u=n(p,e);else if("value"===n){u=-1;for(var y=0;y<p.length;y++)if(p[y].value===e.activeLabel){u=y;break}}var m=yL(yL({},d),{},{x:d.left,y:d.top}),v=Math.min(s,m.x+m.width),g=Math.min(c,m.y+m.height),b=p[u]&&p[u].value,x=yX(l.state,l.props.data,u),w=p[u]?{x:"horizontal"===r?p[u].coordinate:v,y:"horizontal"===r?g:p[u].coordinate}:yq;l.setState(yL(yL({},e),{},{activeLabel:b,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else l.setState(e)}),y$(l,"renderCursor",function(e){var r,n=l.state,o=n.isTooltipActive,i=n.activeCoordinate,s=n.activePayload,c=n.offset,u=n.activeTooltipIndex,f=n.tooltipAxisBandSize,d=l.getTooltipEventType(),p=null!==(r=e.props.active)&&void 0!==r?r:o,h=l.props.layout,y=e.key||"_recharts-cursor";return a().createElement(yS,{key:y,activeCoordinate:i,activePayload:s,activeTooltipIndex:u,chartName:t,element:e,isActive:p,layout:h,offset:c,tooltipAxisBandSize:f,tooltipEventType:d})}),y$(l,"renderPolarAxis",function(e,t,r){var n=rC()(e,"type.axisType"),o=rC()(l.state,"".concat(n,"Map")),a=e.type.defaultProps,s=void 0!==a?yL(yL({},a),e.props):e.props,c=o&&o[s["".concat(n,"Id")]];return(0,i.cloneElement)(e,yL(yL({},c),{},{className:(0,rO.A)(n,c.className),key:e.key||"".concat(t,"-").concat(r),ticks:da(c,!0)}))}),y$(l,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,o=t.polarRadius,a=l.state,s=a.radiusAxisMap,c=a.angleAxisMap,u=rF(s),f=rF(c),d=f.cx,p=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(e,{polarAngles:Array.isArray(n)?n:da(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(o)?o:da(u,!0).map(function(e){return e.coordinate}),cx:d,cy:p,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),y$(l,"renderLegend",function(){var e=l.state.formattedGraphicalItems,t=l.props,r=t.children,n=t.width,o=t.height,a=l.props.margin||{},s=fZ({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!s)return null;var c=s.item,f=yM(s,yA);return(0,i.cloneElement)(c,yL(yL({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:l.handleLegendBBoxUpdate}))}),y$(l,"renderTooltip",function(){var e,t=l.props,r=t.children,n=t.accessibilityLayer,o=na(r,n2);if(!o)return null;var a=l.state,s=a.isTooltipActive,c=a.activeCoordinate,u=a.activePayload,f=a.activeLabel,d=a.offset,p=null!==(e=o.props.active)&&void 0!==e?e:s;return(0,i.cloneElement)(o,{viewBox:yL(yL({},d),{},{x:d.left,y:d.top}),active:p,label:f,payload:p?u:[],coordinate:c,accessibilityLayer:n})}),y$(l,"renderBrush",function(e){var t=l.props,r=t.margin,n=t.data,o=l.state,a=o.offset,s=o.dataStartIndex,c=o.dataEndIndex,u=o.updateId;return(0,i.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:ds(l.handleBrushChange,e.props.onChange),data:n,x:rR(e.props.x)?e.props.x:a.left,y:rR(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:rR(e.props.width)?e.props.width:a.width,startIndex:s,endIndex:c,updateId:"brush-".concat(u)})}),y$(l,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=l.clipPathId,o=l.state,a=o.xAxisMap,s=o.yAxisMap,c=o.offset,u=e.type.defaultProps||{},f=e.props,d=f.xAxisId,p=void 0===d?u.xAxisId:d,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return(0,i.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[p],yAxis:s[y],viewBox:{x:c.left,y:c.top,width:c.width,height:c.height},clipPathId:n})}),y$(l,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,l=[],s=t.props.key,c=void 0!==t.item.type.defaultProps?yL(yL({},t.item.type.defaultProps),t.item.props):t.item.props,u=c.activeDot,f=yL(yL({index:i,dataKey:c.dataKey,cx:r.x,cy:r.y,r:4,fill:f8(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},nu(u,!1)),r3(u));return l.push(n.renderActiveDot(u,f,"".concat(s,"-activePoint-").concat(i))),o?l.push(n.renderActiveDot(u,yL(yL({},f),{},{cx:o.x,cy:o.y}),"".concat(s,"-basePoint-").concat(i))):a&&l.push(null),l}),y$(l,"renderGraphicChild",function(e,t,r){var n=l.filterFormatItem(e,t,r);if(!n)return null;var o=l.getTooltipEventType(),a=l.state,s=a.isTooltipActive,c=a.tooltipAxis,u=a.activeTooltipIndex,f=a.activeLabel,d=na(l.props.children,n2),p=n.props,h=p.points,y=p.isRange,m=p.baseLine,v=void 0!==n.item.type.defaultProps?yL(yL({},n.item.type.defaultProps),n.item.props):n.item.props,g=v.activeDot,b=v.hide,x=v.activeBar,w=v.activeShape,j=!!(!b&&s&&d&&(g||x||w)),O={};"axis"!==o&&d&&"click"===d.props.trigger?O={onClick:ds(l.handleItemMouseEnter,e.props.onClick)}:"axis"!==o&&(O={onMouseLeave:ds(l.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:ds(l.handleItemMouseEnter,e.props.onMouseEnter)});var S=(0,i.cloneElement)(e,yL(yL({},n.props),O));if(j){if(u>=0){if(c.dataKey&&!c.allowDuplicatedCategory){var A="function"==typeof c.dataKey?function(e){return"function"==typeof c.dataKey?c.dataKey(e.payload):null}:"payload.".concat(c.dataKey.toString());k=rH(h,A,f),N=y&&m&&rH(m,A,f)}else k=null==h?void 0:h[u],N=y&&m&&m[u];if(w||x){var P=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[(0,i.cloneElement)(e,yL(yL(yL({},n.props),O),{},{activeIndex:P})),null,null]}if(!rG()(k))return[S].concat(yI(l.renderActivePoints({item:n,activePoint:k,basePoint:N,childIndex:u,isRange:y})))}else{var k,N,E,M=(null!==(E=l.getItemByXY(l.state.activeCoordinate))&&void 0!==E?E:{graphicalItem:S}).graphicalItem,C=M.item,T=void 0===C?e:C,_=M.childIndex,I=yL(yL(yL({},n.props),O),{},{activeIndex:_});return[(0,i.cloneElement)(T,I),null,null]}}return y?[S,null,null]:[S,null]}),y$(l,"renderCustomized",function(e,t,r){return(0,i.cloneElement)(e,yL(yL({key:"recharts-customized-".concat(r)},l.props),l.state))}),y$(l,"renderMap",{CartesianGrid:{handler:yH,once:!0},ReferenceArea:{handler:l.renderReferenceElement},ReferenceLine:{handler:yH},ReferenceDot:{handler:l.renderReferenceElement},XAxis:{handler:yH},YAxis:{handler:yH},Brush:{handler:l.renderBrush,once:!0},Bar:{handler:l.renderGraphicChild},Line:{handler:l.renderGraphicChild},Area:{handler:l.renderGraphicChild},Radar:{handler:l.renderGraphicChild},RadialBar:{handler:l.renderGraphicChild},Scatter:{handler:l.renderGraphicChild},Pie:{handler:l.renderGraphicChild},Funnel:{handler:l.renderGraphicChild},Tooltip:{handler:l.renderCursor,once:!0},PolarGrid:{handler:l.renderPolarGrid,once:!0},PolarAngleAxis:{handler:l.renderPolarAxis},PolarRadiusAxis:{handler:l.renderPolarAxis},Customized:{handler:l.renderCustomized}}),l.clipPathId="".concat(null!==(r=e.id)&&void 0!==r?r:r$("recharts"),"-clip"),l.throttleTriggeredAfterMouseMove=rA()(l.triggeredAfterMouseMove,null!==(o=e.throttleDelay)&&void 0!==o?o:1e3/60),l.state={},l}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&y_(e,t)}(n,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=na(t,n2);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var l=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,s=yX(this.state,r,a,l),c=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===o?{x:c,y:u}:{y:c,x:u},d=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});d&&(f=yL(yL({},f),d.props.points[a].tooltipPosition),s=d.props.points[a].tooltipPayload);var p={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:l,activePayload:s,activeCoordinate:f};this.setState(p),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){nf([na(e.children,n2)],[na(this.props.children,n2)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=na(this.props.children,n2);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return s.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var l=this.state,s=l.xAxisMap,c=l.yAxisMap;if("axis"!==this.getTooltipEventType()&&s&&c){var u=rF(s).scale,f=rF(c).scale,d=u&&u.invert?u.invert(o.chartX):null,p=f&&f.invert?f.invert(o.chartY):null;return yL(yL({},o),{},{xValue:d,yValue:p})}var h=yY(this.state,this.props.data,this.props.layout,a);return h?yL(yL({},o),h):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var l=this.state,s=l.angleAxisMap,c=l.radiusAxisMap;return s&&c?dQ({x:o,y:i},rF(s)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=na(e,n2),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),yL(yL({},r3(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){hl.on(hs,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){hl.removeListener(hs,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===nt(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return a().createElement("defs",null,a().createElement("clipPath",{id:e},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=yE(t,2),n=r[0],o=r[1];return yL(yL({},e),{},y$({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=yE(t,2),n=r[0],o=r[1];return yL(yL({},e),{},y$({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],l=a.props,s=a.item,c=void 0!==s.type.defaultProps?yL(yL({},s.type.defaultProps),s.props):s.props,u=nt(s.type);if("Bar"===u){var f=(l.data||[]).find(function(t){return ax(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var d=(l.data||[]).find(function(t){return dQ(e,t)});if(d)return{graphicalItem:a,payload:d}}else if(hz(a,n)||hF(a,n)||hU(a,n)){var p=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,l=(hz(i,o)?t="trapezoids":hF(i,o)?t="sectors":hU(i,o)&&(t="points"),t),s=hz(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:hF(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:hU(i,o)?o.payload:{},c=a.filter(function(e,t){var r=u1()(s,e),n=i.props[l].filter(function(e){var t;return(hz(i,o)?t=hq:hF(i,o)?t=hH:hU(i,o)&&(t=hW),t)(e,o)}),a=i.props[l].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(c[c.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:c.data}),h=void 0===c.activeIndex?p:c.activeIndex;return{graphicalItem:yL(yL({},a),{},{childIndex:h}),payload:hU(a,n)?c.data[p]:a.props.data[p]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!nl(this))return null;var n=this.props,o=n.children,i=n.className,l=n.width,s=n.height,c=n.style,u=n.compact,f=n.title,d=n.desc,p=nu(yM(n,yP),!1);if(u)return a().createElement(p$,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(n3,yN({},p,{width:l,height:s,title:f,desc:d}),this.renderClipPath(),np(o,this.renderMap)));this.props.accessibilityLayer&&(p.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,p.role=null!==(t=this.props.role)&&void 0!==t?t:"application",p.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},p.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return a().createElement(p$,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",yN({className:(0,rO.A)("recharts-wrapper",i),style:yL({position:"relative",cursor:"default",width:l,height:s},c)},h,{ref:function(e){r.container=e}}),a().createElement(n3,yN({},p,{width:l,height:s,title:f,desc:d,style:yU}),this.renderClipPath(),np(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yz(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);y$(y,"displayName",t),y$(y,"defaultProps",yL({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),y$(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,l=e.layout,s=e.stackOffset,c=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var d=y0(e);return yL(yL(yL({},d),{},{updateId:0},h(yL(yL({props:e},d),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:l,prevStackOffset:s,prevMargin:c,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||l!==t.prevLayout||s!==t.prevStackOffset||!rQ(c,t.prevMargin)){var p=y0(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},m=yL(yL({},yY(t,n,l)),{},{updateId:t.updateId+1}),v=yL(yL(yL({},p),y),m);return yL(yL(yL({},v),h(yL({props:e},v),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:l,prevStackOffset:s,prevMargin:c,prevChildren:o})}if(!nf(o,t.prevChildren)){var g,b,x,w,j=na(o,dF),O=j&&null!==(g=null===(b=j.props)||void 0===b?void 0:b.startIndex)&&void 0!==g?g:u,S=j&&null!==(x=null===(w=j.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,A=rG()(n)||O!==u||S!==f?t.updateId+1:t.updateId;return yL(yL({updateId:A},h(yL(yL({props:e},t),{},{updateId:A,dataStartIndex:O,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:O,dataEndIndex:S})}return null}),y$(y,"renderActiveDot",function(e,t,r){var n;return n=(0,i.isValidElement)(e)?(0,i.cloneElement)(e,t):rY()(e)?e(t):a().createElement(it,t),a().createElement(o9,{className:"recharts-active-dot",key:r},n)});var m=(0,i.forwardRef)(function(e,t){return a().createElement(y,yN({},e,{ref:t}))});return m.displayName=y.displayName,m}({chartName:"PieChart",GraphicalChild:m1,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:mp},{axisType:"radiusAxis",AxisComp:mC}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,l=e.startAngle,s=e.endAngle,c=rz(e.cx,i,i/2),u=rz(e.cy,a,a/2),f=dY(i,a,r),d=rz(e.innerRadius,f,0),p=rz(e.outerRadius,f,.8*f);return Object.keys(t).reduce(function(e,r){var i,a=t[r],f=a.domain,h=a.reversed;if(rG()(a.range))"angleAxis"===n?i=[l,s]:"radiusAxis"===n&&(i=[d,p]),h&&(i=[i[1],i[0]]);else{var y,m=function(e){if(Array.isArray(e))return e}(y=i=a.range)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,l=[],s=!0,c=!1;try{i=(r=r.call(e)).next;for(;!(s=(n=i.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(y,2)||function(e,t){if(e){if("string"==typeof e)return dV(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dV(e,t)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();l=m[0],s=m[1]}var v=dc(a,o),g=v.realScaleType,b=v.scale;b.domain(f).range(i),du(b);var x=dh(b,dH(dH({},a),{},{realScaleType:g})),w=dH(dH(dH({},a),x),{},{range:i,radius:p,realScaleType:g,scale:b,cx:c,cy:u,innerRadius:d,outerRadius:p,startAngle:l,endAngle:s});return dH(dH({},e),{},dW({},r,w))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});let m5=["hsl(var(--chart-1))","hsl(var(--chart-2))","hsl(var(--chart-3))","hsl(var(--chart-4))","hsl(var(--chart-5))","hsl(200 70% 50%)","hsl(300 70% 50%)","hsl(50 70% 50%)"];function m4({totalIncome:e,categories:t,balancesVisible:r}){let n=e-t.reduce((e,t)=>e+t.budget,0),i=t.filter(e=>e.budget>0).map((e,t)=>({name:e.name,value:e.budget,fill:m5[t%m5.length],isVisible:e.isVisible??!0}));n>0&&i.push({name:"Unallocated",value:n,fill:"hsl(var(--muted))",isVisible:!0});let a=i.reduce((e,t)=>(e[t.name]={label:t.name,color:t.fill},e),{});if(0===e&&0===t.length)return(0,o.jsxs)(A.Zp,{children:[(0,o.jsx)(A.aR,{className:"pb-2",children:(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(rj,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,o.jsx)(A.Wu,{children:(0,o.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Enter your income and add categories to see a visual breakdown."})})]});let l=0===i.length||i.every(e=>0===e.value);return(0,o.jsxs)(A.Zp,{children:[(0,o.jsx)(A.aR,{className:"pb-2",children:(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(rj,{className:"h-5 w-5 text-primary"}),"Budget Overview"]})}),(0,o.jsxs)(A.Wu,{children:[l?(0,o.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Allocate funds to categories to see the chart."}):(0,o.jsx)(oQ,{config:a,className:"mx-auto aspect-square max-h-[250px] h-auto",children:(0,o.jsxs)(m2,{children:[(0,o.jsx)(n2,{formatter:(e,t,n)=>{let o=n.payload?.isVisible??!0;return[r&&o?`R ${Number(e).toFixed(2)}`:"R ••••",t]},content:(0,o.jsx)(o1,{nameKey:"name",hideLabel:!1})}),(0,o.jsx)(m1,{data:i,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",outerRadius:80,labelLine:!1,label:({percent:e,payload:t})=>{let n=t?.isVisible??!0;return r&&n?`${(100*e).toFixed(0)}%`:""},children:i.map((e,t)=>(0,o.jsx)(mH,{fill:e.fill},`cell-${t}`))}),(0,o.jsx)(oY,{content:(0,o.jsx)(o2,{nameKey:"name",className:"text-xs flex-wrap justify-center gap-x-2 gap-y-1"})})]})}),t.map(e=>{if(0===e.budget)return null;let t=r&&(e.isVisible??!0),n=e.subCategories.reduce((e,t)=>e+t.allocatedAmount,0),i=e.budget>0?n/e.budget*100:0,a=n>e.budget;return(0,o.jsxs)("div",{className:"mt-3 text-xs",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-0.5",children:[(0,o.jsx)("span",{className:"font-medium",children:e.name}),(0,o.jsx)("span",{className:"text-muted-foreground",children:t?`${n.toFixed(2)} / ${e.budget.toFixed(2)}`:"•••• / ••••"})]}),(0,o.jsx)(tX,{value:Math.min(i,100),className:`h-1.5 ${a?"bg-destructive/70 [&>*]:bg-destructive":"[&>*]:bg-primary"}`})]},e.id)})]})]})}var m3=r(51215);function m6(e,[t,r]){return Math.min(r,Math.max(t,e))}var m8=r(38674),m7=r(13495),m9=r(69024),ve=[" ","Enter","ArrowUp","ArrowDown"],vt=[" ","Enter"],vr="Select",[vn,vo,vi]=(0,q.N)(vr),[va,vl]=(0,U.A)(vr,[vi,m8.Bk]),vs=(0,m8.Bk)(),[vc,vu]=va(vr),[vf,vd]=va(vr),vp=e=>{let{__scopeSelect:t,children:r,open:n,defaultOpen:a,onOpenChange:l,value:s,defaultValue:c,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:h,required:y,form:m}=e,v=vs(t),[g,b]=i.useState(null),[x,w]=i.useState(null),[j,O]=i.useState(!1),S=(0,ec.jH)(f),[A=!1,P]=(0,V.i)({prop:n,defaultProp:a,onChange:l}),[k,N]=(0,V.i)({prop:s,defaultProp:c,onChange:u}),E=i.useRef(null),M=!g||m||!!g.closest("form"),[C,T]=i.useState(new Set),_=Array.from(C).map(e=>e.props.value).join(";");return(0,o.jsx)(m8.bL,{...v,children:(0,o.jsxs)(vc,{required:y,scope:t,trigger:g,onTriggerChange:b,valueNode:x,onValueNodeChange:w,valueNodeHasChildren:j,onValueNodeHasChildrenChange:O,contentId:(0,K.B)(),value:k,onValueChange:N,open:A,onOpenChange:P,dir:S,triggerPointerDownPosRef:E,disabled:h,children:[(0,o.jsx)(vn.Provider,{scope:t,children:(0,o.jsx)(vf,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{T(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{T(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),M?(0,o.jsxs)(vJ,{"aria-hidden":!0,required:y,tabIndex:-1,name:d,autoComplete:p,value:k,onChange:e=>N(e.target.value),disabled:h,form:m,children:[void 0===k?(0,o.jsx)("option",{value:""}):null,Array.from(C)]},_):null]})})};vp.displayName=vr;var vh="SelectTrigger",vy=i.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...a}=e,l=vs(r),s=vu(vh,r),c=s.disabled||n,u=(0,H.s)(t,s.onTriggerChange),f=vo(r),d=i.useRef("touch"),[p,h,y]=vQ(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===s.value),n=v0(t,e,r);void 0!==n&&s.onValueChange(n.value)}),m=e=>{c||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,o.jsx)(m8.Mz,{asChild:!0,...l,children:(0,o.jsx)(G.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":vZ(s.value)?"":void 0,...a,ref:u,onClick:(0,W.m)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==d.current&&m(e)}),onPointerDown:(0,W.m)(a.onPointerDown,e=>{d.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())}),onKeyDown:(0,W.m)(a.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&ve.includes(e.key)&&(m(),e.preventDefault())})})})});vy.displayName=vh;var vm="SelectValue",vv=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:i,children:a,placeholder:l="",...s}=e,c=vu(vm,r),{onValueNodeHasChildrenChange:u}=c,f=void 0!==a,d=(0,H.s)(t,c.onValueNodeChange);return(0,X.N)(()=>{u(f)},[u,f]),(0,o.jsx)(G.sG.span,{...s,ref:d,style:{pointerEvents:"none"},children:vZ(c.value)?(0,o.jsx)(o.Fragment,{children:l}):a})});vv.displayName=vm;var vg=i.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...i}=e;return(0,o.jsx)(G.sG.span,{"aria-hidden":!0,...i,ref:t,children:n||"▼"})});vg.displayName="SelectIcon";var vb=e=>(0,o.jsx)(eW.Z,{asChild:!0,...e});vb.displayName="SelectPortal";var vx="SelectContent",vw=i.forwardRef((e,t)=>{let r=vu(vx,e.__scopeSelect),[n,a]=i.useState();return((0,X.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,o.jsx)(vS,{...e,ref:t}):n?m3.createPortal((0,o.jsx)(vj,{scope:e.__scopeSelect,children:(0,o.jsx)(vn.Slot,{scope:e.__scopeSelect,children:(0,o.jsx)("div",{children:e.children})})}),n):null});vw.displayName=vx;var[vj,vO]=va(vx),vS=i.forwardRef((e,t)=>{let{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:s,side:c,sideOffset:u,align:f,alignOffset:d,arrowPadding:p,collisionBoundary:h,collisionPadding:y,sticky:m,hideWhenDetached:v,avoidCollisions:g,...b}=e,x=vu(vx,r),[w,j]=i.useState(null),[O,S]=i.useState(null),A=(0,H.s)(t,e=>j(e)),[P,k]=i.useState(null),[N,E]=i.useState(null),M=vo(r),[C,T]=i.useState(!1),_=i.useRef(!1);i.useEffect(()=>{if(w)return(0,eX.Eq)(w)},[w]),(0,eV.Oh)();let I=i.useCallback(e=>{let[t,...r]=M().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&O&&(O.scrollTop=0),r===n&&O&&(O.scrollTop=O.scrollHeight),r?.focus(),document.activeElement!==o))return},[M,O]),D=i.useCallback(()=>I([P,w]),[I,P,w]);i.useEffect(()=>{C&&D()},[C,D]);let{onOpenChange:R,triggerPointerDownPosRef:B}=x;i.useEffect(()=>{if(w){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():w.contains(r.target)||R(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[w,R,B]),i.useEffect(()=>{let e=()=>R(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[R]);let[L,$]=vQ(e=>{let t=M().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=v0(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),z=i.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==x.value&&x.value===t||n)&&(k(e),n&&(_.current=!0))},[x.value]),F=i.useCallback(()=>w?.focus(),[w]),U=i.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==x.value&&x.value===t||n)&&E(e)},[x.value]),q="popper"===n?vP:vA,V=q===vP?{side:c,sideOffset:u,align:f,alignOffset:d,arrowPadding:p,collisionBoundary:h,collisionPadding:y,sticky:m,hideWhenDetached:v,avoidCollisions:g}:{};return(0,o.jsx)(vj,{scope:r,content:w,viewport:O,onViewportChange:S,itemRefCallback:z,selectedItem:P,onItemLeave:F,itemTextRefCallback:U,focusSelectedItem:D,selectedItemText:N,position:n,isPositioned:C,searchRef:L,children:(0,o.jsx)(eG.A,{as:eY.DX,allowPinchZoom:!0,children:(0,o.jsx)(eH.n,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,W.m)(a,e=>{x.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,o.jsx)(eq.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:(0,o.jsx)(q,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault(),...b,...V,onPlaced:()=>T(!0),ref:A,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:(0,W.m)(b.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>I(t)),e.preventDefault()}})})})})})})});vS.displayName="SelectContentImpl";var vA=i.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:n,...a}=e,l=vu(vx,r),s=vO(vx,r),[c,u]=i.useState(null),[f,d]=i.useState(null),p=(0,H.s)(t,e=>d(e)),h=vo(r),y=i.useRef(!1),m=i.useRef(!0),{viewport:v,selectedItem:g,selectedItemText:b,focusSelectedItem:x}=s,w=i.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&v&&g&&b){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),o=b.getBoundingClientRect();if("rtl"!==l.dir){let n=o.left-t.left,i=r.left-n,a=e.left-i,l=e.width+a,s=Math.max(l,t.width),u=m6(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.left=u+"px"}else{let n=t.right-o.right,i=window.innerWidth-r.right-n,a=window.innerWidth-e.right-i,l=e.width+a,s=Math.max(l,t.width),u=m6(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=l+"px",c.style.right=u+"px"}let i=h(),a=window.innerHeight-20,s=v.scrollHeight,u=window.getComputedStyle(f),d=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),m=parseInt(u.borderBottomWidth,10),x=d+p+s+parseInt(u.paddingBottom,10)+m,w=Math.min(5*g.offsetHeight,x),j=window.getComputedStyle(v),O=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),A=e.top+e.height/2-10,P=g.offsetHeight/2,k=d+p+(g.offsetTop+P);if(k<=A){let e=i.length>0&&g===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(a-A,P+(e?S:0)+(f.clientHeight-v.offsetTop-v.offsetHeight)+m);c.style.height=k+t+"px"}else{let e=i.length>0&&g===i[0].ref.current;c.style.top="0px";let t=Math.max(A,d+v.offsetTop+(e?O:0)+P);c.style.height=t+(x-k)+"px",v.scrollTop=k-A+v.offsetTop}c.style.margin="10px 0",c.style.minHeight=w+"px",c.style.maxHeight=a+"px",n?.(),requestAnimationFrame(()=>y.current=!0)}},[h,l.trigger,l.valueNode,c,f,v,g,b,l.dir,n]);(0,X.N)(()=>w(),[w]);let[j,O]=i.useState();(0,X.N)(()=>{f&&O(window.getComputedStyle(f).zIndex)},[f]);let S=i.useCallback(e=>{e&&!0===m.current&&(w(),x?.(),m.current=!1)},[w,x]);return(0,o.jsx)(vk,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:S,children:(0,o.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,o.jsx)(G.sG.div,{...a,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});vA.displayName="SelectItemAlignedPosition";var vP=i.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:i=10,...a}=e,l=vs(r);return(0,o.jsx)(m8.UC,{...l,...a,ref:t,align:n,collisionPadding:i,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});vP.displayName="SelectPopperPosition";var[vk,vN]=va(vx,{}),vE="SelectViewport",vM=i.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:n,...a}=e,l=vO(vE,r),s=vN(vE,r),c=(0,H.s)(t,l.onViewportChange),u=i.useRef(0);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),(0,o.jsx)(vn.Slot,{scope:r,children:(0,o.jsx)(G.sG.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,W.m)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=s;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,a=Math.min(n,i),l=i-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});vM.displayName=vE;var vC="SelectGroup",[vT,v_]=va(vC);i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=(0,K.B)();return(0,o.jsx)(vT,{scope:r,id:i,children:(0,o.jsx)(G.sG.div,{role:"group","aria-labelledby":i,...n,ref:t})})}).displayName=vC;var vI="SelectLabel",vD=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=v_(vI,r);return(0,o.jsx)(G.sG.div,{id:i.id,...n,ref:t})});vD.displayName=vI;var vR="SelectItem",[vB,vL]=va(vR),v$=i.forwardRef((e,t)=>{let{__scopeSelect:r,value:n,disabled:a=!1,textValue:l,...s}=e,c=vu(vR,r),u=vO(vR,r),f=c.value===n,[d,p]=i.useState(l??""),[h,y]=i.useState(!1),m=(0,H.s)(t,e=>u.itemRefCallback?.(e,n,a)),v=(0,K.B)(),g=i.useRef("touch"),b=()=>{a||(c.onValueChange(n),c.onOpenChange(!1))};if(""===n)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,o.jsx)(vB,{scope:r,value:n,disabled:a,textId:v,isSelected:f,onItemTextChange:i.useCallback(e=>{p(t=>t||(e?.textContent??"").trim())},[]),children:(0,o.jsx)(vn.ItemSlot,{scope:r,value:n,disabled:a,textValue:d,children:(0,o.jsx)(G.sG.div,{role:"option","aria-labelledby":v,"data-highlighted":h?"":void 0,"aria-selected":f&&h,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...s,ref:m,onFocus:(0,W.m)(s.onFocus,()=>y(!0)),onBlur:(0,W.m)(s.onBlur,()=>y(!1)),onClick:(0,W.m)(s.onClick,()=>{"mouse"!==g.current&&b()}),onPointerUp:(0,W.m)(s.onPointerUp,()=>{"mouse"===g.current&&b()}),onPointerDown:(0,W.m)(s.onPointerDown,e=>{g.current=e.pointerType}),onPointerMove:(0,W.m)(s.onPointerMove,e=>{g.current=e.pointerType,a?u.onItemLeave?.():"mouse"===g.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,W.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,W.m)(s.onKeyDown,e=>{(u.searchRef?.current===""||" "!==e.key)&&(vt.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});v$.displayName=vR;var vz="SelectItemText",vF=i.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:a,...l}=e,s=vu(vz,r),c=vO(vz,r),u=vL(vz,r),f=vd(vz,r),[d,p]=i.useState(null),h=(0,H.s)(t,e=>p(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),y=d?.textContent,m=i.useMemo(()=>(0,o.jsx)("option",{value:u.value,disabled:u.disabled,children:y},u.value),[u.disabled,u.value,y]),{onNativeOptionAdd:v,onNativeOptionRemove:g}=f;return(0,X.N)(()=>(v(m),()=>g(m)),[v,g,m]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(G.sG.span,{id:u.textId,...l,ref:h}),u.isSelected&&s.valueNode&&!s.valueNodeHasChildren?m3.createPortal(l.children,s.valueNode):null]})});vF.displayName=vz;var vU="SelectItemIndicator",vq=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return vL(vU,r).isSelected?(0,o.jsx)(G.sG.span,{"aria-hidden":!0,...n,ref:t}):null});vq.displayName=vU;var vH="SelectScrollUpButton",vW=i.forwardRef((e,t)=>{let r=vO(vH,e.__scopeSelect),n=vN(vH,e.__scopeSelect),[a,l]=i.useState(!1),s=(0,H.s)(t,n.onScrollButtonChange);return(0,X.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,o.jsx)(vX,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});vW.displayName=vH;var vV="SelectScrollDownButton",vG=i.forwardRef((e,t)=>{let r=vO(vV,e.__scopeSelect),n=vN(vV,e.__scopeSelect),[a,l]=i.useState(!1),s=(0,H.s)(t,n.onScrollButtonChange);return(0,X.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,o.jsx)(vX,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});vG.displayName=vV;var vX=i.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:n,...a}=e,l=vO("SelectScrollButton",r),s=i.useRef(null),c=vo(r),u=i.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return i.useEffect(()=>()=>u(),[u]),(0,X.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,o.jsx)(G.sG.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,W.m)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(n,50))}),onPointerMove:(0,W.m)(a.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(n,50))}),onPointerLeave:(0,W.m)(a.onPointerLeave,()=>{u()})})}),vY=i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,o.jsx)(G.sG.div,{"aria-hidden":!0,...n,ref:t})});vY.displayName="SelectSeparator";var vK="SelectArrow";function vZ(e){return""===e||void 0===e}i.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,i=vs(r),a=vu(vK,r),l=vO(vK,r);return a.open&&"popper"===l.position?(0,o.jsx)(m8.i3,{...i,...n,ref:t}):null}).displayName=vK;var vJ=i.forwardRef((e,t)=>{let{value:r,...n}=e,a=i.useRef(null),l=(0,H.s)(t,a),s=function(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return i.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(s!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[s,r]),(0,o.jsx)(m9.s,{asChild:!0,children:(0,o.jsx)("select",{...n,ref:l,defaultValue:r})})});function vQ(e){let t=(0,m7.c)(e),r=i.useRef(""),n=i.useRef(0),o=i.useCallback(e=>{let o=r.current+e;t(o),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),a=i.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,o,a]}function v0(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,l=(n=e,o=Math.max(a,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(l=l.filter(e=>e!==r));let s=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}vJ.displayName="BubbleSelect";let v1=(0,P.A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var v2=r(58450);let v5=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsxs)(vy,{ref:n,className:(0,C.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,o.jsx)(vg,{asChild:!0,children:(0,o.jsx)(e$,{className:"h-4 w-4 opacity-50"})})]}));v5.displayName=vy.displayName;let v4=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vW,{ref:r,className:(0,C.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,o.jsx)(v1,{className:"h-4 w-4"})}));v4.displayName=vW.displayName;let v3=i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vG,{ref:r,className:(0,C.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,o.jsx)(e$,{className:"h-4 w-4"})}));v3.displayName=vG.displayName;let v6=i.forwardRef(({className:e,children:t,position:r="popper",...n},i)=>(0,o.jsx)(vb,{children:(0,o.jsxs)(vw,{ref:i,className:(0,C.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,o.jsx)(v4,{}),(0,o.jsx)(vM,{className:(0,C.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,o.jsx)(v3,{})]})}));v6.displayName=vw.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vD,{ref:r,className:(0,C.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=vD.displayName;let v8=i.forwardRef(({className:e,children:t,...r},n)=>(0,o.jsxs)(v$,{ref:n,className:(0,C.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,o.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,o.jsx)(vq,{children:(0,o.jsx)(v2.A,{className:"h-4 w-4"})})}),(0,o.jsx)(vF,{children:t})]}));v8.displayName=v$.displayName,i.forwardRef(({className:e,...t},r)=>(0,o.jsx)(vY,{ref:r,className:(0,C.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=vY.displayName;let v7=(0,P.A)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]),v9=(0,P.A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]]),ge=(0,P.A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),gt=(0,P.A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),gr=(0,P.A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),gn=(0,P.A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),go=(0,P.A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),gi=(0,P.A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ga=(0,P.A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),gl=tN.Ik({name:tN.Yj().min(1,{message:"Goal name is required."}).max(50,{message:"Name must be 50 characters or less."}),targetAmount:tN.vk(e=>"string"==typeof e?parseFloat(e):e,tN.ai().min(1,{message:"Target amount must be greater than 0."})),icon:tN.Yj().optional()}),gs=[{value:"Default",label:"Default",Icon:v7},{value:"Savings",label:"Savings",Icon:({className:e})=>(0,o.jsxs)("svg",{className:e,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,o.jsx)("path",{d:"M10 21h4"}),(0,o.jsx)("path",{d:"M12 17v4"}),(0,o.jsx)("path",{d:"M10 3h4c2.2 0 4 1.8 4 4v2c0 1.1-.9 2-2 2h-1"}),(0,o.jsx)("path",{d:"M8 11V7c0-2.2 1.8-4 4-4"}),(0,o.jsx)("path",{d:"M19 13c0-1.66-1.34-3-3-3h-2V7"}),(0,o.jsx)("path",{d:"M10 13c2.2 0 4-1.8 4-4"}),(0,o.jsx)("path",{d:"M2 13c2.5 0 2.5-3 5-3s2.5 3 5 3c2.5 0 2.5-3 5-3s2.5 3 5 3"}),(0,o.jsx)("path",{d:"M7.5 13s.5-1 2.5-1 2.5 1 2.5 1"}),(0,o.jsx)("path",{d:"M14 13c2 0 2.5-1 2.5-1"}),(0,o.jsx)("path",{d:"M2 17h.01"})]})},{value:"Vacation",label:"Vacation",Icon:v9},{value:"Shopping",label:"Shopping",Icon:ge},{value:"Car",label:"Car",Icon:gt},{value:"Home",label:"Home Renovation",Icon:gr},{value:"Business",label:"Business",Icon:gn},{value:"Education",label:"Education",Icon:go},{value:"Wedding",label:"Wedding",Icon:gi},{value:"Gift",label:"Gift",Icon:ga}];function gc({onSubmit:e,initialData:t,onClose:r}){let n=(0,tk.mN)({resolver:(0,tP.u)(gl),defaultValues:{name:t?.name||"",targetAmount:t?.targetAmount||0,icon:t?.icon||"Default"}});return(0,o.jsx)(tE.lV,{...n,children:(0,o.jsxs)("form",{onSubmit:n.handleSubmit(o=>{e(o.name,o.targetAmount,o.icon),t||n.reset({name:"",targetAmount:0,icon:"Default"}),r()}),className:"space-y-4 pt-2",children:[(0,o.jsx)(tE.zB,{control:n.control,name:"name",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Goal Name"}),(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(O.p,{placeholder:"e.g., New Laptop, Vacation Fund",...e})}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsx)(tE.zB,{control:n.control,name:"targetAmount",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Target Amount (R)"}),(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(O.p,{type:"number",placeholder:"e.g., 15000",...e,step:"any"})}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsx)(tE.zB,{control:n.control,name:"icon",render:({field:e})=>(0,o.jsxs)(tE.eI,{children:[(0,o.jsx)(tE.lR,{children:"Goal Icon (Optional)"}),(0,o.jsxs)(vp,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,o.jsx)(tE.MJ,{children:(0,o.jsx)(v5,{children:(0,o.jsx)(vv,{placeholder:"Select an icon"})})}),(0,o.jsx)(v6,{children:gs.map(e=>(0,o.jsx)(v8,{value:e.value,children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(e.Icon,{className:"h-4 w-4"}),e.label]})},e.value))})]}),(0,o.jsx)(tE.C5,{})]})}),(0,o.jsxs)("div",{className:"flex justify-end gap-2 pt-2",children:[(0,o.jsx)(F.$,{type:"button",variant:"outline",onClick:r,children:"Cancel"}),(0,o.jsx)(F.$,{type:"submit",children:t?"Save Changes":"Set Goal"})]})]})})}var gu=r(35849);let gf=(0,P.A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),gd=(0,P.A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),gp={Default:v7,Vacation:gu.A,Gadget:gf};function gh({goal:e,onSetGoal:t,onUpdateProgress:r,onClearGoal:n,overallRemaining:a,balancesVisible:l}){let[s,c]=(0,i.useState)(!1),[u,f]=(0,i.useState)(!1),[d,p]=(0,i.useState)(0),[h,y]=(0,i.useState)(!1),m=e=>l?`R ${e.toFixed(2)}`:"R ••••",v=e?.icon&&gp[e.icon]?gp[e.icon]:R;if(!e||e.dateAchieved)return(0,o.jsxs)(A.Zp,{children:[(0,o.jsxs)(A.aR,{className:"pb-2",children:[(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[e?.dateAchieved?(0,o.jsx)(gd,{className:"h-5 w-5 text-green-500"}):(0,o.jsx)(R,{className:"h-5 w-5 text-primary"}),e?.dateAchieved?"Goal Achieved!":"Financial Goal"]}),e?.dateAchieved&&e&&(0,o.jsxs)(A.BT,{className:"text-sm",children:["Congrats on achieving: ",e.name,"!"]})]}),(0,o.jsxs)(A.Wu,{children:[e?.dateAchieved&&e&&(0,o.jsxs)("div",{className:"space-y-1 text-center",children:[(0,o.jsx)("p",{className:"text-2xl font-bold text-green-600",children:m(e.targetAmount)}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Achieved on ",new Date(e.dateAchieved).toLocaleDateString()]})]}),(0,o.jsx)("p",{className:`text-sm text-muted-foreground ${e?.dateAchieved?"mt-2 text-center":"text-center py-4"}`,children:e?.dateAchieved?"Ready for a new challenge?":"Set a financial goal to start saving towards something important!"})]}),(0,o.jsx)(A.wL,{children:(0,o.jsxs)(e1,{open:s,onOpenChange:c,children:[(0,o.jsx)(e5,{asChild:!0,children:(0,o.jsxs)(F.$,{className:"w-full",onClick:()=>c(!0),children:[(0,o.jsx)(tI,{className:"mr-2 h-4 w-4"})," ",e?.dateAchieved?"Set New Goal":"Set a Goal"]})}),(0,o.jsxs)(tx,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tw,{children:(0,o.jsx)(tj,{className:"font-headline",children:e?.dateAchieved?"Set New Financial Goal":"Set Financial Goal"})}),(0,o.jsx)(gc,{onSubmit:(e,r,n)=>{t(e,r,n),c(!1)},initialData:null,onClose:()=>c(!1)})]})]})})]});let g=e.targetAmount>0?e.savedAmount/e.targetAmount*100:0;return(0,o.jsxs)(A.Zp,{children:[(0,o.jsxs)(A.aR,{className:"pb-3 pt-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)(A.ZB,{className:"text-lg flex items-center gap-2 font-headline",children:[(0,o.jsx)(v,{className:"h-5 w-5 text-primary"}),e.name]}),(0,o.jsxs)("div",{className:"flex gap-1",children:[(0,o.jsxs)(e1,{open:s,onOpenChange:c,children:[(0,o.jsx)(e5,{asChild:!0,children:(0,o.jsx)(F.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:()=>c(!0),children:(0,o.jsx)(tO,{className:"h-4 w-4"})})}),(0,o.jsxs)(tx,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tw,{children:(0,o.jsx)(tj,{className:"font-headline",children:"Edit Financial Goal"})}),(0,o.jsx)(gc,{onSubmit:(e,r,n)=>{t(e,r,n),c(!1)},initialData:e,onClose:()=>c(!1)})]})]}),(0,o.jsx)(F.$,{variant:"ghost",size:"icon",className:"h-7 w-7 text-destructive hover:text-destructive",onClick:()=>y(!0),children:(0,o.jsx)(tS.A,{className:"h-4 w-4"})})]})]}),(0,o.jsxs)(A.BT,{className:"text-xs pt-1",children:["Target: ",m(e.targetAmount)," | Saved: ",m(e.savedAmount)]})]}),(0,o.jsxs)(A.Wu,{className:"space-y-3",children:[(0,o.jsx)(tX,{value:g,className:"h-2 [&>*]:bg-primary"}),(0,o.jsxs)("div",{className:"text-xs text-muted-foreground",children:[m(e.targetAmount-e.savedAmount)," still to go. You can do it!"]}),l&&a>0&&(0,o.jsxs)("p",{className:"text-xs text-green-600 bg-green-500/10 p-1.5 rounded-md",children:["You have ",m(a)," unallocated in your budget. Consider putting some towards your goal!"]})]}),(0,o.jsx)(A.wL,{children:(0,o.jsxs)(e1,{open:u,onOpenChange:f,children:[(0,o.jsx)(e5,{asChild:!0,children:(0,o.jsx)(F.$,{className:"w-full",variant:"outline",onClick:()=>{p(e.savedAmount),f(!0)},children:"Log Progress"})}),(0,o.jsxs)(tx,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(tw,{children:(0,o.jsxs)(tj,{className:"font-headline",children:["Log Progress for ",e.name]})}),(0,o.jsxs)("div",{className:"space-y-4 py-2",children:[(0,o.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current Target: ",m(e.targetAmount)]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"progressAmount",className:"block text-sm font-medium text-foreground mb-1",children:"Total Amount Saved Towards Goal (R)"}),(0,o.jsx)("input",{id:"progressAmount",type:l?"number":"text",value:l?d:"••••",onChange:e=>{let t=parseFloat(e.target.value);p(Math.max(0,isNaN(t)?0:t))},readOnly:!l,className:"w-full p-2 border rounded-md border-input",step:"any"})]}),(0,o.jsx)(F.$,{onClick:()=>{r(d),f(!1)},className:"w-full",children:"Save Progress"})]})]})]})}),(0,o.jsx)(t5,{open:h,onOpenChange:y,children:(0,o.jsxs)(rc,{children:[(0,o.jsxs)(ru,{children:[(0,o.jsx)(rd,{children:"Are you sure?"}),(0,o.jsxs)(rp,{children:['This will clear your current financial goal "',e.name,'". This action cannot be undone.']})]}),(0,o.jsxs)(rf,{children:[(0,o.jsx)(ry,{children:"Cancel"}),(0,o.jsx)(rh,{onClick:()=>{n(),y(!1)},className:"bg-destructive hover:bg-destructive/90",children:"Clear Goal"})]})]})})]})}var gy=r(85726);let gm=(0,P.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),gv={trophy:gm,star:(0,P.A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),target:R,zap:$,award:gf,crown:(0,P.A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},gg={gold:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 border-yellow-500/30",silver:"bg-gradient-to-r from-gray-300 to-gray-500 text-gray-900 border-gray-400/30",bronze:"bg-gradient-to-r from-orange-400 to-orange-600 text-orange-900 border-orange-500/30",primary:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground border-primary/30",success:"bg-gradient-to-r from-green-400 to-green-600 text-green-900 border-green-500/30"},gb={sm:"px-2 py-1 text-xs",md:"px-3 py-1.5 text-sm",lg:"px-4 py-2 text-base"};function gx({title:e,description:t,icon:r="trophy",variant:n="gold",size:i="md",animated:a=!0,showConfetti:l=!1,className:s}){let c=gv[r],u=(0,o.jsxs)("div",{className:(0,C.cn)("inline-flex items-center gap-2 rounded-full font-medium border shadow-lg",gg[n],gb[i],a&&"hover:scale-105 transition-transform duration-200",s),children:[(0,o.jsx)(c,{className:(0,C.cn)("shrink-0","sm"===i?"h-3 w-3":"md"===i?"h-4 w-4":"h-5 w-5")}),(0,o.jsx)("span",{className:"font-semibold",children:e})]});return a?(0,o.jsxs)(w.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:260,damping:20,delay:.1},whileHover:{scale:1.05},whileTap:{scale:.95},className:"inline-block",children:[u,t&&(0,o.jsx)(w.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-xs text-muted-foreground mt-1 text-center",children:t})]}):u}function gw({title:e,description:t,icon:r="trophy",variant:n="gold",onClose:i}){let a=gv[r];return(0,o.jsxs)(w.P.div,{initial:{x:300,opacity:0},animate:{x:0,opacity:1},exit:{x:300,opacity:0},transition:{type:"spring",stiffness:300,damping:30},className:(0,C.cn)("flex items-center gap-3 p-4 rounded-lg border shadow-lg max-w-sm","bg-card/95 backdrop-blur-sm border-border/50"),children:[(0,o.jsx)(w.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:.6,repeat:2},className:(0,C.cn)("flex items-center justify-center w-10 h-10 rounded-full",gg[n]),children:(0,o.jsx)(a,{className:"h-5 w-5"})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsx)("h4",{className:"font-semibold text-foreground",children:e}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:t})]}),i&&(0,o.jsx)("button",{onClick:i,className:"text-muted-foreground hover:text-foreground transition-colors",children:"\xd7"})]})}var gj=r(78850);let gO=(0,P.A)("WalletCards",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2",key:"4125el"}],["path",{d:"M3 11h3c.8 0 1.6.3 2.1.9l1.1.9c1.6 1.6 4.1 1.6 5.7 0l1.1-.9c.5-.5 1.3-.9 2.1-.9H21",key:"1dpki6"}]]),gS={BUDGET_STARTER:"BUDGET_STARTER",GOAL_SETTER:"GOAL_SETTER",GOAL_CRUSHER:"GOAL_CRUSHER"},gA={[gS.BUDGET_STARTER]:{title:"Budget Starter!",description:"You've set your income and added your first category!",IconComponent:gO},[gS.GOAL_SETTER]:{title:"Goal Setter!",description:"You've set your first financial goal!",IconComponent:R},[gS.GOAL_CRUSHER]:{title:"Goal Crusher!",description:"Congratulations! You've achieved your financial goal!",IconComponent:gm}};function gP(){let{currentUser:e,loading:t}=(0,gj.A)();(0,l.useRouter)();let[r,n]=(0,i.useState)(!1),[a,s]=(0,i.useState)(0),[c,u]=(0,i.useState)([]),[f,d]=(0,i.useState)(null),[p,h]=(0,i.useState)([]),[y,m]=(0,i.useState)(!0),[v,g]=(0,i.useState)(null),[b,O]=(0,i.useState)(!0),{toast:S}=(0,tY.dj)();(0,i.useCallback)(()=>e?`budgetWiseData_${e.uid}`:null,[e]);let A=(0,i.useCallback)(e=>{if(!p.includes(e)){h(t=>[...t,e]),g(e);let t=gA[e];t&&(S({title:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(t.IconComponent,{className:"h-5 w-5 text-accent"}),(0,o.jsx)("span",{children:t.title})]}),description:t.description}),setTimeout(()=>g(null),5e3))}},[p,S]),P=(0,i.useCallback)(()=>{m(e=>!e)},[]),k=(0,i.useCallback)(e=>{u(t=>t.map(t=>t.id===e?{...t,isVisible:!(t.isVisible??!0)}:t))},[]),N=(0,i.useCallback)(e=>{s(e)},[]),E=(0,i.useCallback)((e,t)=>{let r={id:crypto.randomUUID(),name:e,budget:t,subCategories:[],isVisible:!0};u(e=>[...e,r])},[]),C=(0,i.useCallback)((e,t,r)=>{u(n=>n.map(n=>n.id===e?{...n,name:t,budget:r}:n))},[]),T=(0,i.useCallback)(e=>{u(t=>t.filter(t=>t.id!==e))},[]),_=(0,i.useCallback)((e,t,r)=>{let n=!1;return u(o=>o.map(o=>{if(o.id===e){if(o.subCategories.reduce((e,t)=>e+t.allocatedAmount,0)+r>o.budget)return n=!1,o;let e={id:crypto.randomUUID(),name:t,allocatedAmount:r};return n=!0,{...o,subCategories:[...o.subCategories,e]}}return o})),n},[]),I=(0,i.useCallback)((e,t,r,n)=>{let o=!1;return u(i=>i.map(i=>i.id===e?i.subCategories.filter(e=>e.id!==t).reduce((e,t)=>e+t.allocatedAmount,0)+n>i.budget?(o=!1,i):(o=!0,{...i,subCategories:i.subCategories.map(e=>e.id===t?{...e,name:r,allocatedAmount:n}:e)}):i)),o},[]),D=(0,i.useCallback)((e,t)=>{u(r=>r.map(r=>r.id===e?{...r,subCategories:r.subCategories.filter(e=>e.id!==t)}:r)),S({title:"Subcategory Deleted",description:"Subcategory has been removed."})},[S]),R=(0,i.useCallback)((e,t,r)=>{d({id:f?.id||crypto.randomUUID(),name:e,targetAmount:t,savedAmount:f?.id?f.savedAmount:0,icon:r,dateSet:f?.dateSet||new Date().toISOString(),dateAchieved:null}),p.includes(gS.GOAL_SETTER)&&f||A(gS.GOAL_SETTER),S({title:"Financial Goal Updated!",description:`Your goal "${e}" has been set/updated.`})},[f,A,S,p]),B=(0,i.useCallback)(e=>{if(f){let t={...f,savedAmount:e};e>=f.targetAmount&&!f.dateAchieved&&(t.dateAchieved=new Date().toISOString(),A(gS.GOAL_CRUSHER),S({title:"Goal Achieved!",description:`Congratulations on reaching your goal: ${f.name}!`,duration:5e3})),d(t)}},[f,A,S]),L=(0,i.useCallback)(()=>{d(null),S({title:"Financial Goal Cleared",description:"Your financial goal has been removed."})},[S]),$=c.reduce((e,t)=>e+t.budget,0),F=a-$;return!t&&r&&e?(0,o.jsxs)("div",{className:"flex flex-col min-h-screen bg-background",children:[(0,o.jsx)(j.default,{title:"BudgetWise",balancesVisible:y,onToggleBalances:P}),(0,o.jsx)(x,{children:v&&(0,o.jsx)(w.P.div,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0},className:"fixed top-20 right-4 z-50",children:(0,o.jsx)(gw,{title:gA[v]?.title||"Achievement Unlocked!",description:gA[v]?.description||"You've earned a new achievement!",icon:"trophy",variant:"gold",onClose:()=>g(null)})})}),(0,o.jsxs)("main",{className:"flex-grow container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:[p.length>0&&(0,o.jsx)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-6 flex flex-wrap gap-2",children:p.map(e=>(0,o.jsx)(gx,{title:gA[e]?.title||"Achievement",icon:"trophy",variant:"gold",size:"sm",animated:!0},e))}),(0,o.jsxs)(w.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,o.jsxs)(w.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.1},className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,o.jsx)(M,{totalIncome:a,onIncomeChange:N,balancesVisible:y}),(0,o.jsx)(z,{totalIncome:a,overallTotalAllocated:$,balancesVisible:y}),(0,o.jsx)(gh,{goal:f,onSetGoal:R,onUpdateProgress:B,onClearGoal:L,overallRemaining:F,balancesVisible:y}),(0,o.jsx)(m4,{totalIncome:a,categories:c,balancesVisible:y})]}),(0,o.jsx)(w.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"md:col-span-2",children:(0,o.jsx)(rw,{categories:c,onAddCategory:E,onUpdateCategory:C,onDeleteCategory:T,onAddSubCategory:_,onUpdateSubCategory:I,onDeleteSubCategory:D,onToggleCategoryVisibility:k,balancesVisible:y})})]})]})]}):(0,o.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,o.jsx)(j.default,{title:"BudgetWise",balancesVisible:y,onToggleBalances:P}),(0,o.jsx)(gy.eX,{})]})}},10653:(e,t,r)=>{var n=r(21456),o=r(63979),i=r(7651);e.exports=function(e){return n(e,i,o)}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new o(n,i||e,a),s=r?r+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],l]:e._events[s].push(l):(e._events[s]=l,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,o,i,a){var l=r?r+e:e;if(!this._events[l])return!1;var s,c,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,i),!0;case 6:return u.fn.call(u.context,t,n,o,i,a),!0}for(c=1,s=Array(f-1);c<f;c++)s[c-1]=arguments[c];u.fn.apply(u.context,s)}else{var d,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),f){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,o);break;default:if(!s)for(d=1,s=Array(f-1);d<f;d++)s[d-1]=arguments[d];u[c].fn.apply(u[c].context,s)}}return!0},l.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var l=this._events[i];if(l.fn)l.fn!==t||o&&!l.once||n&&l.context!==n||a(this,i);else{for(var s=0,c=[],u=l.length;s<u;s++)(l[s].fn!==t||o&&!l[s].once||n&&l[s].context!==n)&&c.push(l[s]);c.length?this._events[i]=1===c.length?c[0]:c:a(this,i)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},11424:(e,t,r)=>{var n=r(47603);e.exports=r(66400)(n)},11539:(e,t,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,l=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,c=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=s.test(e);return r||c.test(e)?u(e.slice(2),r?2:8):l.test(e)?a:+e}},12290:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},12344:(e,t,r)=>{e.exports=r(65984)()},14675:e=>{e.exports=function(e){return function(){return e}}},14975:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},15451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},15871:(e,t,r)=>{var n=r(36341),o=r(27467);e.exports=function e(t,r,i,a,l){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,l):t!=t&&r!=r)}},15883:(e,t,r)=>{var n=r(2984),o=r(46063),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},15909:(e,t,r)=>{var n=r(87506),o=r(66930),i=r(658);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:e=>{e.exports=function(e){return this.__data__.has(e)}},17518:(e,t,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),l=r(43378),s=r(89624),c=r(65727),u=r(48169),f=r(40542);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[u];var d=-1;return t=n(t,s(i)),l(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++d,value:e}}),function(e,t){return c(e,t,r)})}},17830:(e,t,r)=>{e.exports=r(41547)(r(85718),"WeakMap")},18234:(e,t,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;e.exports=function(e,t,r){var l=null==e?0:e.length;if(!l)return -1;var s=null==r?0:i(r);return s<0&&(s=a(l+s,0)),n(e,o(t,3),s)}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(e,t,r)=>{var n=r(8336);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=+(r.size!=o),this}},20540:(e,t,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,l=Math.min;e.exports=function(e,t,r){var s,c,u,f,d,p,h=0,y=!1,m=!1,v=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=s,n=c;return s=c=void 0,h=t,f=e.apply(n,r)}function b(e){var r=e-p,n=e-h;return void 0===p||r>=t||r<0||m&&n>=u}function x(){var e,r,n,i=o();if(b(i))return w(i);d=setTimeout(x,(e=i-p,r=i-h,n=t-e,m?l(n,u-r):n))}function w(e){return(d=void 0,v&&s)?g(e):(s=c=void 0,f)}function j(){var e,r=o(),n=b(r);if(s=arguments,c=this,p=r,n){if(void 0===d)return h=e=p,d=setTimeout(x,t),y?g(e):f;if(m)return clearTimeout(d),d=setTimeout(x,t),g(p)}return void 0===d&&(d=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,u=(m="maxWait"in r)?a(i(r.maxWait)||0,t):u,v="trailing"in r?!!r.trailing:v),j.cancel=function(){void 0!==d&&clearTimeout(d),h=0,s=p=c=d=void 0},j.flush=function(){return void 0===d?f:w(o())},j}},20623:(e,t,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),l=r(34883),s=r(41132),c=r(46436);e.exports=function(e,t){return a(e)&&l(t)?s(c(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programming\\\\BudgetWise\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\page.tsx","default")},21367:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},21456:(e,t,r)=>{var n=r(41693),o=r(40542);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},21592:(e,t,r)=>{var n=r(42205),o=r(61837);e.exports=function(e,t){return n(o(e,t),1)}},21630:(e,t,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,l){var s=1&r,c=n(e),u=c.length;if(u!=n(t).length&&!s)return!1;for(var f=u;f--;){var d=c[f];if(!(s?d in t:o.call(t,d)))return!1}var p=l.get(e),h=l.get(t);if(p&&h)return p==t&&h==e;var y=!0;l.set(e,t),l.set(t,e);for(var m=s;++f<u;){var v=e[d=c[f]],g=t[d];if(i)var b=s?i(g,v,d,t,e,l):i(v,g,d,e,t,l);if(!(void 0===b?v===g||a(v,g,r,i,l):b)){y=!1;break}m||(m="constructor"==d)}if(y&&!m){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return l.delete(e),l.delete(t),y}},22964:(e,t,r)=>{e.exports=r(23729)(r(18234))},23729:(e,t,r)=>{var n=r(22),o=r(32269),i=r(7651);e.exports=function(e){return function(t,r,a){var l=Object(t);if(!o(t)){var s=n(r,3);t=i(t),r=function(e){return s(l[e],e,l)}}var c=e(t,r,a);return c>-1?l[s?t[c]:c]:void 0}}},25118:e=>{e.exports=function(e){return this.__data__.has(e)}},27006:(e,t,r)=>{var n=r(46328),o=r(99525),i=r(58276);e.exports=function(e,t,r,a,l,s){var c=1&r,u=e.length,f=t.length;if(u!=f&&!(c&&f>u))return!1;var d=s.get(e),p=s.get(t);if(d&&p)return d==t&&p==e;var h=-1,y=!0,m=2&r?new n:void 0;for(s.set(e,t),s.set(t,e);++h<u;){var v=e[h],g=t[h];if(a)var b=c?a(g,v,h,t,e,s):a(v,g,h,e,t,s);if(void 0!==b){if(b)continue;y=!1;break}if(m){if(!o(t,function(e,t){if(!i(m,t)&&(v===e||l(v,e,r,a,s)))return m.push(t)})){y=!1;break}}else if(!(v===g||l(v,g,r,a,s))){y=!1;break}}return s.delete(e),s.delete(t),y}},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27669:e=>{e.exports=function(){this.__data__=[],this.size=0}},28837:(e,t,r)=>{var n=r(57797),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},28977:(e,t,r)=>{var n=r(11539),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},29205:(e,t,r)=>{var n=r(8336);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=+!!t,t}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,t,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},29508:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).get(e)}},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},30316:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},30401:(e,t,r)=>{e.exports=r(41547)(r(85718),"Promise")},30854:(e,t,r)=>{var n=r(66930),o=r(658),i=r(95746);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},32269:(e,t,r)=>{var n=r(5231),o=r(69619);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},33873:e=>{"use strict";e.exports=require("path")},34117:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:e=>{e.exports=function(e){return this.__data__.get(e)}},34772:(e,t,r)=>{e.exports=r(41547)(r(85718),"Set")},34821:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},34883:(e,t,r)=>{var n=r(55048);e.exports=function(e){return e==e&&!n(e)}},34990:(e,t,r)=>{e.exports=r(87321)()},35142:(e,t,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},35163:(e,t,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,l=i.propertyIsEnumerable;e.exports=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!l.call(e,"callee")}},35697:(e,t,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),l=r(59774),s=r(2408),c=n?n.prototype:void 0,u=c?c.valueOf:void 0;e.exports=function(e,t,r,n,c,f,d){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var p=l;case"[object Set]":var h=1&n;if(p||(p=s),e.size!=t.size&&!h)break;var y=d.get(e);if(y)return y==t;n|=2,d.set(e,t);var m=a(p(e),p(t),n,c,f,d);return d.delete(e),m;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},35800:(e,t,r)=>{var n=r(57797);e.exports=function(e){return n(this.__data__,e)>-1}},36315:(e,t,r)=>{var n=r(22),o=r(92662);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},36341:(e,t,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),l=r(1566),s=r(40542),c=r(80329),u=r(10090),f="[object Arguments]",d="[object Array]",p="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,m,v){var g=s(e),b=s(t),x=g?d:l(e),w=b?d:l(t);x=x==f?p:x,w=w==f?p:w;var j=x==p,O=w==p,S=x==w;if(S&&c(e)){if(!c(t))return!1;g=!0,j=!1}if(S&&!j)return v||(v=new n),g||u(e)?o(e,t,r,y,m,v):i(e,t,x,r,y,m,v);if(!(1&r)){var A=j&&h.call(e,"__wrapped__"),P=O&&h.call(t,"__wrapped__");if(A||P){var k=A?e.value():e,N=P?t.value():t;return v||(v=new n),m(k,N,r,y,v)}}return!!S&&(v||(v=new n),a(e,t,r,y,m,v))}},36959:e=>{e.exports=function(){}},37456:e=>{e.exports=function(e){return null==e}},37575:(e,t,r)=>{var n=r(66930);e.exports=function(){this.__data__=new n,this.size=0}},37643:(e,t,r)=>{var n=r(6053),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},38404:(e,t,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,l=Function.prototype.toString,s=a.hasOwnProperty,c=l.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=s.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&l.call(r)==c}},38428:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},39672:(e,t,r)=>{var n=r(58141);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},39774:e=>{e.exports=function(e){return e!=e}},40491:(e,t,r)=>{var n=r(1707);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},40542:e=>{e.exports=Array.isArray},41011:(e,t,r)=>{var n=r(41353);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},41132:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},41157:(e,t,r)=>{var n=r(91928);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},41353:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},41547:(e,t,r)=>{var n=r(61548),o=r(90851);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},41693:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},42082:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},42205:(e,t,r)=>{var n=r(41693),o=r(85450);e.exports=function e(t,r,i,a,l){var s=-1,c=t.length;for(i||(i=o),l||(l=[]);++s<c;){var u=t[s];r>0&&i(u)?r>1?e(u,r-1,i,a,l):n(l,u):a||(l[l.length]=u)}return l}},42403:(e,t,r)=>{var n=r(80195);e.exports=function(e){return null==e?"":n(e)}},43378:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},43707:(e,t,r)=>{Promise.resolve().then(r.bind(r,10188))},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>s,Zp:()=>a,aR:()=>l,wL:()=>f});var n=r(60687),o=r(43210),i=r(4780);let a=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));a.displayName="Card";let l=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let s=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));s.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent";let f=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));f.displayName="CardFooter"},45058:(e,t,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);e.exports=function(e){return i(e)?n(a(e)):o(e)}},45603:(e,t,r)=>{var n=r(20540),o=r(55048);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},45803:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},46063:e=>{e.exports=function(e,t){return e<t}},46229:(e,t,r)=>{var n=r(48169),o=r(66354),i=r(11424);e.exports=function(e,t){return i(o(e,t,n),e+"")}},46328:(e,t,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},46436:(e,t,r)=>{var n=r(49227),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},47212:(e,t,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),l=r(7383);e.exports=function(e,t,r){var s=a(e)?n:o;return r&&l(e,t,r)&&(t=void 0),s(e,i(t,3))}},47282:(e,t,r)=>{e=r.nmd(e);var n=r(10663),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,l=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=l},47603:(e,t,r)=>{var n=r(14675),o=r(91928),i=r(48169);e.exports=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i},48169:e=>{e.exports=function(e){return e}},48385:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",l="(?:"+r+"|"+n+")?",s="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[o,i,a].join("|")+")"+s+l+")*",u=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|"))+")"+(s+l+c),"g");e.exports=function(e){return e.match(u)||[]}},49227:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},51449:(e,t,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;e.exports=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t})},52599:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},52823:(e,t,r)=>{var n=r(85406),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},52931:(e,t,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},54070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>u,routeModule:()=>d,tree:()=>c});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),l=r(30893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Desktop\\Programming\\BudgetWise\\src\\app\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},54765:(e,t,r)=>{var n=r(67554),o=r(32269);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},55048:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},56506:(e,t,r)=>{var n=r(32269);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,l=Object(r);(t?a--:++a<i)&&!1!==o(l[a],a,l););return r}}},57088:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(22);e.exports=function(e,t){return e&&e.length?n(e,i(t,2),o):void 0}},57207:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57797:(e,t,r)=>{var n=r(67009);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},58141:(e,t,r)=>{e.exports=r(41547)(Object,"create")},58276:e=>{e.exports=function(e,t){return e.has(t)}},58744:(e,t,r)=>{var n=r(57797);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},59467:(e,t,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),l=r(69619),s=r(46436);e.exports=function(e,t,r){t=n(t,e);for(var c=-1,u=t.length,f=!1;++c<u;){var d=s(t[c]);if(!(f=null!=e&&r(e,d)))break;e=e[d]}return f||++c!=u?f:!!(u=null==e?0:e.length)&&l(u)&&a(d,u)&&(i(e)||o(e))}},59774:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},61320:(e,t,r)=>{var n=r(8336);e.exports=function(e){return n(this,e).has(e)}},61548:(e,t,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),l=/^\[object .+?Constructor\]$/,s=Object.prototype,c=Function.prototype.toString,u=s.hasOwnProperty,f=RegExp("^"+c.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:l).test(a(e))}},61837:(e,t,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(e,t,r)=>{var n=r(29395),o=r(40542),i=r(27467);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},63979:(e,t,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;e.exports=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o},65662:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},65727:(e,t,r)=>{var n=r(81957);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,l=i.length,s=r.length;++o<l;){var c=n(i[o],a[o]);if(c){if(o>=s)return c;return c*("desc"==r[o]?-1:1)}}return e.index-t.index}},65932:(e,t,r)=>{e.exports=r(65662)(Object.getPrototypeOf,Object)},65984:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),l=a.length;l--;){var s=a[e?l:++o];if(!1===r(i[s],s,i))break}return t}}},66354:(e,t,r)=>{var n=r(85244),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,l=o(i.length-t,0),s=Array(l);++a<l;)s[a]=i[t+a];a=-1;for(var c=Array(t+1);++a<t;)c[a]=i[a];return c[t]=r(s),n(e,this,c)}}},66400:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},66713:(e,t,r)=>{var n=r(3105),o=r(34117),i=r(48385);e.exports=function(e){return o(e)?i(e):n(e)}},66837:(e,t,r)=>{var n=r(58141);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(e,t,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),l=r(58744);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},67009:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},67200:(e,t,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),l=r(25118),s=r(30854);function c(e){var t=this.__data__=new n(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=i,c.prototype.get=a,c.prototype.has=l,c.prototype.set=s,e.exports=c},67367:(e,t,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),l=r(7383);e.exports=function(e,t,r){var s=a(e)?n:i;return r&&l(e,t,r)&&(t=void 0),s(e,o(t,3))}},67554:(e,t,r)=>{var n=r(99114);e.exports=r(56506)(n)},67619:(e,t,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},69433:(e,t,r)=>{e.exports=r(5566)("toUpperCase")},69619:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},69691:(e,t,r)=>{var n=r(41157),o=r(99114),i=r(22);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},70151:(e,t,r)=>{var n=r(85718);e.exports=function(){return n.Date.now()}},70222:(e,t,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,l=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,l),r=e[l];try{e[l]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[l]=r:delete e[l]),o}},71669:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>m,Rr:()=>v,eI:()=>h,lR:()=>y,lV:()=>c,zB:()=>f});var n=r(60687),o=r(43210),i=r(8730),a=r(27605),l=r(4780),s=r(80013);let c=a.Op,u=o.createContext({}),f=({...e})=>(0,n.jsx)(u.Provider,{value:{name:e.name},children:(0,n.jsx)(a.xI,{...e})}),d=()=>{let e=o.useContext(u),t=o.useContext(p),{getFieldState:r,formState:n}=(0,a.xW)(),i=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},p=o.createContext({}),h=o.forwardRef(({className:e,...t},r)=>{let i=o.useId();return(0,n.jsx)(p.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",e),...t})})});h.displayName="FormItem";let y=o.forwardRef(({className:e,...t},r)=>{let{error:o,formItemId:i}=d();return(0,n.jsx)(s.J,{ref:r,className:(0,l.cn)(o&&"text-destructive",e),htmlFor:i,...t})});y.displayName="FormLabel";let m=o.forwardRef(({...e},t)=>{let{error:r,formItemId:o,formDescriptionId:a,formMessageId:l}=d();return(0,n.jsx)(i.DX,{ref:t,id:o,"aria-describedby":r?`${a} ${l}`:`${a}`,"aria-invalid":!!r,...e})});m.displayName="FormControl";let v=o.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:o}=d();return(0,n.jsx)("p",{ref:r,id:o,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});v.displayName="FormDescription";let g=o.forwardRef(({className:e,children:t,...r},o)=>{let{error:i,formMessageId:a}=d(),s=i?String(i?.message??""):t;return s?(0,n.jsx)("p",{ref:o,id:a,className:(0,l.cn)("text-sm font-medium text-destructive",e),...r,children:s}):null});g.displayName="FormMessage"},71960:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},71967:(e,t,r)=>{var n=r(15871);e.exports=function(e,t){return n(e,t)}},74610:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},75254:(e,t,r)=>{var n=r(78418),o=r(93311),i=r(41132);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},75411:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},75847:(e,t,r)=>{var n=r(67554);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},77822:(e,t,r)=>{var n=r(93490);e.exports=function(e){return n(e)&&e!=+e}},77834:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},78051:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},78418:(e,t,r)=>{var n=r(67200),o=r(15871);e.exports=function(e,t,r,i){var a=r.length,l=a,s=!i;if(null==e)return!l;for(e=Object(e);a--;){var c=r[a];if(s&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++a<l;){var u=(c=r[a])[0],f=e[u],d=c[1];if(s&&c[2]){if(void 0===f&&!(u in e))return!1}else{var p=new n;if(i)var h=i(f,d,u,e,t,p);if(!(void 0===h?o(d,f,3,i,p):h))return!1}}return!0}},79474:(e,t,r)=>{e.exports=r(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(60687),o=r(43210),i=r(78148),a=r(24224),l=r(4780);let s=(0,a.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)(i.b,{ref:r,className:(0,l.cn)(s(),e),...t}));c.displayName=i.b.displayName},80195:(e,t,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),l=1/0,s=n?n.prototype:void 0,c=s?s.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-l?"-0":r}},80329:(e,t,r)=>{e=r.nmd(e);var n=r(85718),o=r(1944),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,l=a&&a.exports===i?n.Buffer:void 0,s=l?l.isBuffer:void 0;e.exports=s||o},80458:(e,t,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},80704:(e,t,r)=>{var n=r(96678);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},81488:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},81957:(e,t,r)=>{var n=r(49227);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),l=void 0!==t,s=null===t,c=t==t,u=n(t);if(!s&&!u&&!a&&e>t||a&&l&&c&&!s&&!u||o&&l&&c||!r&&c||!i)return 1;if(!o&&!a&&!u&&e<t||u&&r&&i&&!o&&!a||s&&r&&i||!l&&i||!c)return -1}return 0}},82038:(e,t,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),l=r(38428),s=r(10090),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&o(e),f=!r&&!u&&a(e),d=!r&&!u&&!f&&s(e),p=r||u||f||d,h=p?n(e.length,String):[],y=h.length;for(var m in e)(t||c.call(e,m))&&!(p&&("length"==m||f&&("offset"==m||"parent"==m)||d&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||l(m,y)))&&h.push(m);return h}},84031:(e,t,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var l=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=+!!t,t}},84482:(e,t,r)=>{var n=r(28977);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},84713:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},85244:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},85406:(e,t,r)=>{e.exports=r(85718)["__core-js_shared__"]},85450:(e,t,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},85718:(e,t,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},85726:(e,t,r)=>{"use strict";r.d(t,{EA:()=>i,eX:()=>s});var n=r(60687),o=r(4780);function i({className:e,...t}){return(0,n.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...t})}function a(){return(0,n.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i,{className:"h-5 w-5 rounded"}),(0,n.jsx)(i,{className:"h-5 w-32"})]}),(0,n.jsx)(i,{className:"h-8 w-20 rounded-md"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)(i,{className:"h-4 w-24"}),(0,n.jsx)(i,{className:"h-4 w-16"})]}),(0,n.jsx)(i,{className:"h-2 w-full rounded-full"}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)(i,{className:"h-4 w-20"}),(0,n.jsx)(i,{className:"h-4 w-16"})]})]})]})}function l(){return(0,n.jsxs)("div",{className:"rounded-lg border bg-card p-6 shadow-sm animate-fade-in",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,n.jsx)(i,{className:"h-5 w-5 rounded"}),(0,n.jsx)(i,{className:"h-5 w-32"})]}),(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)(i,{className:"h-48 w-48 rounded-full"})}),(0,n.jsxs)("div",{className:"mt-6 flex flex-wrap gap-2 justify-center",children:[(0,n.jsx)(i,{className:"h-4 w-16 rounded-full"}),(0,n.jsx)(i,{className:"h-4 w-20 rounded-full"}),(0,n.jsx)(i,{className:"h-4 w-14 rounded-full"}),(0,n.jsx)(i,{className:"h-4 w-18 rounded-full"})]})]})}function s(){return(0,n.jsx)("div",{className:"container mx-auto py-4 sm:py-6 px-2 sm:px-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6",children:[(0,n.jsxs)("div",{className:"md:col-span-1 space-y-4 sm:space-y-6",children:[(0,n.jsx)(a,{}),(0,n.jsx)(a,{}),(0,n.jsx)(a,{})]}),(0,n.jsxs)("div",{className:"md:col-span-2 space-y-4 sm:space-y-6",children:[(0,n.jsx)(a,{}),(0,n.jsx)(l,{})]})]})})}},85745:(e,t,r)=>{var n=r(86451);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},85938:(e,t,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);e.exports=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])})},86451:(e,t,r)=>{var n=r(95746);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},87270:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},87321:(e,t,r)=>{var n=r(98798),o=r(7383),i=r(28977);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},87506:(e,t,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),l=r(39672);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},87955:(e,t,r)=>{e.exports=r(84031)()},89167:(e,t,r)=>{e.exports=r(41547)(r(85718),"DataView")},89185:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},89492:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},89605:(e,t,r)=>{e.exports=r(65662)(Object.keys,Object)},89624:e=>{e.exports=function(e){return function(t){return e(t)}}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(60687),o=r(43210),i=r(4780);let a=o.forwardRef(({className:e,type:t,...r},o)=>(0,n.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...r}));a.displayName="Input"},90200:(e,t,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},90453:(e,t,r)=>{var n=r(2984),o=r(99180),i=r(48169);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},90851:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},91290:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},91928:(e,t,r)=>{var n=r(41547);e.exports=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}()},92662:(e,t,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),l=r(95308),s=r(2408);e.exports=function(e,t,r){var c=-1,u=o,f=e.length,d=!0,p=[],h=p;if(r)d=!1,u=i;else if(f>=200){var y=t?null:l(e);if(y)return s(y);d=!1,u=a,h=new n}else h=t?[]:p;t:for(;++c<f;){var m=e[c],v=t?t(m):m;if(m=r||0!==m?m:0,d&&v==v){for(var g=h.length;g--;)if(h[g]===v)continue t;t&&h.push(v),p.push(m)}else u(h,v,r)||(h!==p&&h.push(v),p.push(m))}return p}},93311:(e,t,r)=>{var n=r(34883),o=r(7651);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},93490:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},94388:(e,t,r)=>{var n=r(57797);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},95308:(e,t,r)=>{var n=r(34772),o=r(36959),i=r(2408);e.exports=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o},95746:(e,t,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),l=r(19976);function s(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}s.prototype.clear=n,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},96678:(e,t,r)=>{var n=r(91290),o=r(39774),i=r(74610);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},97668:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case l:case a:case d:case p:return e;default:switch(e=e&&e.$$typeof){case u:case c:case f:case y:case h:case s:return e;default:return t}}case o:return t}}}(e)===i}},98451:(e,t,r)=>{var n=r(29395),o=r(27467);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},98798:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,l=r(t((n-e)/(o||1)),0),s=Array(l);l--;)s[i?l:++a]=e,e+=o;return s}},99114:(e,t,r)=>{var n=r(12344),o=r(7651);e.exports=function(e,t){return e&&n(e,t,o)}},99180:e=>{e.exports=function(e,t){return e>t}},99525:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,242,99,758,330,253,529],()=>r(54070));module.exports=n})();