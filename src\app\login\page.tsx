
'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import LoginForm from '@/components/auth/LoginForm';
import GoogleSignInButton from '@/components/auth/GoogleSignInButton';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PiggyBank } from 'lucide-react';
import { AnimatedWrapper, FloatingElement } from '@/components/ui/animated-wrapper';
import { motion } from 'framer-motion';

export default function LoginPage() {
  const { currentUser, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && currentUser) {
      router.push('/');
    }
  }, [currentUser, loading, router]);

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }
  if (!loading && currentUser) {
    return null; // Or a loading spinner, effectively handled by redirect
  }

  return (
    <motion.div
      className="flex flex-col items-center justify-center min-h-screen bg-background p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <AnimatedWrapper variant="scaleIn" delay={0.2}>
        <Card className="w-full max-w-md card-enhanced glow-primary">
          <CardHeader className="text-center">
            <div className="flex justify-center items-center mb-4">
              <FloatingElement>
                <PiggyBank className="h-12 w-12 text-primary" />
              </FloatingElement>
            </div>
            <CardTitle className="text-2xl font-headline">Welcome Back to BudgetWise!</CardTitle>
            <CardDescription>Sign in to manage your finances.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <LoginForm />
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-border/50" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-3 text-muted-foreground font-medium">
                  Or continue with
                </span>
              </div>
            </div>
            <GoogleSignInButton />
            <motion.p
              className="text-center text-sm text-muted-foreground"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              Don&apos;t have an account?{' '}
              <Link href="/register" className="font-medium text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline">
                Sign up
              </Link>
            </motion.p>
          </CardContent>
        </Card>
      </AnimatedWrapper>
    </motion.div>
  );
}
