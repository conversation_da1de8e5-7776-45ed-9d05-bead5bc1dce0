"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[440],{1978:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>sf});let s=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,n){if("function"==typeof e){let[r,s]=l(n);e=e(void 0!==i?i:t.custom,r,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,s]=l(n);e=e(void 0!==i?i:t.custom,r,s)}return e}function h(t,e,i){let n=t.getProps();return u(n,e,void 0!==i?i:n.custom,t)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...d];function p(t){let e;return()=>(void 0===e&&(e=t()),e)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends f{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function g(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function x(t){return"function"==typeof t}function P(t,e){t.timeline=e,t.onfinish=null}let w=t=>Array.isArray(t)&&"number"==typeof t[0],T={linearEasing:void 0},b=function(t,e){let i=p(t);return()=>{var t;return null!==(t=T[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),S=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n},A=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(S(0,r-1,e))+", ";return`linear(${n.substring(0,n.length-2)})`},E=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:E([0,.65,.55,1]),circOut:E([.55,0,1,.45]),backIn:E([.31,.01,.66,-.59]),backOut:E([.33,1.53,.69,.99])},V={x:!1,y:!1};function C(t,e){let i=function(t,e,i){var n;if(t instanceof Element)return[t];if("string"==typeof t){let e=document,i=(n=void 0,e.querySelectorAll(t));return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function D(t){return e=>{"touch"===e.pointerType||V.x||V.y||t(e)}}let R=(t,e)=>!!e&&(t===e||R(t,e.parentElement)),k=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,j=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function F(t){return e=>{"Enter"===e.key&&t(e)}}function B(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let O=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=F(()=>{if(L.has(i))return;B(i,"down");let t=F(()=>{B(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>B(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function I(t){return k(t)&&!(V.x||V.y)}let U=t=>1e3*t,N=t=>t/1e3,$=t=>t,W=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],_=new Set(W),z=new Set(["width","height","top","left","right","bottom",...W]),K=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),Y=t=>s(t)?t[t.length-1]||0:t,H={skipAnimations:!1,useManualTiming:!1},X=["read","resolveKeyframes","update","preRender","render","postRender"];function q(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=X.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,n=!1,r=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){s.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,r=!1,o=!1)=>{let a=o&&n?e:i;return r&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),s.delete(t)},process:t=>{if(o=t,n){r=!0;return}n=!0,[e,i]=[i,e],e.forEach(a),e.clear(),n=!1,r&&(r=!1,l.process(t))}};return l}(s),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let s=H.useManualTiming?r.timestamp:performance.now();i=!1,r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(p))},m=()=>{i=!0,n=!0,r.isProcessing||t(p)};return{schedule:X.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||m(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<X.length;e++)o[X[e]].cancel(t)},state:r,steps:o}}let{schedule:G,cancel:Z,state:Q,steps:J}=q("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:$,!0);function tt(){n=void 0}let te={now:()=>(void 0===n&&te.set(Q.isProcessing||H.useManualTiming?Q.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(tt)}};function ti(t,e){-1===t.indexOf(e)&&t.push(e)}function tn(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class tr{constructor(){this.subscriptions=[]}add(t){return ti(this.subscriptions,t),()=>tn(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ts=t=>!isNaN(parseFloat(t)),to={current:void 0};class ta{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=te.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ts(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new tr);let i=this.events[t].add(e);return"change"===t?()=>{i(),G.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return to.current&&to.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function th(t,e){let i=t.getValue("willChange");if(tu(i)&&i.add)return i.add(e)}let td=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tc="data-"+td("framerAppearId"),tp={current:!1},tm=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tf(t,e,i,n){if(t===e&&i===n)return $;let r=e=>(function(t,e,i,n,r){let s,o;let a=0;do(s=tm(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tm(r(t),e,n)}let tv=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tg=t=>e=>1-t(1-e),ty=tf(.33,1.53,.69,.99),tx=tg(ty),tP=tv(tx),tw=t=>(t*=2)<1?.5*tx(t):.5*(2-Math.pow(2,-10*(t-1))),tT=t=>1-Math.sin(Math.acos(t)),tb=tg(tT),tS=tv(tT),tA=t=>/^0[^.\s]+$/u.test(t),tE=(t,e,i)=>i>e?e:i<t?t:i,tM={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tV={...tM,transform:t=>tE(0,1,t)},tC={...tM,default:1},tD=t=>Math.round(1e5*t)/1e5,tR=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tk=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tj=(t,e)=>i=>!!("string"==typeof i&&tk.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tL=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(tR);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tF=t=>tE(0,255,t),tB={...tM,transform:t=>Math.round(tF(t))},tO={test:tj("rgb","red"),parse:tL("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tB.transform(t)+", "+tB.transform(e)+", "+tB.transform(i)+", "+tD(tV.transform(n))+")"},tI={test:tj("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tO.transform},tU=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tN=tU("deg"),t$=tU("%"),tW=tU("px"),t_=tU("vh"),tz=tU("vw"),tK={...t$,parse:t=>t$.parse(t)/100,transform:t=>t$.transform(100*t)},tY={test:tj("hsl","hue"),parse:tL("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+t$.transform(tD(e))+", "+t$.transform(tD(i))+", "+tD(tV.transform(n))+")"},tH={test:t=>tO.test(t)||tI.test(t)||tY.test(t),parse:t=>tO.test(t)?tO.parse(t):tY.test(t)?tY.parse(t):tI.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tO.transform(t):tY.transform(t)},tX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tq="number",tG="color",tZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tQ(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tZ,t=>(tH.test(t)?(n.color.push(s),r.push(tG),i.push(tH.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tq),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tJ(t){return tQ(t).values}function t0(t){let{split:e,types:i}=tQ(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tq?r+=tD(t[s]):e===tG?r+=tH.transform(t[s]):r+=t[s]}return r}}let t1=t=>"number"==typeof t?0:t,t5={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tR))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tX))||void 0===i?void 0:i.length)||0)>0},parse:tJ,createTransformer:t0,getAnimatableNone:function(t){let e=tJ(t);return t0(t)(e.map(t1))}},t2=new Set(["brightness","contrast","saturate","opacity"]);function t9(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tR)||[];if(!n)return t;let r=i.replace(n,""),s=+!!t2.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let t3=/\b([a-z-]*)\(.*?\)/gu,t8={...t5,getAnimatableNone:t=>{let e=t.match(t3);return e?e.map(t9).join(" "):t}},t4={...tM,transform:Math.round},t6={borderWidth:tW,borderTopWidth:tW,borderRightWidth:tW,borderBottomWidth:tW,borderLeftWidth:tW,borderRadius:tW,radius:tW,borderTopLeftRadius:tW,borderTopRightRadius:tW,borderBottomRightRadius:tW,borderBottomLeftRadius:tW,width:tW,maxWidth:tW,height:tW,maxHeight:tW,top:tW,right:tW,bottom:tW,left:tW,padding:tW,paddingTop:tW,paddingRight:tW,paddingBottom:tW,paddingLeft:tW,margin:tW,marginTop:tW,marginRight:tW,marginBottom:tW,marginLeft:tW,backgroundPositionX:tW,backgroundPositionY:tW,rotate:tN,rotateX:tN,rotateY:tN,rotateZ:tN,scale:tC,scaleX:tC,scaleY:tC,scaleZ:tC,skew:tN,skewX:tN,skewY:tN,distance:tW,translateX:tW,translateY:tW,translateZ:tW,x:tW,y:tW,z:tW,perspective:tW,transformPerspective:tW,opacity:tV,originX:tK,originY:tK,originZ:tW,zIndex:t4,size:tW,fillOpacity:tV,strokeOpacity:tV,numOctaves:t4},t7={...t6,color:tH,backgroundColor:tH,outlineColor:tH,fill:tH,stroke:tH,borderColor:tH,borderTopColor:tH,borderRightColor:tH,borderBottomColor:tH,borderLeftColor:tH,filter:t8,WebkitFilter:t8},et=t=>t7[t];function ee(t,e){let i=et(t);return i!==t8&&(i=t5),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ei=new Set(["auto","none","0"]),en=t=>t===tM||t===tW,er=(t,e)=>parseFloat(t.split(", ")[e]),es=(t,e)=>(i,{transform:n})=>{if("none"===n||!n)return 0;let r=n.match(/^matrix3d\((.+)\)$/u);if(r)return er(r[1],e);{let e=n.match(/^matrix\((.+)\)$/u);return e?er(e[1],t):0}},eo=new Set(["x","y","z"]),ea=W.filter(t=>!eo.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:es(4,13),y:es(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,eh=!1,ed=!1;function ec(){if(ed){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var n;null===(n=t.getValue(e))||void 0===n||n.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ed=!1,eh=!1,eu.forEach(t=>t.complete()),eu.clear()}function ep(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ed=!0)})}class em{constructor(t,e,i,n,r,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),eh||(eh=!0,G.read(ep),G.resolveKeyframes(ec))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;for(let r=0;r<t.length;r++)if(null===t[r]){if(0===r){let r=null==n?void 0:n.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}else t[r]=t[r-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ef=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ev=t=>e=>"string"==typeof e&&e.startsWith(t),eg=ev("--"),ey=ev("var(--"),ex=t=>!!ey(t)&&eP.test(t.split("/*")[0].trim()),eP=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ew=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eT=t=>e=>e.test(t),eb=[tM,tW,t$,tN,tz,t_,{test:t=>"auto"===t,parse:t=>t}],eS=t=>eb.find(eT(t));class eA extends em{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&ex(n=n.trim())){let r=function t(e,i,n=1){$(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=ew.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${null!=i?i:n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return ef(t)?parseFloat(t):t}return ex(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!z.has(i)||2!==t.length)return;let[n,r]=t,s=eS(n),o=eS(r);if(s!==o){if(en(s)&&en(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tA(n))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!ei.has(e)&&tQ(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=ee(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(i);r&&r.jump(this.measuredOrigin,!1);let s=n.length-1,o=n[s];n[s]=el[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eE=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t5.test(t)||"0"===t)&&!t.startsWith("url(")),eM=t=>null!==t;function eV(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(eM),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return s&&void 0!==n?n:r[s]}class eC{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ep(),ec()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:i,type:n,velocity:r,delay:s,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eE(r,e),a=eE(s,e);return $(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||x(i))&&n)}(t,i,n,r)){if(tp.current||!s){a&&a(eV(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eD=(t,e,i)=>t+(e-t)*i;function eR(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ek(t,e){return i=>i>0?e:t}let ej=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},eL=[tI,tO,tY],eF=t=>eL.find(e=>e.test(t));function eB(t){let e=eF(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tY&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=eR(a,n,t+1/3),s=eR(a,n,t),o=eR(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let eO=(t,e)=>{let i=eB(t),n=eB(e);if(!i||!n)return ek(t,e);let r={...i};return t=>(r.red=ej(i.red,n.red,t),r.green=ej(i.green,n.green,t),r.blue=ej(i.blue,n.blue,t),r.alpha=eD(i.alpha,n.alpha,t),tO.transform(r))},eI=(t,e)=>i=>e(t(i)),eU=(...t)=>t.reduce(eI),eN=new Set(["none","hidden"]);function e$(t,e){return i=>eD(t,e,i)}function eW(t){return"number"==typeof t?e$:"string"==typeof t?ex(t)?ek:tH.test(t)?eO:eK:Array.isArray(t)?e_:"object"==typeof t?tH.test(t)?eO:ez:ek}function e_(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>eW(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function ez(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=eW(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let eK=(t,e)=>{let i=t5.createTransformer(e),n=tQ(t),r=tQ(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?eN.has(t)&&!r.values.length||eN.has(e)&&!n.values.length?function(t,e){return eN.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eU(e_(function(t,e){var i;let n=[],r={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let o=e.types[s],a=t.indexes[o][r[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;n[s]=l,r[o]++}return n}(n,r),r.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),ek(t,e))};function eY(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eD(t,e,i):eW(t)(t,e)}function eH(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let eX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eq(t,e){return t*Math.sqrt(1-e*e)}let eG=["duration","bounce"],eZ=["stiffness","damping","mass"];function eQ(t,e){return e.some(e=>void 0!==t[e])}function eJ(t=eX.visualDuration,e=eX.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eX.velocity,stiffness:eX.stiffness,damping:eX.damping,mass:eX.mass,isResolvedFromDuration:!1,...t};if(!eQ(t,eZ)&&eQ(t,eG)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*tE(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:eX.mass,stiffness:n,damping:r}}else{let i=function({duration:t=eX.duration,bounce:e=eX.bounce,velocity:i=eX.velocity,mass:n=eX.mass}){let r,s;$(t<=U(eX.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tE(eX.minDamping,eX.maxDamping,o),t=tE(eX.minDuration,eX.maxDuration,N(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/eq(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=eq(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=U(t),isNaN(a))return{stiffness:eX.stiffness,damping:eX.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:eX.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-N(n.velocity||0)}),f=p||0,v=h/(2*Math.sqrt(u*d)),g=a-o,x=N(Math.sqrt(u/d)),P=5>Math.abs(g);if(r||(r=P?eX.restSpeed.granular:eX.restSpeed.default),s||(s=P?eX.restDelta.granular:eX.restDelta.default),v<1){let t=eq(x,v);i=e=>a-Math.exp(-v*x*e)*((f+v*x*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===v)i=t=>a-Math.exp(-x*t)*(g+(f+x*g)*t);else{let t=x*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*x*e),n=Math.min(t*e,300);return a-i*((f+v*x*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0;v<1&&(n=0===t?U(f):eH(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(w),2e4),e=A(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function e0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let P=t=>-g*Math.exp(-t/n),w=t=>x+P(t),T=t=>{let e=P(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=eJ({keyframes:[m.value,v(m.value)],velocity:eH(w,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}let e1=tf(.42,0,1,1),e5=tf(0,0,.58,1),e2=tf(.42,0,.58,1),e9=t=>Array.isArray(t)&&"number"!=typeof t[0],e3={linear:$,easeIn:e1,easeInOut:e2,easeOut:e5,circIn:tT,circInOut:tS,circOut:tb,backIn:tx,backInOut:tP,backOut:ty,anticipate:tw},e8=t=>{if(w(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tf(e,i,n,r)}return"string"==typeof t?($(void 0!==e3[t],`Invalid easing type '${t}'`),e3[t]):t};function e4({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=e9(n)?n.map(e8):e8(n),s={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if($(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||eY,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=eU(Array.isArray(e)?e[i]||$:e,s)),n.push(s)}return n}(e,n,r),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=S(t[n],t[n+1],i);return a[n](r)};return i?e=>u(tE(t[0],t[s-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=S(0,e,n);t.push(eD(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||e2).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}let e6=t=>{let e=({timestamp:e})=>t(e);return{start:()=>G.update(e,!0),stop:()=>Z(e),now:()=>Q.isProcessing?Q.timestamp:te.now()}},e7={decay:e0,inertia:e0,tween:e4,keyframes:e4,spring:eJ},it=t=>t/100;class ie extends eC{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:n,keyframes:r}=this.options,s=(null==n?void 0:n.KeyframeResolver)||em;this.resolver=new s(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=x(n)?n:e7[n]||e4;l!==e4&&"number"!=typeof t[0]&&(e=eU(it,eY(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:h}=u,d=h+s;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(r+1)-s}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:r,mirroredGenerator:s,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return r.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=r;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=s)),y=tE(0,1,i)*h}let P=g?{done:!1,value:a[0]}:x.next(y);o&&(P.value=o(P.value));let{done:w}=P;g||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==n&&(P.value=eV(a,this.options,n)),f&&f(P.value),T&&this.finish(),P}get duration(){let{resolved:t}=this;return t?N(t.calculatedDuration):0}get time(){return N(this.currentTime)}set time(t){t=U(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e6,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),ir=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:tw,backInOut:tP,circInOut:tS};class io extends eC{constructor(t){super(t);let{name:e,motionValue:i,element:n,keyframes:r}=this.options;this.resolver=new eA(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:n=300,times:r,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof s&&b()&&s in is&&(s=is[s]),x((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&b()||!e||"string"==typeof e&&(e in M||b())||w(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ie({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},r=[],s=0;for(;!n.done&&s<2e4;)r.push((n=i.sample(s)).value),s+=10;return{times:void 0,keyframes:r,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),n=h.duration,r=h.times,s=h.ease,o="keyframes"}let h=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&b()?A(e,i):w(e)?E(e):Array.isArray(e)?e.map(e=>t(e,i)||M.easeOut):M[e]}(a,r);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:n,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:n,times:r,ease:s});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(P(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eV(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:n,times:r,type:o,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return N(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return N(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=U(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return $;let{animation:i}=e;P(i,t)}else this.pendingTimeline=t;return $}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:n,type:r,ease:s,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ie({...u,keyframes:i,duration:n,type:r,ease:s,times:o,isGenerator:!0}),d=U(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ir()&&i&&ii.has(i)&&!a&&!l&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}}let ia={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ih={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},id=(t,{keyframes:e})=>e.length>2?iu:_.has(t)?t.startsWith("scale")?il(e[1]):ia:ih,ic=(t,e,i,n={},r,s)=>o=>{let a=g(n,t)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=U(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...id(t,h)}),h.duration&&(h.duration=U(h.duration)),h.repeatDelay&&(h.repeatDelay=U(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(d=!0)),(tp.current||H.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!s&&void 0!==e.get()){let t=eV(h.keyframes,a);if(void 0!==t)return G.update(()=>{h.onUpdate(t),h.onComplete()}),new v([])}return!s&&io.supports(h)?new io(h):new ie(h)};function ip(t,e,{delay:i=0,transitionOverride:n,type:r}={}){var s;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(o=n);let u=[],d=r&&t.animationState&&t.animationState.getState()[r];for(let e in l){let n=t.getValue(e,null!==(s=t.latestValues[e])&&void 0!==s?s:null),r=l[e];if(void 0===r||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let a={delay:i,...g(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tc];if(i){let t=window.MotionHandoffAnimation(i,e,G);null!==t&&(a.startTime=t,h=!0)}}th(t,e),n.start(ic(e,n,r,t.shouldReduceMotion&&z.has(e)?{type:!1}:a,t,h));let c=n.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{G.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=h(t,e)||{};for(let e in r={...r,...i}){let i=Y(r[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tl(i))}}(t,a)})}),u}function im(t,e,i={}){var n;let r=h(t,e,"exit"===i.type?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let o=r?()=>Promise.all(ip(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(iv).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(im(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+n,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function iv(t,e){return t.sortNodePosition(e)}let ig=c.length,iy=[...d].reverse(),ix=d.length;function iP(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iw(){return{animate:iP(!0),whileInView:iP(),whileHover:iP(),whileTap:iP(),whileDrag:iP(),whileFocus:iP(),exit:iP()}}class iT{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ib extends iT{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>im(t,e,i)));else if("string"==typeof e)n=im(t,e,i);else{let r="function"==typeof e?h(t,e,i.custom):e;n=Promise.all(ip(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iw(),n=!0,l=e=>(i,n)=>{var r;let s=h(t,n,"exit"===e?null===(r=t.presenceContext)||void 0===r?void 0:r.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function u(u){let{props:h}=t,d=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ig;t++){let n=c[t],r=e.props[n];(a(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<ix;e++){var g,y;let c=iy[e],x=i[c],P=void 0!==h[c]?h[c]:d[c],w=a(P),T=c===u?x.isActive:null;!1===T&&(v=e);let b=P===d[c]&&P!==h[c]&&w;if(b&&n&&t.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...f},!x.isActive&&null===T||!P&&!x.prevProp||r(P)||"boolean"==typeof P)continue;let S=(g=x.prevProp,"string"==typeof(y=P)?y!==g:!!Array.isArray(y)&&!o(y,g)),A=S||c===u&&x.isActive&&!b&&w||e>v&&w,E=!1,M=Array.isArray(P)?P:[P],V=M.reduce(l(c),{});!1===T&&(V={});let{prevResolvedValues:C={}}=x,D={...C,...V},R=e=>{A=!0,m.has(e)&&(E=!0,m.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=V[t],i=C[t];if(f.hasOwnProperty(t))continue;let n=!1;(s(e)&&s(i)?o(e,i):e===i)?void 0!==e&&m.has(t)?R(t):x.protectedKeys[t]=!0:null!=e?R(t):m.add(t)}x.prevProp=P,x.prevResolvedValues=V,x.isActive&&(f={...f,...V}),n&&t.blockInitialAnimation&&(A=!1);let k=!(b&&S)||E;A&&k&&p.push(...M.map(t=>({animation:t,options:{type:c}})))}if(m.size){let e={};m.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=null!=n?n:null}),p.push({animation:e})}let x=!!p.length;return n&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(x=!1),n=!1,x?e(p):Promise.resolve()}return{animateChanges:u,setActive:function(e,n){var r;if(i[e].isActive===n)return Promise.resolve();null===(r=t.variantChildren)||void 0===r||r.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,n)}),i[e].isActive=n;let s=u(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iw(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iS=0;class iA extends iT{constructor(){super(...arguments),this.id=iS++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function iE(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}function iM(t){return{point:{x:t.pageX,y:t.pageY}}}let iV=t=>e=>k(e)&&t(e,iM(e));function iC(t,e,i,n){return iE(t,e,iV(i),n)}let iD=(t,e)=>Math.abs(t-e);class iR{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iL(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iD(t.x,e.x)**2+iD(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=Q;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ik(e,this.transformPagePoint),G.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iL("pointercancel"===t.type?this.lastMoveEventInfo:ik(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!k(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=ik(iM(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=Q;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iL(s,this.history)),this.removeListeners=eU(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function ik(t,e){return e?{point:e(t.point)}:t}function ij(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iL({point:t},e){return{point:t,delta:ij(t,iF(e)),offset:ij(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iF(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>U(.1)));)i--;if(!n)return{x:0,y:0};let s=N(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iF(t){return t[t.length-1]}function iB(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iO(t){return t.max-t.min}function iI(t,e,i,n=.5){t.origin=n,t.originPoint=eD(e.min,e.max,t.origin),t.scale=iO(i)/iO(e),t.translate=eD(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iU(t,e,i,n){iI(t.x,e.x,i.x,n?n.originX:void 0),iI(t.y,e.y,i.y,n?n.originY:void 0)}function iN(t,e,i){t.min=i.min+e.min,t.max=t.min+iO(e)}function i$(t,e,i){t.min=e.min-i.min,t.max=t.min+iO(e)}function iW(t,e,i){i$(t.x,e.x,i.x),i$(t.y,e.y,i.y)}function i_(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iz(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function iK(t,e,i){return{min:iY(t,e),max:iY(t,i)}}function iY(t,e){return"number"==typeof t?t:t[e]||0}let iH=()=>({translate:0,scale:1,origin:0,originPoint:0}),iX=()=>({x:iH(),y:iH()}),iq=()=>({min:0,max:0}),iG=()=>({x:iq(),y:iq()});function iZ(t){return[t("x"),t("y")]}function iQ({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iJ(t){return void 0===t||1===t}function i0({scale:t,scaleX:e,scaleY:i}){return!iJ(t)||!iJ(e)||!iJ(i)}function i1(t){return i0(t)||i5(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i5(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i2(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function i9(t,e=0,i=1,n,r){t.min=i2(t.min,e,i,n,r),t.max=i2(t.max,e,i,n,r)}function i3(t,{x:e,y:i}){i9(t.x,e.translate,e.scale,e.originPoint),i9(t.y,i.translate,i.scale,i.originPoint)}function i8(t,e){t.min=t.min+e,t.max=t.max+e}function i4(t,e,i,n,r=.5){let s=eD(t.min,t.max,r);i9(t,e,i,s,n)}function i6(t,e){i4(t.x,e.x,e.scaleX,e.scale,e.originX),i4(t.y,e.y,e.scaleY,e.scale,e.originY)}function i7(t,e){return iQ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let nt=({current:t})=>t?t.ownerDocument.defaultView:null,ne=new WeakMap;class ni{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iG(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iR(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iM(t).point)},onStart:(t,e)=>{var i;let{drag:n,dragPropagation:r,onDragStart:s}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=n)||"y"===i?V[i]?null:(V[i]=!0,()=>{V[i]=!1}):V.x||V.y?null:(V.x=V.y=!0,()=>{V.x=V.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iZ(t=>{let e=this.getAxisMotionValue(t).get()||0;if(t$.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iO(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&G.postRender(()=>s(t,e)),th(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iZ(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:nt(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&G.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nn(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?eD(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?eD(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,r=this.constraints;e&&iB(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:i_(t.x,i,r),y:i_(t.y,e,n)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iK(t,"left","right"),y:iK(t,"top","bottom")}}(i),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&iZ(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iB(e))return!1;let n=e.current;$(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i7(t,i),{scroll:r}=e;return r&&(i8(n.x,r.offset.x),i8(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:iz((t=r.layout.layoutBox).x,s.x),y:iz(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iQ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iZ(o=>{if(!nn(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return th(this.visualElement,t),i.start(ic(t,i,0,e,this.visualElement,!1))}stopAnimation(){iZ(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iZ(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iZ(e=>{let{drag:i}=this.getProps();if(!nn(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-eD(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iB(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iZ(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iO(t),r=iO(e);return r>n?i=S(e.min,e.max-n,t.min):n>r&&(i=S(t.min,t.max-r,e.min)),tE(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iZ(e=>{if(!nn(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(eD(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;ne.set(this.visualElement,this);let t=iC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iB(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),G.read(e);let r=iE(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iZ(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function nn(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class nr extends iT{constructor(t){super(t),this.removeGroupControls=$,this.removeListeners=$,this.controls=new ni(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$}unmount(){this.removeGroupControls(),this.removeListeners()}}let ns=t=>(e,i)=>{t&&G.postRender(()=>t(e,i))};class no extends iT{constructor(){super(...arguments),this.removePointerDownListener=$}onPointerDown(t){this.session=new iR(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nt(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:ns(t),onStart:ns(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&G.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var na,nl,nu=i(95155),nh=i(12115),nd=i(32082),nc=i(90869);let np=(0,nh.createContext)({}),nm={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nf(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nv={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tW.test(t))return t;t=parseFloat(t)}let i=nf(t,e.target.x),n=nf(t,e.target.y);return`${i}% ${n}%`}},ng={},{schedule:ny,cancel:nx}=q(queueMicrotask,!1);class nP extends nh.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;Object.assign(ng,nT),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nm.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,s=i.projection;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||G.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ny.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nw(t){let[e,i]=(0,nd.xQ)(),n=(0,nh.useContext)(nc.L);return(0,nu.jsx)(nP,{...t,layoutGroup:n,switchLayoutGroup:(0,nh.useContext)(np),isPresent:e,safeToRemove:i})}let nT={borderRadius:{...nv,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nv,borderTopRightRadius:nv,borderBottomLeftRadius:nv,borderBottomRightRadius:nv,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=t5.parse(t);if(n.length>5)return t;let r=t5.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=eD(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}},nb=(t,e)=>t.depth-e.depth;class nS{constructor(){this.children=[],this.isDirty=!1}add(t){ti(this.children,t),this.isDirty=!0}remove(t){tn(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nb),this.isDirty=!1,this.children.forEach(t)}}function nA(t){let e=tu(t)?t.get():t;return K(e)?e.toValue():e}let nE=["TopLeft","TopRight","BottomLeft","BottomRight"],nM=nE.length,nV=t=>"string"==typeof t?parseFloat(t):t,nC=t=>"number"==typeof t||tW.test(t);function nD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nR=nj(0,.5,tb),nk=nj(.5,.95,$);function nj(t,e,i){return n=>n<t?0:n>e?1:i(S(t,e,n))}function nL(t,e){t.min=e.min,t.max=e.max}function nF(t,e){nL(t.x,e.x),nL(t.y,e.y)}function nB(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nO(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nI(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(t$.test(e)&&(e=parseFloat(e),e=eD(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eD(s.min,s.max,n);t===s&&(a-=e),t.min=nO(t.min,e,i,a,r),t.max=nO(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nU=["x","scaleX","originX"],nN=["y","scaleY","originY"];function n$(t,e,i,n){nI(t.x,e,nU,i?i.x:void 0,n?n.x:void 0),nI(t.y,e,nN,i?i.y:void 0,n?n.y:void 0)}function nW(t){return 0===t.translate&&1===t.scale}function n_(t){return nW(t.x)&&nW(t.y)}function nz(t,e){return t.min===e.min&&t.max===e.max}function nK(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nY(t,e){return nK(t.x,e.x)&&nK(t.y,e.y)}function nH(t){return iO(t.x)/iO(t.y)}function nX(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nq{constructor(){this.members=[]}add(t){ti(this.members,t),t.scheduleRender()}remove(t){if(tn(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nG={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},nZ="undefined"!=typeof window&&void 0!==window.MotionDebug,nQ=["","X","Y","Z"],nJ={visibility:"hidden"},n0=0;function n1(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n5({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=null==e?void 0:e()){this.id=n0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nZ&&(nG.totalNodes=nG.resolvedTargetDeltas=nG.recalculatedProjection=0),this.nodes.forEach(n3),this.nodes.forEach(ri),this.nodes.forEach(rn),this.nodes.forEach(n8),nZ&&window.MotionDebug.record(nG)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nS)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tr),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(r||n)&&(this.isLayoutDirty=!0),t){let i;let n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=te.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(Z(n),t(s-e))};return G.read(n,!0),()=>Z(n)}(n,250),nm.hasAnimatedSinceResize&&(nm.hasAnimatedSinceResize=!1,this.nodes.forEach(re))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||ru,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!nY(this.targetLayout,n)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...g(r,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||re(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rr),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[tc];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",G,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n6);return}this.isUpdating||this.nodes.forEach(n7),this.isUpdating=!1,this.nodes.forEach(rt),this.nodes.forEach(n2),this.nodes.forEach(n9),this.clearAllSnapshots();let t=te.now();Q.delta=tE(0,1e3/60,t-Q.timestamp),Q.timestamp=t,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ny.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n4),this.sharedNodes.forEach(rs)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iG(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!n_(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&(e||i1(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),rc((e=n).x),rc(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iG();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(rm))){let{scroll:t}=this.root;t&&(i8(i.x,t.offset.x),i8(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iG();if(nF(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nF(i,t),i8(i.x,r.offset.x),i8(i.y,r.offset.y))}return i}applyTransform(t,e=!1){let i=iG();nF(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i6(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i1(n.latestValues)&&i6(i,n.latestValues)}return i1(this.latestValues)&&i6(i,this.latestValues),i}removeTransform(t){let e=iG();nF(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let n=iG();nF(n,i.measurePageBox()),n$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return i1(this.latestValues)&&n$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,n,r;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iG(),this.relativeTargetOrigin=iG(),iW(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iG(),this.targetWithTransforms=iG()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,n=this.relativeTarget,r=this.relativeParent.target,iN(i.x,n.x,r.x),iN(i.y,n.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nF(this.target,this.layout.layoutBox),i3(this.target,this.targetDelta)):nF(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iG(),this.relativeTargetOrigin=iG(),iW(this.relativeTargetOrigin,this.target,t.target),nF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nZ&&nG.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||i0(this.parent.latestValues)||i5(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(n=!1),n)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;nF(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i6(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,i3(t,s)),n&&i1(r.latestValues)&&i6(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iG());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nB(this.prevProjectionDelta.x,this.projectionDelta.x),nB(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iU(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&nX(this.projectionDelta.x,this.prevProjectionDelta.x)&&nX(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nZ&&nG.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iX(),this.projectionDelta=iX(),this.projectionDeltaWithTransform=iX()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iX();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iG(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rl));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(ro(o.x,t.x,n),ro(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;if(iW(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=n,ra(p.x,m.x,f.x,v),ra(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,nz(u.x,c.x)&&nz(u.y,c.y)))this.isProjectionDirty=!1;i||(i=iG()),nF(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=eD(0,void 0!==i.opacity?i.opacity:1,nR(n)),t.opacityExit=eD(void 0!==e.opacity?e.opacity:1,0,nk(n))):s&&(t.opacity=eD(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,n));for(let r=0;r<nM;r++){let s=`border${nE[r]}Radius`,o=nD(e,s),a=nD(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nC(o)===nC(a)?(t[s]=Math.max(eD(nV(o),nV(a),n),0),(t$.test(a)||t$.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=eD(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.update(()=>{nm.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let n=tu(0)?0:tl(t);return n.start(ic("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&rp(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iG();let e=iO(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iO(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nF(e,i),i6(e,r),iU(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nq),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n1("z",t,n,this.animationValues);for(let e=0;e<nQ.length;e++)n1(`rotate${nQ[e]}`,t,n,this.animationValues),n1(`skew${nQ[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nJ;let n={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nA(null==t?void 0:t.pointerEvents)||"",n.transform=r?r(this.latestValues,""):"none",n;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nA(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(e.transform=r?r({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),n.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),r&&(n.transform=r(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let t in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?n.opacity=s===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,ng){if(void 0===o[t])continue;let{correct:e,applyTo:i}=ng[t],r="none"===n.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)n[i[e]]=r}else n[t]=r}return this.options.layoutId&&(n.pointerEvents=s===this?nA(null==t?void 0:t.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(n6),this.root.sharedNodes.clear()}}}function n2(t){t.updateLayout()}function n9(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:r}=t.options,s=i.source!==t.layout.source;"size"===r?iZ(t=>{let n=s?i.measuredBox[t]:i.layoutBox[t],r=iO(n);n.min=e[t].min,n.max=n.min+r}):rp(r,i.layoutBox,e)&&iZ(n=>{let r=s?i.measuredBox[n]:i.layoutBox[n],o=iO(e[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iX();iU(o,e,i.layoutBox);let a=iX();s?iU(a,t.applyTransform(n,!0),i.measuredBox):iU(a,e,i.layoutBox);let l=!n_(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iG();iW(o,i.layoutBox,r.layoutBox);let a=iG();iW(a,e,s.layoutBox),nY(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n3(t){nZ&&nG.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n8(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n4(t){t.clearSnapshot()}function n6(t){t.clearMeasurements()}function n7(t){t.isLayoutDirty=!1}function rt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function re(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ri(t){t.resolveTargetDelta()}function rn(t){t.calcProjection()}function rr(t){t.resetSkewAndRotation()}function rs(t){t.removeLeadSnapshot()}function ro(t,e,i){t.translate=eD(e.translate,0,i),t.scale=eD(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function ra(t,e,i,n){t.min=eD(e.min,i.min,n),t.max=eD(e.max,i.max,n)}function rl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ru={duration:.45,ease:[.4,0,.1,1]},rh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rd=rh("applewebkit/")&&!rh("chrome/")?Math.round:$;function rc(t){t.min=rd(t.min),t.max=rd(t.max)}function rp(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nH(e)-nH(i)))}function rm(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let rf=n5({attachResizeListener:(t,e)=>iE(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rv={current:void 0},rg=n5({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rv.current){let t=new rf({});t.mount(window),t.setOptions({layoutScroll:!0}),rv.current=t}return rv.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ry(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&G.postRender(()=>r(e,iM(e)))}class rx extends iT{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=C(t,i),o=D(t=>{let{target:i}=t,n=e(t);if("function"!=typeof n||!i)return;let s=D(t=>{n(t),i.removeEventListener("pointerleave",s)});i.addEventListener("pointerleave",s,r)});return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,t=>(ry(this.node,t,"Start"),t=>ry(this.node,t,"End"))))}unmount(){}}class rP extends iT{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eU(iE(this.node.current,"focus",()=>this.onFocus()),iE(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function rw(t,e,i){let{props:n}=t;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&G.postRender(()=>r(e,iM(e)))}class rT extends iT{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=C(t,i),o=t=>{let n=t.currentTarget;if(!I(t)||L.has(n))return;L.add(n);let s=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),I(t)&&L.has(n)&&(L.delete(n),"function"==typeof s&&s(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||R(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{!j.has(t.tagName)&&-1===t.tabIndex&&null===t.getAttribute("tabindex")&&(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),t.addEventListener("focus",t=>O(t,r),r)}),s}(t,t=>(rw(this.node,t,"Start"),(t,{success:e})=>rw(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rb=new WeakMap,rS=new WeakMap,rA=t=>{let e=rb.get(t.target);e&&e(t)},rE=t=>{t.forEach(rA)},rM={some:0,all:1};class rV extends iT{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rM[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rS.has(i)||rS.set(i,{});let n=rS.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rE,{root:t,...e})),n[r]}(e);return rb.set(t,i),n.observe(t),()=>{rb.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rC=(0,nh.createContext)({strict:!1});var rD=i(51508);let rR=(0,nh.createContext)({});function rk(t){return r(t.animate)||c.some(e=>a(t[e]))}function rj(t){return!!(rk(t)||t.variants)}function rL(t){return Array.isArray(t)?t.join(" "):t}var rF=i(68972);let rB={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rO={};for(let t in rB)rO[t]={isEnabled:e=>rB[t].some(t=>!!e[t])};let rI=Symbol.for("motionComponentSymbol");var rU=i(80845),rN=i(97494);let r$=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rW(t){if("string"!=typeof t||t.includes("-"));else if(r$.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r_=i(82885);let rz=t=>(e,i)=>{let n=(0,nh.useContext)(rR),s=(0,nh.useContext)(rU.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,s,o){let a={latestValues:function(t,e,i,n){let s={},o=n(t,{});for(let t in o)s[t]=nA(o[t]);let{initial:a,animate:l}=t,h=rk(t),d=rj(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=u(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(n,s,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:n,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,n,s);return i?o():(0,r_.M)(o)},rK=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rY={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rH=W.length;function rX(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(_.has(t)){o=!0;continue}if(eg(t)){r[t]=i;continue}{let e=rK(i,t6[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rH;s++){let o=W[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rK(a,t6[o]);if(!l){r=!1;let e=rY[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rq={offset:"stroke-dashoffset",array:"stroke-dasharray"},rG={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(t,e,i){return"string"==typeof t?t:tW.transform(e+i*t)}function rQ(t,{attrX:e,attrY:i,attrScale:n,originX:r,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(rX(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==r||void 0!==s||p.transform)&&(p.transformOrigin=function(t,e,i){let n=rZ(e,t.x,t.width),r=rZ(i,t.y,t.height);return`${n} ${r}`}(m,void 0!==r?r:.5,void 0!==s?s:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==o&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rq:rG;t[s.offset]=tW.transform(-n);let o=tW.transform(e),a=tW.transform(i);t[s.array]=`${o} ${a}`}(c,o,a,l,!1)}let rJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),r0=()=>({...rJ(),attrs:{}}),r1=t=>"string"==typeof t&&"svg"===t.toLowerCase();function r5(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}let r2=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function r9(t,e,i,n){for(let i in r5(t,e,void 0,n),e.attrs)t.setAttribute(r2.has(i)?i:td(i),e.attrs[i])}function r3(t,{layout:e,layoutId:i}){return _.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ng[t]||"opacity"===t)}function r8(t,e,i){var n;let{style:r}=t,s={};for(let o in r)(tu(r[o])||e.style&&tu(e.style[o])||r3(o,t)||(null===(n=null==i?void 0:i.getValue(o))||void 0===n?void 0:n.liveStyle)!==void 0)&&(s[o]=r[o]);return s}function r4(t,e,i){let n=r8(t,e,i);for(let i in t)(tu(t[i])||tu(e[i]))&&(n[-1!==W.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r6=["x","y","width","height","cx","cy","r"],r7={useVisualState:rz({scrapeMotionValuesFromProps:r4,createRenderState:r0,onUpdate:({props:t,prevProps:e,current:i,renderState:n,latestValues:r})=>{if(!i)return;let s=!!t.drag;if(!s){for(let t in r)if(_.has(t)){s=!0;break}}if(!s)return;let o=!e;if(e)for(let i=0;i<r6.length;i++){let n=r6[i];t[n]!==e[n]&&(o=!0)}o&&G.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,n),G.render(()=>{rQ(n,r,r1(i.tagName),t.transformTemplate),r9(i,n)})})}})},st={useVisualState:rz({scrapeMotionValuesFromProps:r8,createRenderState:rJ})};function se(t,e,i){for(let n in e)tu(e[n])||r3(n,i)||(t[n]=e[n])}let si=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function sn(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||si.has(t)}let sr=t=>!sn(t);try{!function(t){t&&(sr=e=>e.startsWith("on")?!sn(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let ss={current:null},so={current:!1},sa=[...eb,tH,t5],sl=t=>sa.find(eT(t)),su=new WeakMap,sh=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sd{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=em,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,G.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rk(e),this.isVariantNode=rj(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,su.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),so.current||function(){if(so.current=!0,rF.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ss.current=t.matches;t.addListener(e),e()}else ss.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ss.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in su.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=_.has(t),r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&G.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rO){let e=rO[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iG()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sh.length;e++){let i=sh[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(tu(r))t.addValue(n,r);else if(tu(s))t.addValue(n,tl(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,tl(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tl(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(ef(n)||tA(n))?n=parseFloat(n):!sl(n)&&t5.test(e)&&(n=ee(t,e)),this.setBaseTarget(t,tu(n)?n.get():n)),tu(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=u(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);r&&(i=r[t])}if(n&&void 0!==i)return i;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tu(r)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new tr),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sc extends sd{constructor(){super(...arguments),this.KeyframeResolver=eA}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class sp extends sc{constructor(){super(...arguments),this.type="html",this.renderInstance=r5}readValueFromInstance(t,e){if(_.has(e)){let t=et(e);return t&&t.default||0}{let i=window.getComputedStyle(t),n=(eg(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i7(t,e)}build(t,e,i){rX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r8(t,e,i)}}class sm extends sc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iG}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(_.has(e)){let t=et(e);return t&&t.default||0}return e=r2.has(e)?e:td(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r4(t,e,i)}build(t,e,i){rQ(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){r9(t,e,i,n)}mount(t){this.isSVGTag=r1(t.tagName),super.mount(t)}}let sf=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((na={animation:{Feature:ib},exit:{Feature:iA},inView:{Feature:rV},tap:{Feature:rT},focus:{Feature:rP},hover:{Feature:rx},pan:{Feature:no},drag:{Feature:nr,ProjectionNode:rg,MeasureLayout:nw},layout:{ProjectionNode:rg,MeasureLayout:nw}},nl=(t,e)=>rW(t)?new sm(e):new sp(e,{allowProjection:t!==nh.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:l}=t;function u(t,e){var i,n,u;let h;let d={...(0,nh.useContext)(rD.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nh.useContext)(nc.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(rk(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nh.useContext)(rR));return(0,nh.useMemo)(()=>({initial:e,animate:i}),[rL(e),rL(i)])}(t),m=o(t,c);if(!c&&rF.B){n=0,u=0,(0,nh.useContext)(rC).strict;let t=function(t){let{drag:e,layout:i}=rO;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);h=t.MeasureLayout,p.visualElement=function(t,e,i,n,r){var s,o;let{visualElement:a}=(0,nh.useContext)(rR),l=(0,nh.useContext)(rC),u=(0,nh.useContext)(rU.t),h=(0,nh.useContext)(rD.Q).reducedMotion,d=(0,nh.useRef)(null);n=n||l.renderer,!d.current&&n&&(d.current=n(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,nh.useContext)(np);c&&!c.projection&&r&&("html"===c.type||"svg"===c.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iB(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(d.current,i,r,p);let m=(0,nh.useRef)(!1);(0,nh.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tc],v=(0,nh.useRef)(!!f&&!(null===(s=window.MotionHandoffIsComplete)||void 0===s?void 0:s.call(window,f))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,f)));return(0,rN.E)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),ny.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,nh.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,f)}),v.current=!1))}),c}(l,m,d,r,t.ProjectionNode)}return(0,nu.jsxs)(rR.Provider,{value:p,children:[h&&p.visualElement?(0,nu.jsx)(h,{visualElement:p.visualElement,...d}):null,s(l,t,(i=p.visualElement,(0,nh.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iB(e)&&(e.current=t))},[i])),m,c,p.visualElement)]})}n&&function(t){for(let e in t)rO[e]={...rO[e],...t[e]}}(n),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!==(i=null!==(e=l.displayName)&&void 0!==e?e:l.name)&&void 0!==i?i:"",")"));let h=(0,nh.forwardRef)(u);return h[rI]=l,h}({...rW(t)?r7:st,preloadedFeatures:na,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(rW(e)?function(t,e,i,n){let r=(0,nh.useMemo)(()=>{let i=r0();return rQ(i,e,r1(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};se(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return se(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nh.useMemo)(()=>{let i=rJ();return rX(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(sr(r)||!0===i&&sn(r)||!e&&!sn(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nh.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nh.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,nh.createElement)(e,{...l,children:h})}}(e),createVisualElement:nl,Component:t})}))},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(12115);function r(t,e){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=r.current;e&&(r.current=null,e())}else t&&(i.current=s(t,n)),e&&(r.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return m}});let n=i(88229),r=i(95155),s=n._(i(12115)),o=i(82757),a=i(95227),l=i(69818),u=i(6654),h=i(69991),d=i(85929);i(43230);let c=i(24930);function p(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}let m=s.default.forwardRef(function(t,e){let i,n;let{href:o,as:m,children:f,prefetch:v=null,passHref:g,replace:y,shallow:x,scroll:P,onClick:w,onMouseEnter:T,onTouchStart:b,legacyBehavior:S=!1,...A}=t;i=f,S&&("string"==typeof i||"number"==typeof i)&&(i=(0,r.jsx)("a",{children:i}));let E=s.default.useContext(a.AppRouterContext),M=!1!==v,V=null===v?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:C,as:D}=s.default.useMemo(()=>{let t=p(o);return{href:t,as:m?p(m):t}},[o,m]);S&&(n=s.default.Children.only(i));let R=S?n&&"object"==typeof n&&n.ref:e,k=s.default.useCallback(t=>(M&&null!==E&&(0,c.mountLinkInstance)(t,C,E,V),()=>{(0,c.unmountLinkInstance)(t)}),[M,C,E,V]),j={ref:(0,u.useMergedRef)(k,R),onClick(t){S||"function"!=typeof w||w(t),S&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(t),E&&!t.defaultPrevented&&!function(t,e,i,n,r,o,a){let{nodeName:l}=t.currentTarget;!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t))&&(t.preventDefault(),s.default.startTransition(()=>{let t=null==a||a;"beforePopState"in e?e[r?"replace":"push"](i,n,{shallow:o,scroll:t}):e[r?"replace":"push"](n||i,{scroll:t})}))}(t,E,C,D,y,x,P)},onMouseEnter(t){S||"function"!=typeof T||T(t),S&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(t),E&&M&&(0,c.onNavigationIntent)(t.currentTarget)},onTouchStart:function(t){S||"function"!=typeof b||b(t),S&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(t),E&&M&&(0,c.onNavigationIntent)(t.currentTarget)}};return(0,h.isAbsoluteUrl)(D)?j.href=D:S&&!g&&("a"!==n.type||"href"in n.props)||(j.href=(0,d.addBasePath)(D)),S?s.default.cloneElement(n,j):(0,r.jsx)("a",{...A,...j,children:i})});("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},32082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(12115),r=i(80845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{t&&a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},51508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},68972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},69991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return v},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class v extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class g extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}},78859:(t,e)=>{function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},80845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(12115).createContext)(null)},82757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=i(6966)._(i(78859)),r=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||r.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},82885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(12115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},90869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(12115).createContext)({})},97494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(12115);let r=i(68972).B?n.useLayoutEffect:n.useEffect}}]);