{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["\nimport { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// This file is modified to export a default function to satisfy Next.js.\n// As per the original comments, Firebase Studio handles route protection client-side.\n// This middleware function is minimal and does not implement custom routing logic.\n\nexport default function middleware(request: NextRequest) {\n  // Client-side checks in AuthProvider and page.tsx handle route protection.\n  // This middleware passes the request through.\n  return NextResponse.next();\n}\n\n// The original file contained comments about not using middleware in Firebase Studio\n// and an example of what standard middleware might look like.\n// Those comments have been preserved below for context, but the active code\n// is just the minimal middleware function above.\n\n// Original comments for context:\n// Firebase Studio currently does not support middleware.ts directly.\n// Route protection is handled client-side in the page components (e.g., src/app/page.tsx)\n// using the AuthContext and useRouter from next/navigation.\n//\n// If you were to implement middleware for route protection in a standard Next.js setup,\n// it might look something like this (DO NOT USE THIS IN FIREBASE STUDIO):\n/*\nimport { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\n// This is the actual middleware function signature Next.js expects\nexport function exampleMiddleware(request: NextRequest) {\n  const currentUserCookie = request.cookies.get('firebaseAuthToken'); // Example cookie name\n\n  const protectedRoutes = ['/']; // Add any other routes that need protection\n  const authRoutes = ['/login', '/register'];\n\n  const { pathname } = request.nextUrl;\n\n  if (protectedRoutes.includes(pathname) && !currentUserCookie) {\n    return NextResponse.redirect(new URL('/login', request.url));\n  }\n\n  if (authRoutes.includes(pathname) && currentUserCookie) {\n    return NextResponse.redirect(new URL('/', request.url));\n  }\n\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: ['/', '/login', '/register'], // Apply middleware to these paths\n};\n*/\n\n// For Firebase Studio, client-side checks are the way to go for now.\n// The AuthProvider and useAuth hook along with useRouter in page.tsx handle this.\n\n// The original file ended with `export {};` which caused the error.\n// It's now replaced by the default export above.\n"], "names": [], "mappings": ";;;AACA;AAAA;;AAOe,SAAS,WAAW,OAAoB;IACrD,2EAA2E;IAC3E,8CAA8C;IAC9C,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,EAEA,qFAAqF;CACrF,8DAA8D;CAC9D,4EAA4E;CAC5E,iDAAiD;CAEjD,iCAAiC;CACjC,qEAAqE;CACrE,0FAA0F;CAC1F,4DAA4D;CAC5D,EAAE;CACF,wFAAwF;CACxF,0EAA0E;CAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAEA,qEAAqE;CACrE,kFAAkF;CAElF,oEAAoE;CACpE,iDAAiD"}}]}