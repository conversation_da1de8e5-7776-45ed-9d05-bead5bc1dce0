'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { Trophy, Star, Target, PiggyBank, TrendingUp, Award, CheckCircle2, Zap } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: 'trophy' | 'star' | 'target' | 'piggybank' | 'trending' | 'award' | 'check' | 'zap';
  type: 'budget' | 'savings' | 'goal' | 'streak' | 'milestone';
  threshold: number;
  currentValue: number;
  isUnlocked: boolean;
  unlockedAt?: Date;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

const iconMap = {
  trophy: Trophy,
  star: Star,
  target: Target,
  piggybank: PiggyBank,
  trending: TrendingUp,
  award: Award,
  check: CheckCircle2,
  zap: Zap,
};

const rarityColors = {
  common: 'from-gray-400 to-gray-600',
  rare: 'from-blue-400 to-blue-600',
  epic: 'from-purple-400 to-purple-600',
  legendary: 'from-yellow-400 to-yellow-600',
};

const rarityGlow = {
  common: 'shadow-gray-400/20',
  rare: 'shadow-blue-400/30',
  epic: 'shadow-purple-400/30',
  legendary: 'shadow-yellow-400/40',
};

interface AchievementBadgeProps {
  achievement: Achievement;
  size?: 'sm' | 'md' | 'lg';
  showProgress?: boolean;
}

export function AchievementBadge({ achievement, size = 'md', showProgress = false }: AchievementBadgeProps) {
  const Icon = iconMap[achievement.icon];
  const progress = Math.min((achievement.currentValue / achievement.threshold) * 100, 100);
  
  const sizeClasses = {
    sm: 'w-12 h-12 text-xs',
    md: 'w-16 h-16 text-sm',
    lg: 'w-20 h-20 text-base',
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <motion.div
      className="relative"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <div
        className={`
          ${sizeClasses[size]} 
          rounded-full 
          bg-gradient-to-br ${rarityColors[achievement.rarity]}
          ${achievement.isUnlocked ? `shadow-lg ${rarityGlow[achievement.rarity]}` : 'opacity-50 grayscale'}
          flex items-center justify-center
          border-2 border-white/20
          relative overflow-hidden
        `}
      >
        <Icon className={`${iconSizes[size]} text-white`} />
        
        {achievement.isUnlocked && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            initial={{ x: '-100%' }}
            animate={{ x: '100%' }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 3,
              ease: 'easeInOut',
            }}
          />
        )}
      </div>
      
      {showProgress && !achievement.isUnlocked && (
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-full">
          <div className="bg-muted rounded-full h-1 overflow-hidden">
            <motion.div
              className="h-full bg-primary rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 1, ease: 'easeOut' }}
            />
          </div>
        </div>
      )}
    </motion.div>
  );
}

interface AchievementCardProps {
  achievement: Achievement;
  onClaim?: () => void;
}

export function AchievementCard({ achievement, onClaim }: AchievementCardProps) {
  const progress = Math.min((achievement.currentValue / achievement.threshold) * 100, 100);
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className={`relative overflow-hidden ${achievement.isUnlocked ? 'border-primary/50' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <AchievementBadge achievement={achievement} size="md" />
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-sm truncate">{achievement.title}</h3>
                <Badge variant={achievement.isUnlocked ? 'default' : 'secondary'} className="text-xs">
                  {achievement.rarity}
                </Badge>
              </div>
              
              <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                {achievement.description}
              </p>
              
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Progress</span>
                  <span>{achievement.currentValue} / {achievement.threshold}</span>
                </div>
                
                <div className="bg-muted rounded-full h-2 overflow-hidden">
                  <motion.div
                    className={`h-full rounded-full ${
                      achievement.isUnlocked 
                        ? 'bg-gradient-to-r from-success to-success/80' 
                        : 'bg-gradient-to-r from-primary to-primary/80'
                    }`}
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 1, ease: 'easeOut' }}
                  />
                </div>
              </div>
              
              {achievement.isUnlocked && achievement.unlockedAt && (
                <p className="text-xs text-success mt-2">
                  Unlocked {achievement.unlockedAt.toLocaleDateString()}
                </p>
              )}
            </div>
          </div>
        </CardContent>
        
        {achievement.isUnlocked && (
          <motion.div
            className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-success/20 to-transparent"
            initial={{ scale: 0, rotate: -45 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <CheckCircle2 className="absolute top-1 right-1 h-4 w-4 text-success" />
          </motion.div>
        )}
      </Card>
    </motion.div>
  );
}

interface AchievementNotificationProps {
  achievement: Achievement;
  isVisible: boolean;
  onClose: () => void;
}

export function AchievementNotification({ achievement, isVisible, onClose }: AchievementNotificationProps) {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(onClose, 5000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed top-4 right-4 z-50 max-w-sm"
          initial={{ opacity: 0, x: 300, scale: 0.8 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 300, scale: 0.8 }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        >
          <Card className="border-primary/50 bg-gradient-to-r from-card to-card/80 backdrop-blur-sm shadow-xl">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 0.5, repeat: 2 }}
                >
                  <AchievementBadge achievement={achievement} size="md" />
                </motion.div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Trophy className="h-4 w-4 text-primary" />
                    <span className="text-sm font-semibold text-primary">Achievement Unlocked!</span>
                  </div>
                  <h3 className="font-semibold text-sm">{achievement.title}</h3>
                  <p className="text-xs text-muted-foreground">{achievement.description}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Confetti effect */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-primary rounded-full"
                initial={{
                  x: Math.random() * 100,
                  y: 0,
                  opacity: 1,
                }}
                animate={{
                  y: -100,
                  x: Math.random() * 200 - 100,
                  opacity: 0,
                  rotate: 360,
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.1,
                  ease: 'easeOut',
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
