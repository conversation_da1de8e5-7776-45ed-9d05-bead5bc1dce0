"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[321],{44321:(e,a,s)=>{s.d(a,{default:()=>H});var r=s(95155),t=s(6874),i=s.n(t),l=s(1978),n=s(519),o=s(17607),d=s(4607),c=s(50594),m=s(18271),p=s(33349),h=s(17104),f=s(13896),x=s(30285),u=s(90925),g=s(12115),N=s(9449),y=s(73158),j=s(10518),v=s(70154),w=s(59434);let b=N.bL,P=N.l9;N.YJ,N.ZL,N.Pb,N.z6,g.forwardRef((e,a)=>{let{className:s,inset:t,children:i,...l}=e;return(0,r.jsxs)(N.ZP,{ref:a,className:(0,w.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",s),...l,children:[i,(0,r.jsx)(y.A,{className:"ml-auto"})]})}).displayName=N.ZP.displayName,g.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(N.G5,{ref:a,className:(0,w.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...t})}).displayName=N.G5.displayName;let z=g.forwardRef((e,a)=>{let{className:s,sideOffset:t=4,...i}=e;return(0,r.jsx)(N.ZL,{children:(0,r.jsx)(N.UC,{ref:a,sideOffset:t,className:(0,w.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})})});z.displayName=N.UC.displayName;let C=g.forwardRef((e,a)=>{let{className:s,inset:t,...i}=e;return(0,r.jsx)(N.q7,{ref:a,className:(0,w.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",t&&"pl-8",s),...i})});C.displayName=N.q7.displayName,g.forwardRef((e,a)=>{let{className:s,children:t,checked:i,...l}=e;return(0,r.jsxs)(N.H_,{ref:a,className:(0,w.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:i,...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(N.VF,{children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})}),t]})}).displayName=N.H_.displayName,g.forwardRef((e,a)=>{let{className:s,children:t,...i}=e;return(0,r.jsxs)(N.hN,{ref:a,className:(0,w.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(N.VF,{children:(0,r.jsx)(v.A,{className:"h-2 w-2 fill-current"})})}),t]})}).displayName=N.hN.displayName;let _=g.forwardRef((e,a)=>{let{className:s,inset:t,...i}=e;return(0,r.jsx)(N.JU,{ref:a,className:(0,w.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",s),...i})});_.displayName=N.JU.displayName;let A=g.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(N.wv,{ref:a,className:(0,w.cn)("-mx-1 my-1 h-px bg-muted",s),...t})});A.displayName=N.wv.displayName;var k=s(91394);function H(e){var a;let{title:s,balancesVisible:t,onToggleBalances:g}=e,{currentUser:N,signOut:y,loading:j}=(0,u.A)();return(0,r.jsx)(l.P.header,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,ease:"easeOut"},className:"bg-card/95 backdrop-blur-md shadow-sm sticky top-0 z-40 border-b border-border/50",children:(0,r.jsxs)("div",{className:"container mx-auto flex items-center justify-between py-3 px-4 sm:px-6",children:[(0,r.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center gap-2 group",children:[(0,r.jsx)(l.P.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,repeatDelay:5},className:"p-1 rounded-full bg-gradient-to-r from-primary/20 to-primary/10 group-hover:from-primary/30 group-hover:to-primary/20 transition-all duration-300",children:(0,r.jsx)(n.A,{className:"h-7 w-7 text-primary"})}),(0,r.jsx)("h1",{className:"text-xl sm:text-2xl font-headline font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",children:s})]})}),(0,r.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[g&&(0,r.jsx)(l.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsxs)(x.$,{variant:"ghost",size:"icon",onClick:g,className:"text-foreground hover:text-foreground/80 h-8 w-8 sm:h-9 sm:w-9 hover:bg-primary/10 transition-all duration-200",children:[(0,r.jsx)(l.P.div,{initial:!1,animate:{rotate:180*!t},transition:{duration:.3},children:t?(0,r.jsx)(o.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}):(0,r.jsx)(d.A,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),(0,r.jsx)("span",{className:"sr-only",children:t?"Hide Balances":"Show Balances"})]})}),(0,r.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)(i(),{href:"/tips",className:"text-sm sm:text-base text-primary hover:text-primary/80 transition-all duration-200 hidden sm:flex items-center gap-1 px-3 py-2 rounded-md hover:bg-primary/10",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"Budgeting Tips"]})}),(0,r.jsx)(l.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"sm:hidden",children:(0,r.jsx)(x.$,{variant:"ghost",size:"icon",asChild:!0,className:"text-primary hover:text-primary/80 h-8 w-8 hover:bg-primary/10",children:(0,r.jsxs)(i(),{href:"/tips",children:[(0,r.jsx)(c.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Budgeting Tips"})]})})}),j?(0,r.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},className:"h-8 w-20 bg-muted rounded animate-pulse"}):N?(0,r.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.2},children:(0,r.jsxs)(b,{children:[(0,r.jsx)(P,{asChild:!0,children:(0,r.jsx)(l.P.div,{whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(x.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full hover:bg-primary/10 transition-all duration-200",children:(0,r.jsxs)(k.eu,{className:"h-8 w-8 ring-2 ring-primary/20 hover:ring-primary/40 transition-all duration-200",children:[(0,r.jsx)(k.BK,{src:N.photoURL||void 0,alt:N.displayName||N.email||"User"}),(0,r.jsx)(k.q5,{className:"bg-gradient-to-r from-primary/20 to-accent/20 text-foreground font-semibold",children:((e,a)=>{if(a){let e=a.split(" ").filter(Boolean);return e.length>1?(e[0][0]+e[1][0]).toUpperCase():a.substring(0,2).toUpperCase()}return e?e.substring(0,2).toUpperCase():"U"})(N.email,N.displayName)})]})})})}),(0,r.jsxs)(z,{className:"w-56 glass",align:"end",forceMount:!0,children:[(0,r.jsx)(_,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none",children:N.displayName||(null===(a=N.email)||void 0===a?void 0:a.split("@")[0])}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:N.email})]})}),(0,r.jsx)(A,{}),(0,r.jsx)(C,{asChild:!0,className:"cursor-pointer hover:bg-primary/10 transition-colors",children:(0,r.jsxs)(i(),{href:"/profile",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile Settings"})]})}),(0,r.jsx)(A,{}),(0,r.jsxs)(C,{onClick:y,className:"cursor-pointer hover:bg-destructive/10 text-destructive transition-colors",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]})}):(0,r.jsxs)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.3},className:"flex items-center gap-2",children:[(0,r.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(x.$,{variant:"ghost",asChild:!0,size:"sm",className:"hover:bg-primary/10",children:(0,r.jsxs)(i(),{href:"/login",children:[(0,r.jsx)(h.A,{className:"mr-1 h-4 w-4 sm:mr-2"})," Login"]})})}),(0,r.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(x.$,{asChild:!0,size:"sm",className:"bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70",children:(0,r.jsxs)(i(),{href:"/register",children:[(0,r.jsx)(f.A,{className:"mr-1 h-4 w-4 sm:mr-2"})," Sign Up"]})})})]})]})]})})}},91394:(e,a,s)=>{s.d(a,{BK:()=>o,eu:()=>n,q5:()=>d});var r=s(95155),t=s(12115),i=s(85977),l=s(59434);let n=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(i.bL,{ref:a,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...t})});n.displayName=i.bL.displayName;let o=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(i._V,{ref:a,className:(0,l.cn)("aspect-square h-full w-full",s),...t})});o.displayName=i._V.displayName;let d=t.forwardRef((e,a)=>{let{className:s,...t}=e;return(0,r.jsx)(i.H4,{ref:a,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...t})});d.displayName=i.H4.displayName}}]);